# mcp_runner.py

import subprocess
import threading
import os

def run_mcp_in_background(port: int = 8937):
    """
    Runs Playwright MCP in a separate background thread on the given port.
    Returns the subprocess.Popen object.
    """
    script_path = os.path.join(os.path.dirname(__file__), "..", "Grab-Url", "profile")
    script_path = os.path.abspath(script_path)  # Optional: convert to absolute path
    print("script:", script_path)
    def target():
        
            print(f"🚀[MCP:{port}] Starting...")
            cmd = f'npx @playwright/mcp@latest --user-data-dir "{script_path}" --port {port} --reload=False'
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True,
                universal_newlines=True
            )
            print(f"✅[MCP:{port}] Started (pid={process.pid})")

            # Optional: log stdout/stderr for debugging
            for line in process.stdout:
                print(f"[MCP STDOUT] {line}", end='')
            for line in process.stderr:
                print(f"[MCP STDERR] {line}", end='')

        
    thread = threading.Thread(target=target, daemon=True)
    thread.start()
    return thread

