import re
from datetime import datetime
import os
import subprocess
import platform
import logging
import os
import shutil
from db_connection import get_db_connection
import uuid
import socket
import urllib.request
import urllib.error
import urllib.error
from typing import List, Dict, Any
import json

directory = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

conn = get_db_connection()
cur = conn.cursor()

def add_instructions(test_step_docuemtation):
    instruction=(
    f"Here are the test steps and documentation {test_step_docuemtation} for Playwright MCP to convert to optimal automation script.Give priority to the test steps and refer to the documentation only as a guide. Documentation may add extra information that is not needed for the test steps. Do not add any extra validations that are not explicitly stated in the test steps. Do not invent new behaviors or extend beyond the provided instructions. Test cases are well written so no need to add any extra validations that are not explicitly stated in the test steps.Specifically any text validations out of scope of the test steps should not be added. Use the documentation only as a guide and not as a source of truth for the test steps. Do not add any extra validations that are not explicitly stated in the test steps. Do not invent new behaviors or extend beyond the provided instructions. Test cases are well written so no need to add any extra validations that are not explicitly stated in the test steps.")
    return instruction

def build_system_prompt() -> str:
    system_prompt = (
        "You are an expert Playwright Test Automation Engineer specialized in automation."
        "An expert at converting written test steps into clean, maintainable, and efficient code."
        "You don’t just read text literally—you grasp its underlying semantic meaning"
        "Your mission is to generate robust, maintainable test suites that leverage Playwright's Model-Centered Protocol (MCP). "
        "CRITICAL: MCP utilizes the accessibility tree rather than the full DOM - all element interactions must use "
        "accessibility attributes instead of traditional DOM structure.\n\n"
 
        "<core_capabilities>\n"
        "- Create production-ready Playwright test suites in TypeScript\n"
        "- Work exclusively with accessibility tree attributes (not DOM)\n"
        "- Generate self-contained, executable test code with proper assertions\n"
        "- Implement comprehensive error handling and recovery mechanisms\n"
        "- Produce clear test documentation with rationales for implementation choices\n"
        "</core_capabilities>\n\n"
 
        "<locator_strategy>\n"
        "PREFERRED (use in this order):\n"
        "1. `getByLabel`: Target elements by their associated label text\n"
        "2. `getByRole`: Target elements by their ARIA role with appropriate options\n"
        "3. `getByText`: Target elements by their visible text content\n"
        "4. `getByPlaceholder`: Target input fields by placeholder text\n"
        "AVOID THESE (use only as documented last resort):\n"
        "- CSS selectors, XPath, ID selectors, or any DOM structure-dependent locators\n"
        "- If you must use a non-accessibility locator, document the justification\n"
        "</locator_strategy>\n\n"
 
 
        "<reasoning_steps>\n"
        "Step 1: Reason About the Goal\n"
        "  - Analyze the user's step descriptions to identify the intent.\n"
        "  - Determine the core action (input, click, verify) and desired outcome.\n"
        "  - If locator info is missing or ambiguous, infer only what's necessary using accessibility principles.\n"
        "  - Do not invent new behaviors or extend beyond the provided instructions.\n\n"
        "Step 2: Contextually dynamic fields-When interpreting test steps that involve form inputs, recognize that some fields are dynamic in a **real-world sense**, meaning their valid values depend on business logic, temporal context, or test intent — not just DOM structure."
 
        "Follow these guidelines:"
       
        "1. **Respect Business Rules**:"
        "   - For age restrictions (e.g., must be 18+), compute the correct date relative to today's date."
        "   - For fields like expiration dates, use future dates unless the test requests otherwise."
       
        "2. **Use realistic, synthetic data**:"
        "   - Names, emails, phone numbers, etc., should be plausible and locale-appropriate."
        "   - Invalid values should be intentionally incorrect but syntactically relevant"
       
        "Examples:"
       
        "Test Step: 'Enter a valid date of birth for a user who is at least 18 years old."
        "find current date minus 18 years add to the date of birth field"
        "Step 3: Semantic reasoning -Proper semantic understanding is crucial for ensuring accurate test execution, especially when using LLM-driven test frameworks. The following gives semantic misunderstandings "
        "   1. **'Empty' Inputs**:"
        "       - Interpret 'empty' as an empty string: `""` (i.e., leave the field blank)."
        "       - Do NOT type the word 'empty'."
           
        "   2. **'Visible' vs. 'Hidden'**:"
        "       - 'Visible' means the element should be rendered and visible on screen."
        "       - 'Hidden' refers to elements not visible, either via `display: none` or `visibility: hidden`."
        "Step 4: Plan the Actions\n"
        "  - Break each goal into sequenced Playwright actions.\n"
        "  - Prefer visible text and ARIA-based locators.\n"
        "  - Include:\n"
        "    * Navigations (to pages or forms)\n"
        "    * Inputs (values for fields with labels)\n"
        "    * Actions (clicks on accessible buttons)\n"
        "    * Verifications (assertions on state or message visibility)\n"
        "  - Insert waits for navigation, element visibility, or interaction readiness.\n"
        "  - Handle challenges (e.g., duplicate labels, async rendering) using fallback strategies.\n\n"
        "Step 5: Self-Correct and Validate\n"
        "  - Review action plan for alignment with input steps.\n"
        "  - Ensure only the intended validations are present.\n"
        "  - Avoid overchecking (e.g., asserting success when only error is expected).\n"
        "  - Consider edge cases (missing/hidden labels, race conditions).\n"
        "  - Adjust to align with accessibility constraints and test determinism.\n\n"
        "Step 6: Generate Code (Playwright + TypeScript)\n"
        "  - Use `test.describe`, `test.beforeEach`, and multiple `test()` blocks.\n"
        "  - Use `await expect()` with meaningful selectors and accessible text.\n"
        "  - Structure test files cleanly with fixtures and helper utilities if needed.\n"
        "  - Name tests clearly (e.g., 'should show error for invalid email').\n"
        "  - Include comments, typed inputs, and properly formatted assertions.\n"
        "  - Ensure code is fully standalone and executable.\n"
        "  - Take screenshot and save to output directory.\n"
        "  - Save the test code to the output directory.\n"
       
        "</reasoning_steps>\n\n"
 
        "<testing_methodology>\n"
        "1. Requirement Analysis\n"
        "   - Extract clear test objectives from requirements\n"
        "   - Identify critical user flows and validation points\n"
        "   - Consider accessibility implications in test design\n\n"
        "2. Test Action Planning\n"
        "   - Design clear test step sequences with accessibility-first approach\n"
        "   - Anticipate potential stability issues and plan mitigations\n"
        "   - Structure tests for readability and maintenance\n\n"
        "3. Implementation Best Practices\n"
        "   - Implement page objects or component abstractions when beneficial\n"
        "   - Use descriptive test and function names that reflect business logic\n"
        "   - Include appropriate waits and synchronization points\n"
        "   - Add comprehensive assertions that validate both state and content\n"
        "   - Implement error recovery mechanisms for fragile interactions\n\n"
        "4. Validation Strategy\n"
        "   - Form field validation (empty, invalid, valid inputs)\n"
        "   - Error message verification via accessibility attributes\n"
        "   - For fields do not use exact text match, use partial matches or regex where appropriate\n"
        "   - Visual validation where appropriate\n"
        "5. Guidelines:\n"
        "   -Use the tool named <browser_generate_playwright_test> to generate test cases.\n"
        "   -Do not use promises or response checks unless explicitly specified.\n"
        "   -Refrain from adding any extra validations that are not explicitly stated in the test steps.\n"
        "   -Avoid capturing unnecessary details from the website that are not outlined in the test steps.\n"
        "   -Use timeout/wait in milliseconds example 5000,10000\n"
        "6. When verifying dynamic text content (e.g., error messages, validation hints, or status updates), always ensure the target element is scrolled into view using scrollIntoViewIfNeeded() or equivalent. This guarantees visibility for both the automation tool and visual validation.\n"
        "</testing_methodology>\n\n"
 
        "<complex_scenarios_handling>\n"
        "- Dynamic content: Implement `waitForSelector` with appropriate timeout and state options\n"
        "- Shadow DOM: Use special frame handling capabilities in Playwright\n"
        "- iframes: Leverage `frameLocator()` with accessibility selectors\n"
        "- SPAs: Add stabilization waits and state verification before actions\n"
        "- Multi-step forms: Implement progressive form filling with validation at each step\n"
        "- Internationalization: Create parameterized tests that work across locales\n"
        "</complex_scenarios_handling>\n\n"
 
        "<code_structure>\n"
        "- Test files should follow a consistent organization pattern\n"
        "- Include setup/teardown with appropriate fixture management\n"
        "- Implement reusable helper functions for common operations\n"
        "- Use appropriate test annotations and metadata\n"
        "- Follow TypeScript best practices with proper typing\n"
        "- Implement appropriate error handling with diagnostic information\n"
        "</code_structure>\n\n"
 
        "<output_format>\n"
        "1. Test Strategy Summary (concise overview of approach)\n"
        "2. Playwright Test Code (complete, runnable TypeScript code)\n"
        "3. Implementation Notes (key design decisions, accessibility considerations)\n"
        "4. Potential Enhancements (suggestions for improved coverage or robustness)\n"
        "5. Finally save the code in a file named playwright.spec.ts\n"
        "</output_format>\n\n"
 
        "<communication_style>\n"
        "- Use formal, professional language appropriate for technical documentation\n"
        "- Present code with clear formatting and proper syntax highlighting\n"
        "- When explaining code choices, focus on technical rationale and best practices\n"
        "- Highlight accessibility considerations in your implementation decisions\n"
        "- Provide complete context for code decisions rather than fragmented explanations\n"
        "</communication_style>\n"
       
       """
        <persistent_profile>
        You MUST use a persistent browser profile in all Playwright automation to ensure session state is preserved.
 
        NEVER use the default context (`browser.newContext`) when tests require session persistence (such as login state, saved cookies, or local storage).
 
        ALWAYS follow this exact setup when writing Playwright tests with persistent profile support:
 
        ```ts
        import {{ chromium, BrowserContext, Page, test, expect }} from '@playwright/test';
 
        let context: BrowserContext;
        let page: Page;
 
        const profileDir = './profile';  // This MUST be a valid and writable folder path
        const testUrl = 'https://example.com';
 
        // Persistent context setup
        test.beforeAll(async () => {{
        context = await chromium.launchPersistentContext(profileDir, {{
            headless: true,
            slowMo: 50,
        }});
        page = await context.newPage();
        }});
 
        // Clean up after all tests
        test.afterAll(async () => {{
        await context.close();
        }});
 
        // All tests must use the persistent context
        test('Example test using persistent profile', async () => {{
        await page.goto(testUrl);
 
        // Use robust selectors
        await page.getByRole('textbox', {{ name: 'Search' }}).fill('Playwright');
        await page.getByRole('button', {{ name: 'Search' }}).click();
 
        // Assert successful navigation or behavior
        await expect(page).toHaveURL(/search/);
    }});
    </persistent_profile> """
 
 
        "<reference>\n"
        "-Use the provided Playwright documentation as a reference for best practices and code snippets .\n"
        " -Use the test cases as a source of truth for the test steps and expected behavior.\n"
        " -No additional checks should be performed other than the ones mentioned in the test steps.\n"
        "- Do not invent new behaviors or extend beyond the provided instructions.\n"
        "- Test cases are well written so no need to add any extra validations that are not explicitly stated in the test steps.\n"
    )
    return system_prompt

def extract_typescript_code(response_text):
    """
    Extract the TypeScript code block from the response text that appears after any heading containing 'Playwright Test Code'.
    Args:
        response_text (str): The response text containing the Playwright test code.
    Returns:
        str: The extracted TypeScript code or None if not found.
    """
   
    pattern = r'Playwright Test Code.*?```(?:typescript|javascript|ts|js)\s*(.*?)```'
    match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
    if match:
        extract_code =match.group(1).strip()
       
        return extract_code
   
    return None
def save_and_run_ts_code(code,index):
    print("main from save_and_run_ts_code directory", directory)
    """
    Save the extracted TypeScript code to a file and execute it using Playwright.
    Args:
        code (str): The TypeScript code to be saved and run.
    Returns:
        str: The result of the test execution.
    """
    if not code:
        return "No TypeScript code found in the response."
    print("code", code)
    try:
       
        with open(f"{directory}/tests/test_report{index+1}.spec.ts", "w", encoding="utf-8") as f:
            f.write(code)
 
    except Exception as e:
       
        return f"Error saving TypeScript file: {str(e)}"


def run_playwright_tests(path):
    print("main from run_playwright_tests path", path)
    test_directory = path
    is_windows = platform.system() == "Windows"
 
    command = ["npx", "playwright", "test", test_directory, "--config=playwright.config.ts"]
 
    print("command", command)
    # try:
    result = subprocess.run(
        command,
        cwd=".",  # make sure this is where your config file is
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        shell=is_windows
    )
    return result.stdout, result.stderr
    # except Exception as e:
    #     logger.info(f"Error running subprocess: {e}")
    #     return "", str(e)


def clean_playwright_output(raw_output: str) -> str:
    # Remove ANSI escape sequences
    ansi_escape = re.compile(r'\x1B\[[0-?]*[ -/]*[@-~]')
    cleaned = ansi_escape.sub('', raw_output)
    # Replace misencoded characters like â€º
    cleaned = cleaned.replace("â€º", "›")  # optional: replace with intended character
 
    lines = cleaned.splitlines()
    filtered_lines = [
        line for line in lines
        if "To open last HTML report run" not in line
        and "npx playwright show-report" not in line
        and line.strip() != ''
    ]
    # Or strip all non-ASCII if you prefer
    # cleaned = re.sub(r'[^\x00-\x7F]+','', cleaned)
    return "\n".join(filtered_lines)


def is_port_in_use_socket(port):
    """
    Check if a port is in use using socket connection.
    """
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0
 
def is_port_in_use_urllib(port):
    """
    Check if a port is in use by trying to access a URL.
    """
    try:
        urllib.request.urlopen(f"http://localhost:{port}", timeout=1)
        return True
    except (urllib.error.URLError, ConnectionRefusedError):
        return False
 
def is_port_in_use_command(port):
    """
    Check if a port is in use using system commands.
    """
    try:
        if os.name == 'nt':  # Windows
            output = subprocess.check_output(f"netstat -ano | findstr :{port} | findstr LISTENING", shell=True)
            return len(output) > 0
        else:  # Linux/Mac
            output = subprocess.check_output(f"lsof -i :{port}", shell=True)
            return len(output) > 0
    except subprocess.CalledProcessError:
        return False
 
 

def is_port_in_use(port):
    """
    Check if a port is in use using multiple methods for reliability.
    """
    # Try multiple methods in case one fails
    methods = [is_port_in_use_socket, is_port_in_use_urllib, is_port_in_use_command]
   
    for method in methods:
        try:
            if method(port):
                print(f"Port {port} is in use (detected by {method.__name__})")
                return True
        except Exception as e:
            print(f"Error checking port with {method.__name__}: {e}")
   
    print(f"Port {port} is not in use (all methods checked)")
    return False

def find_available_port(start_port=9323):
    """
    Find the next available port.
    If start_port is in use, increment until finding an open port.
    """
    port = start_port
    max_port = start_port + 10  # Don't check indefinitely
   
    while port < max_port:
        if is_port_in_use(port):
            print(f"Port {port} is in use, trying port {port+1}")
            port += 1
        else:
            print(f"Port {port} is available and will be used")
            return port
   
    # If we get here, just return the next port after start_port
    return start_port + 1

def open_playwright_report(directory=directory, is_windows=True):
    """
    Launch the Playwright report in a web browser.
    Args:
        directory (str): The working directory to run the command.
        is_windows (bool): Whether the system is Windows (default: True).
    Returns:
        str: Success message or error message.
    """
    try:
 
        # First check port 9323 with all methods
        logger.info("\nChecking port 9323 with all methods:")
        is_port_in_use(9323)
       
        # Then find next available port
        logger.info("\nFinding available port:")
        port = find_available_port(9323)
        logger.info(f"Final port to use: {port}")
       
        command = ["npx", "playwright", "show-report", f"--port={port}"]
 
        # Prepare startupinfo for Windows to hide the console window (optional)
        startupinfo = None
        if is_windows:
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW

            subprocess.Popen(
                    f'start cmd /c {" ".join(command)}',
                    cwd=directory,
                    shell=True,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    startupinfo=startupinfo
                )
        else:
            subprocess.Popen(
                command,
                cwd=directory,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                start_new_session=True
            )
        logger.info(f"Report launched successfully on port {port}.")

    except Exception as e:
        logger.info(f"Error showing report: {str(e)}")

 
def convert_to_pg_array(uuid_list):
    return '{' + ','.join(str(u) for u in uuid_list) + '}'


def _flatten_tests(suites: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Recursively collect every leaf test from the 'suites' structure."""
    tests = []
    for suite in suites:
        for spec in suite.get("specs", []):
            for test in spec.get("tests", []):
                # Build a flat dict with the columns you need
                result = test["results"][0] if test["results"] else {}
                error = result.get("error", {})

                tests.append({
                    "test_name": spec["title"],
                    "file_name": suite["file"],
                    "status": test["status"],                    # passed | failed | skipped
                    "error_message": error.get("message"),
                    "stack_trace": error.get("stack"),
                    "start_time": result.get("startTime"),
                    "duration_ms": result.get("duration"),
                    "project_name": spec.get("projectName", "chrome"),
                    "retry": result.get("retry", 0)
                })
        # recurse into nested suites
        tests.extend(_flatten_tests(suite.get("suites", [])))
    return tests

def _flatten_tests(suites):
    flat = []
    for suite in suites:
        file_name = suite.get("file", "unknown")

        for subsuite in suite.get("suites", []):
            for spec in subsuite.get("specs", []):
                test_name = spec.get("title", "Unnamed Test")

                for test in spec.get("tests", []):
                    for result in test.get("results", []):
                        # ✅ Extract screenshot paths from attachments
                        attachments = result.get("attachments", [])
                        screenshot_paths = [
                            att["path"].replace("\\", "/")
                            for att in attachments
                            if att.get("contentType") == "image/png" and att.get("path")
                        ]

                        errors = result.get("errors", [])
                        error_message = errors[0]["message"] if errors else None
                        stack_trace = errors[0].get("location", {}).get("file") if errors else None

                        flat.append({
                            "test_name": test_name,
                            "file_name": file_name,
                            "status": result.get("status", ""),
                            "error_message": error_message,
                            "stack_trace": stack_trace,
                            "start_time": result.get("startTime"),
                            "duration_ms": result.get("duration", 0),
                            "project_name": result.get("projectName", ""),
                            "retry": result.get("retry", 0),
                            "screenshot_paths": screenshot_paths
                        })
    return flat

def insert_test_result(run_id, script_ids, result_file, result_json, duration_seconds=None, remarks=None):
    result_id = str(uuid.uuid4())
    script_ids_pg = convert_to_pg_array(script_ids)
    executed_at = datetime.now()

    try:
        logger.info(f"Inserting test result:\n"
                    f"Result ID: {result_id}\n"
                    f"Run ID: {run_id}\n"
                    f"Script IDs: {script_ids_pg}\n"
                    f"Result File: {result_file}\n"
                    f"Executed At: {executed_at}\n"
                    f"Duration: {duration_seconds}\n"
                    f"Remarks: {remarks}\n"
                    f"Result JSON: {result_json}")

        # Step 1: Insert into TestResults
        cur.execute("""
            INSERT INTO TestResults (
                result_id, run_id, script_ids, result_file, executed_at, duration_seconds, remarks, results_json
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            result_id,
            run_id,
            script_ids_pg,
            result_file,
            executed_at,
            duration_seconds,
            remarks,
            result_json
        ))
        conn.commit()

        # Step 2: Fetch inserted results_json back
        cur.execute("""
            SELECT results_json
            FROM TestResults
            WHERE result_id = %s
        """, (str(result_id),))
        row = cur.fetchone()
        if not row:
            raise Exception("No TestResults entry found for the given result_id.")
        results_json_dict = row[0]

        # Step 3: Flatten test results from suites
        flat_results = _flatten_tests(results_json_dict.get("suites", []))
        logger.info(f"✅ Retrieved {len(flat_results)} test results from JSON")

        # Step 4: Insert into DetailedTestResults
        for test in flat_results:
            detailed_id = str(uuid.uuid4())
            status = "Passed" if test["status"] == "passed" else "Failed"
            screenshot_paths = test.get("screenshot_paths", [])

            cur.execute("""
                INSERT INTO DetailedTestResults (
                    id, run_id, report_id,
                    test_name, file_name, status,
                    error_message, stack_trace,
                    start_time, duration_ms,
                    project_name, retry, created_at,
                    screenshot_paths
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                detailed_id,
                str(run_id),
                str(result_id),
                test["test_name"],
                test["file_name"],
                status,
                test["error_message"],
                test["stack_trace"],
                test["start_time"],
                test["duration_ms"],
                test["project_name"],
                test["retry"],
                datetime.now(),
                json.dumps(screenshot_paths)
            ))

        conn.commit()
        logger.info(f"✅ Inserted {len(flat_results)} rows into DetailedTestResults")
        logger.info("✅ Test result inserted successfully.")

    except Exception as e:
        conn.rollback()
        logger.error("❌ Failed to insert test result: %s", e)
        raise e

    finally:
        logger.info("finally")