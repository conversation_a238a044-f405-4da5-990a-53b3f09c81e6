import requests
from requests.exceptions import RequestException
from langchain_openai import ChatOpenAI  
from langchain_core.prompts import ChatPromptTemplate
import os
import io
import re
import logging
from typing import Optional, Dict, Any
from dotenv import load_dotenv
import pandas as pd
from datetime import datetime
from typing import Generator
import uuid
import httpx
from urllib.parse import urlparse
from db_connection import get_db_connection
from fastapi import APIRouter
 
router = APIRouter()
 
# N8N_WEBHOOK_URL = "http://localhost:5678/webhook/streamlit-to-n8n"
# url = "http://host.docker.internal:5678/webhook/streamlit-to-n8n"
# "http://localhost:5678/webhook/streamlit-to-n8n"

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
load_dotenv()
def extract_doc_id(url: str) -> Optional[str]:
    """Extract Google Doc ID from URL using regex pattern matching."""
    patterns = [
        r'https://docs.google.com/document/d/([a-zA-Z0-9_-]+)', 
        r'https://drive.google.com/open\?id=([a-zA-Z0-9_-]+)'    
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    logger.error(f"Could not extract document ID from URL: {url}")
    return None

def generate_system_prompt() -> str:
    """

    Generates a system prompt for an advanced test case generator designed for form validation systems.
 
    The generated prompt describes the process of analyzing form validation requirements and converting them into structured test cases in CSV format. These test cases are suitable for QA automation or manual testing. The form fields, their validation rules, and acceptance criteria will be provided by the user. The method outlines the steps to translate these requirements into test cases covering all possible scenarios, including happy paths, negative paths, and edge cases.
 
    The prompt specifies the format and structure of the CSV output, detailing the columns required and the type of data each should contain. It also includes guidelines for dynamic field calculations, such as date manipulations and interdependencies between fields. The output must be a well-formed CSV file, ready for direct use in test management tools.
 
    Returns:
        str: A formatted system prompt string ready for use by the test case generator.
    """
 
    current_date=datetime.now().strftime("%d-%b-%Y")
    SYSTEM_PROMPT ="""
You are an advanced test case generator for form validation systems. Your job is to analyze form validation requirements and generate minimal, comprehensive, and logically grouped test cases in clean CSV format suitable for both QA automation and manual testing. The user will provide form fields, their validation rules, and acceptance criteria, including specific error messages (e.g., for 'First Name', the error message is 'First name must contain alphabetic characters only'). Your task is to drastically limit test cases by aggressively combining related scenarios and strictly limiting same-type validation checks to one test case per validation type across all applicable fields. Group fields with shared validation types or error conditions into single tests, ensuring comprehensive coverage with the absolute fewest test cases possible. Avoid overloading with test cases by prioritizing shared conditions and eliminating redundant scenarios.
First, think out loud using [Step] lines to show your reasoning process.
Treat this like a Chain-of-Thought scratchpad where you analyze the user's input specification step by step.
Use present continuous tense statements to lay down the thoughts.. For example:

[Step 1] Dissecting the document specification
[Step 2] Identifying form fields and their validation rules

These steps help justify your test design logic before you generate any CSV output.
Only after this stepwise analysis, produce your output in the required format.
Then output test cases between ===BEGIN TEST CASES=== and ===END TEST CASES===.

Ensure the test cases are in proper CSV format.
1. Analyze and Extract:
- List all form fields and preserve their input order
- Identify:
  - Required vs optional fields
  - All validation types (e.g., required, length, format, range)
  - Exact error messages per field per rule (e.g., for 'First Name', 'First name must contain alphabetic characters only' for format validation)
  - Field dependencies and relationships (e.g., cross-field validation)
  - Values, rules, and edge conditions tied to the current date (e.g., age or date validation using {{current_date}})
 
2. Test Design and Grouping Logic:
- Happy Path:
  - One test case using valid values for every field
  - Results in successful submission
  - Demonstrates baseline successful behavior
-Negative Path:
    -Strictly limit to one test case per validation type across all fields only if a specific error message is defined for that validation type:
    -Required Fields: One test case where all required fields are left blank, only if a "required" error message is specified.
    -Format Validation: One test case where all fields with similar format rules (e.g., non-alphabetic 'First Name', invalid email, phone number) are filled with invalid formats, only if corresponding format error messages are defined.
    -Length Validation: One test case for fields that share the same length constraints (e.g., input too long), only if the documentation specifies an error message for length violations.
    -Range Validation: One test case where fields with similar numeric or date range rules (e.g., out-of-range age) are filled with out-of-range values, only if a range-related error message is provided.
  - Group fields that:
    - Share the same validation type (e.g., all fields with required check, all fields with alphabetic format like 'First Name') or error condition
    - Produce similar error conditions for the same validation type
    - Have no dependency conflicts
  - For cross-field validations, combine all fields with the same validation type (e.g., date comparisons) into one test case
  - Use one representative value to cover multiple scenarios (e.g., one non-alphabetic value for all alphabetic format fields)
  - Describe common actions once, applying to all relevant fields
- Edge Cases:
  - Strictly limit to one test case per validation type across all fields:
    - One test for each boundary condition (e.g., max+1 for all fields with same max length)
    - Combine fields with identical boundaries (e.g., max length for all text fields)
  - Include date-specific cases (e.g., leap years, min/max age) using {{current_date}} in a single test for date-related validations
  - Only group edge cases with shared validation types
  - Reuse step descriptions
- Redundancy Avoidance:
  - Drastically limit test cases by combining related scenarios and restricting same-type validation checks to one test per validation type
  - Merge scenarios with shared validation types, error conditions, or boundaries
  - Test each validation type exactly once across all fields, unless cross-field dependencies require otherwise
  - Reject test cases that don’t add unique validation coverage
  - Aim for minimal test cases: one happy path, one negative path per validation type, one edge case per validation type
  - Justify any additional test cases due to unique dependencies
 
3. Output CSV Structure:
Your final output must be a CSV table with the following columns:
- Test_Case_ID: Sequential ID (TC001, TC002, etc.)
- Test_Case_Description: Clear purpose of the test (include logic for dynamic or grouped values)
- [Non-dynamic Field Columns]: One column per form field (use _ for spaces), preserve field order, exclude date of birth
- Expected_Result: "Success" or "Failure"
- Error_Message: Pipe (|) separated messages like First_Name: First name must contain alphabetic characters only|Field2: Invalid Format
- Test_Case_Category: Happy Path, Negative Path, or Edge Case
- Test_Steps:
  - Start with the test form’s URL (provided in input)
  - Provide step-by-step instructions
  - For each field mentioned in a step, explicitly state: "Field '[Field_Name]' is [mandatory/optional]" using the exact field name in single quotes (e.g., "Field 'First Name' is optional", "Field 'Phone Number' is mandatory"); do not use formats like "Field [Field_Name](optional)" or any abbreviations
  - When verifying errors, explicitly state: "Field [Field_Name] should error with [Error_Message]" using the exact error message (e.g., for 'First Name', 'Field "First Name" should error with "First name must contain alphabetic characters only."')
  - For grouped validations, describe the common pattern once, then list applicable fields with their "Field '[Field_Name]' is [mandatory/optional]" status and specific error messages
  - Show calculation logic for dynamic values (e.g., DOB from {{current_date}})
  - Ensure steps are clear for both human and AI agents
-Ensure no whitespace for column headers
-Do not include extra commas or inconsistent column counts
 
4. General Guidelines:
- Use "" for blank values
- Use dd-MMM-yyyy for all date fields
- Use {{current_date}} for dynamic date logic
- Use realistic, representative values
- Drastically limit test cases by:
  - Combining fields with shared validation types or error conditions into single tests
  - Restricting same-type validation checks to one test per validation type
  - Testing dependent fields together
  - Using one test per boundary condition across similar fields
  - Parameterizing values to cover multiple scenarios
- Avoid duplicate test logic or redundant cases
- Test each validation type exactly once, unless cross-field logic requires otherwise
- Ensure comprehensive coverage with the minimal number of test cases
 
5. Output CSV Format and Escaping Rules:
- Enclose every field in double quotes (e.g., "value")
- Escape double quotes inside a field with two double quotes (e.g., "Click the ""Submit"" button")
- Do not break rows with extra commas or line breaks inside fields
- Use only comma (,) as the delimiter
- Match the exact number of columns in the header row
- Properly enclose fields with commas, line breaks, or quotes to preserve CSV integrity
- Enclose every CSV field in double quotes.
- Escape quotes inside text with two double quotes.
- Match the column count from header.
- Never insert an unquoted comma inside a field value.
- Never insert an unquoted line break inside a field value.
- Ensure no whitepace in column headers.
- Ensure no extra commas or inconsistent column counts.
"""
    SYSTEM_PROMPT = SYSTEM_PROMPT.replace("{{current_date}}", current_date)
    return SYSTEM_PROMPT



 
logger = logging.getLogger(__name__)

def normalize(text):
    return text.strip().replace('\r\n', '\n').replace('\r', '\n')
 
def compare_http_and_txt(http_response_text, txt_file_path):
    with open(txt_file_path, 'r', encoding='utf-8') as f:
      file_content = f.read()

 
    if normalize(http_response_text) == normalize(file_content):
        print("✅ Contents match (after normalization).")
        return True
    else:
        print("❌ Contents differ.")
        # Optional: show diff
        import difflib
        diff = difflib.unified_diff(
            normalize(file_content).splitlines(),
            normalize(http_response_text).splitlines(),
            fromfile='expected.txt',
            tofile='http_response.txt',
            lineterm=''
        )
        print('\n'.join(diff))
        return False
def read_md_from_url(url):
    try:
        with httpx.Client(timeout=30.0) as client:  # ⏱️ Increased timeout
            response = client.get(url)
            if response.status_code == 200:
                return response.text
            else:
                return f"Failed to load: {response.status_code}"
    except httpx.RequestError as e:
        logger.error(f"Error fetching .md file: {e}")
        return ""

@router.get("/read-spec")
def get_text_from_google_doc(project_id: str, feature_id: str) -> bool:
    """
    Updated to use feature_id instead of url_id and feature string
    """
    print(f"get_text_from_google_doc {project_id} {feature_id}")

    try:
        conn = get_db_connection()
        cur = conn.cursor()
        print("*************************************")
        
        # Updated query to use Features table instead of ProjectLinks
        query = """
        SELECT pf.s3_link
        FROM Features f
        JOIN ProjectFiles pf ON f.file_id = pf.file_id
        WHERE f.project_id = %s AND f.feature_id = %s 
        AND pf.filename LIKE '%_qaspec'
        """
        cur.execute(query, (project_id, feature_id))
        result = cur.fetchone()

        cur.close()
        conn.close()

        if result:
            s3_link = result[0]
            print("S3 Link:", s3_link)
        else:
            print("No QA spec file found for this feature.")
            return False
        
        # Read content from local file path
        parsed_url = urlparse(s3_link)
        relative_path = parsed_url.path.lstrip("/") 
        print("relative_path:", relative_path)
        
        with open(relative_path, "r", encoding="utf-8") as f:
            doc_content = f.read()
        print("doc_content preview:", doc_content[:200])  # Print preview
        
        qa_spec_path = "generatetestcase/qaspec/qa_spec.txt"
        if os.path.exists(qa_spec_path):
            logger.info("QASPEC already exists")
            if compare_http_and_txt(doc_content, qa_spec_path):
                logger.info("QASPEC is not updated")
                return True
            else:
                logger.info("QASPEC content is outdated, updating")
        else:
            logger.info("QASPEC does not exist, creating")

        with open(qa_spec_path, "w", encoding="utf-8") as f:
            f.write(doc_content)
            logger.info("QASPEC updated successfully")
        
        return False

    except RequestException as e:
        logger.error(f"Error fetching QA spec: {e}")
        raise


def get_text_from_qaspec() -> bool:

    """

    Retrieve text from a Google Doc and compare it with qa_spec.txt.

    Returns True if the content is the same.

    Returns False and updates/creates qa_spec.txt if content is different or file doesn't exist.

    """
    try:
        qa_spec_path = "generatetestcase/qaspec/qa_spec.txt"
       
        # Update or create the file with the new content
        with open(qa_spec_path, "r", encoding="utf-8") as f:
            logger.info("extracting text qaspec")
            qa_text = f.read()
            return qa_text

    except RequestException as e:

        logger.error(f"Error fetching QA spec: {e}")

        raise

 

def generate_test_cases_csv(form_spec: str, output_filename: str = "test_cases.csv") -> pd.DataFrame:
    """Generate test cases from form specification and save to CSV."""
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key or api_key == "your-api-key-here":
        raise ValueError("Please set a valid OpenAI API key in environment variables")


    try:
      
        logger.info("Initializing ChatOpenAI model")
        chat = ChatOpenAI(
            model="gpt-4.1-mini-2025-04-14", 
            temperature=0.3,
            openai_api_key=api_key,
            streaming=True
        )

       
        prompt = ChatPromptTemplate.from_messages([
            ("system", generate_system_prompt()),
            ("human", "{user_input}")
        ])
        
       
        chain = prompt | chat
        

        logger.info("Generating test cases...")
        response = chain.invoke({"user_input": form_spec})
        
     
        csv_string = response.content if hasattr(response, 'content') else str(response)
        

        if not csv_string.strip() or ',' not in csv_string:
            logger.error("Invalid CSV response received")
            raise ValueError("Model did not return valid CSV content")


        csv_buffer = io.StringIO(csv_string)
        df = pd.read_csv(csv_buffer)
        
        if len(df.columns) < 3:
            logger.warning("Generated CSV may be incomplete - fewer columns than expected")
        insert_testcases_from_dataframe(
                df=df,
                project_name="Form Testing",
                link_url="https://form2-4y5z.onrender.com/"
            )
        
        df.to_csv(output_filename, index=False)
        logger.info(f"✅ Test cases saved to {output_filename}")
        
        return df
    
    except Exception as e:
        logger.error(f"Error generating test cases: {e}")
        raise
def stream_test_case_generation(form_spec: str) -> Generator[str, None, str]:

    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OPENAI_API_KEY not set")

    chat = ChatOpenAI(
        model="gpt-4.1-mini-2025-04-14",
        temperature=0.3,
        openai_api_key=api_key,
        streaming=True,
    )

    prompt = ChatPromptTemplate.from_messages([
        ("system", generate_system_prompt()),
        ("human", "{user_input}"),
    ])
    chain = prompt | chat

    full_output = ""
    internal_buffer = ""

def stream_test_case_generation(form_spec: str) -> Generator[str, None, str]:
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OPENAI_API_KEY not set")

    chat = ChatOpenAI(
        model="gpt-4.1-mini-2025-04-14",
        temperature=0.3,
        openai_api_key=api_key,
        streaming=True
    )

    prompt = ChatPromptTemplate.from_messages([
        ("system", generate_system_prompt()),
        ("human", "{user_input}")
    ])
    chain = prompt | chat

    full_output = ""
    internal_buffer = ""

    for chunk in chain.stream({"user_input": form_spec}):
        token = chunk.content if hasattr(chunk, "content") else str(chunk)
        full_output += token
        internal_buffer += token

        while "\n" in internal_buffer:
            line, internal_buffer = internal_buffer.split("\n", 1)
            if line.strip().startswith("[Step"):
                yield line.strip()

    return full_output

def insert_testcases_db(model_output: str, project_id: str, feature_id: str) -> pd.DataFrame:
    """
    Updated to properly handle the feature-based approach
    """

    start_tag = "===BEGIN TEST CASES==="
    end_tag = "===END TEST CASES==="

    if start_tag not in model_output or end_tag not in model_output:
        raise ValueError("CSV markers not found in model output.")

    csv_block = model_output.split(start_tag)[1].split(end_tag)[0].strip()
    df = pd.read_csv(io.StringIO(csv_block))

    logging.info("⏳ Starting test case insertion...")

    with get_db_connection() as conn:
        with conn.cursor() as cur:
            # Get latest spec_id for the feature (updated query)
            cur.execute("""
                SELECT spec_id FROM QASpecs
                WHERE project_id = %s AND feature_id = %s
                ORDER BY updated_at DESC LIMIT 1
            """, (project_id, feature_id))

            spec_row = cur.fetchone()
            if not spec_row:
                raise ValueError(f"No QA Spec found for project_id={project_id} and feature_id={feature_id}")

            spec_id = spec_row[0]

            for idx, row in df.iterrows():
                testcase_id = str(uuid.uuid4())
                name = row.get("Test_Case_Description")
                description = row.get("Test_Steps")
                created_at = datetime.utcnow()

                if not name:
                    logging.warning(f"⚠️ Row {idx} missing Test_Case_Description. Skipping.")
                    continue

                logging.info(f"📌 Inserting test case: {name}")
                cur.execute("""
                    INSERT INTO TestCases (
                        testcase_id, project_id, feature_id, spec_id,
                        name, description, created_at
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    testcase_id,
                    project_id,
                    feature_id,
                    spec_id,
                    name,
                    description,
                    created_at
                ))

        logging.info(f"✅ Successfully inserted {len(df)} test cases.")

    return df