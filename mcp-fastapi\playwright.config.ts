/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { defineConfig } from '@playwright/test';
import path from 'path';
import type { Project } from '@playwright/test';
import FilteredJSONReporter from './filtered-json-reporter';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,

  use: {
    screenshot: 'only-on-failure', //  Capture screenshots on test failure
    // trace: 'retain-on-failure',    // Optional: useful for debugging
    // video: 'retain-on-failure',    // Optional: records video on failure
  },

  
  reporter: [
    ['list'], // Console output in list format
    ['dot'],  // Dot-style minimal output
    ['line'], // Line-by-line test output
    ['html', { outputFolder: 'playwright-report', open: 'never' }],
    ['json', { outputFile: path.join('playwright-report/custom-reports', 'report.json') }],
    ['junit', { outputFile: path.join('playwright-report/custom-reports', 'report.xml') }],
    [require.resolve('./filtered-json-reporter')],  // ✅ Add custom reporter like this
  ],

  projects: [
    { name: 'chrome' },
    // { name: 'msedge', use: { mcpBrowser: 'msedge' } },
    // { name: 'chromium', use: { mcpBrowser: 'chromium' } },
    // { name: 'firefox', use: { mcpBrowser: 'firefox' } },
    // { name: 'webkit', use: { mcpBrowser: 'webkit' } },
  ].filter(Boolean) as Project[],
});
