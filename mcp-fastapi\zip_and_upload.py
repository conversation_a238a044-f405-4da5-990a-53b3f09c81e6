import os
import zipfile
import boto3
from dotenv import load_dotenv

def zip_folder_excluding_index(source_folder: str, zip_path: str, exclude_filename="index.html"):
    """
    Zips all files in source_folder, excluding a specific filename.
    """
    with zipfile.ZipFile(zip_path, 'w', compression=zipfile.ZIP_DEFLATED) as zipf:
        for root, _, files in os.walk(source_folder):
            for file in files:
                if file == exclude_filename:
                    continue
                full_path = os.path.join(root, file)
                arcname = os.path.relpath(full_path, start=source_folder)
                zipf.write(full_path, arcname)

def upload_to_s3(local_file_path, s3_key=None, content_type=None, s3_prefix="", acl=None, verbose=False):
    """
    Uploads a local file to S3 using credentials from environment.
    """
    load_dotenv()

    AWS_ACCESS_KEY = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
    AWS_BUCKET_NAME = os.getenv("AWS_BUCKET_NAME", "mcp-qa-engineer")
    AWS_REGION = "ap-south-1"

    if not AWS_ACCESS_KEY or not AWS_SECRET_KEY or not AWS_BUCKET_NAME:
        raise RuntimeError("Missing AWS credentials or bucket name.")

    if not os.path.exists(local_file_path):
        raise FileNotFoundError(f"Local file '{local_file_path}' not found.")

    # Build S3 key
    if s3_key is None:
        s3_key = os.path.basename(local_file_path)
    if s3_prefix:
        s3_key = os.path.join(s3_prefix.strip("/"), s3_key).replace("\\", "/")

    # Guess content type
    if content_type is None:
        ext = os.path.splitext(local_file_path)[1].lower()
        content_type = {
            ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".pdf": "application/pdf",
            ".txt": "text/plain",
            ".zip": "application/zip",
            ".json": "application/json"
        }.get(ext, "application/octet-stream")

    s3_client = boto3.client(
        "s3",
        region_name=AWS_REGION,
        aws_access_key_id=AWS_ACCESS_KEY,
        aws_secret_access_key=AWS_SECRET_KEY,
    )

    extra_args = {"ContentType": content_type}
    if acl:
        extra_args["ACL"] = acl

    s3_client.upload_file(local_file_path, AWS_BUCKET_NAME, s3_key, ExtraArgs=extra_args)

    s3_url = f"https://{AWS_BUCKET_NAME}.s3.{AWS_REGION}.amazonaws.com/{s3_key}"

    if verbose:
        print(f"✅ Uploaded '{local_file_path}' → s3://{AWS_BUCKET_NAME}/{s3_key}")
        print(f"🌐 URL: {s3_url}")

    return s3_url

def zip_and_upload_folder(run_id, folder_name="playwright-report", staging_folder="backup_reports", 
                          s3_prefix="backups/reports", acl="public-read", verbose=True):
    """
    Zips the reports folder (excluding index.html), uploads to S3.
    """
    os.makedirs(staging_folder, exist_ok=True)
    zip_file_name = f"{run_id}.zip"
    zip_file_path = os.path.join(staging_folder, zip_file_name)

    # Step 1: zip folder excluding index.html
    zip_folder_excluding_index(folder_name, zip_file_path)

    # Step 2: upload to S3
    return upload_to_s3(
        local_file_path=zip_file_path,
        s3_prefix=s3_prefix,
        acl=acl,
        verbose=verbose
    )

