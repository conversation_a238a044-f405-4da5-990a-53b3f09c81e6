import asyncio
from dotenv import load_dotenv
load_dotenv()
from browser_use import Agent,BrowserSession
from browser_use.llm import ChatOpenAI
import sys
import os
import subprocess
import argparse
browser_session = BrowserSession(
    headless=True,
    viewport={'width': 964, 'height': 647},
    user_data_dir='openaisdk/profile',
)
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, ".."))  # one folder up
parser = argparse.ArgumentParser()
parser.add_argument("--module", type=str, required=True)
parser.add_argument("--urls", type=str, required=True)
args = parser.parse_args()

module_name = args.module
url = args.urls 



NAVIGATION_FLOW_PROMPT = f"""
You are a Website Navigator Agent analyzing the navigational flow of: {module_name} which has urls {url}

MISSION  
Map the essential user journey through this webpage and its linked internal pages. Focus on how users complete **primary tasks**, not exhaustive site exploration.

---

SCOPE  
- **Focus**: Main content area and primary user tasks  
- **Exclude**: Header, footer, sidebar, and non-essential navigation  
- **Domain limit**: Stay within the same domain for all child pages and links  

---

KEY DIRECTIVES  

1. **Navigation Flow Analysis**  
- Follow the main task completion sequence  
- If multiple similar elements exist (e.g., multiple “Edit” links), analyze only **one representative example**  
- Analyze each unique page, modal, or tab **only once** (no repeats or loops)  
- Do not follow external links unless absolutely necessary to complete the task  
- ✅ Before analyzing any page, form, modal, or tab — **scroll from top to bottom** to uncover all hidden content, buttons, or fields.

2. **Interactive Element Mapping**  
- Open **every modal, dialog, or popup** triggered by any visible UI element (button, icon, or link)  
- List **every field name individually** — **do not summarize** using phrases like “other relevant fields”  
- Document the following:
  - **Open via**: [Button label or trigger]  
  - **Close via**: [e.g., Click Cancel or X icon]  
  - **Fields:**  
    - [Field Name 1]  
    - [Field Name 2]  

- If the modal contains **tabs**, open each and list all fields under each  
- Inside forms, check for **buttons other than Submit/Cancel** — if they open modals, open them and document contents

🔴 IMPORTANT: You MUST attempt to open any modal, popup, or dialog that is triggered by visible buttons or clickable elements — even those inside forms or within tabbed sections.

This includes modals triggered by:
  -buttons in each page except submit/cancel.

✅ For each modal:
- Simulate a click to open it  
- Scroll the full modal  
- Document how it opens and closes  
- List **all field names**  
- Open and capture **all tabs**, if present

🚫 Do **NOT click**:
- Download/export buttons  
- Destructive actions (delete, archive)  
- Email, print, or system actions  

👉 Instead, describe their purpose (e.g., “Exports table as CSV”)

⚠️ If a modal fails to open, include this message:
**"Failed to open modal triggered by ‘[Button Label]’ — you may need to increase step size."**

3. **Form Page & Tabbed Section Discovery**  
- On any form-based page, **check for other adjacent tabs**  
- Navigate to **each tab**, scroll fully, and capture all field names or content  

4. **Form Field Capture**  
- List all field names in **natural tab order**
- Ignore field type, required status, or validation  
- 🔴 Do not skip any form fields:  
  - Scroll the full page or modal to discover all fields  
  - List **every visible field name individually**  
  - Applies to main forms, modal forms, and tabbed sections  
  - Do **not summarize** or use "Other relevant fields"  

5. **Capture All Page Fields**  
- For every visited page, tab, and modal — ensure **all fields** are documented  
- Use scrolling as needed to reveal off-screen or lazy-loaded elements
6. Open modals/dialog boxes/tabs in the webpage and list their fields and intended action  
- If a modal, popup, tab or dialog-triggering button is encountered:  
  - You MUST open it — this step is non-negotiable  
  - List **all visible field names** inside the modal (no summaries)  
  - Describe the **intended action** of the modal (e.g., “Add Facility”, “Upload Document”, “Assign User”)  
  - If the modal contains tabs, open each and list fields under them  
  - Scroll fully within the modal to ensure all content is captured
  7. **Critical**: Never miss to navigate to tabs/modals/buttons/dialog boxes from the visited page

OUTPUT REQUIREMENTS  

- **Progressive Updates**:
  - Append findings to `navigation.md` step-by-step (do **not overwrite**)  



STRUCTURE FORMAT  

=== NAVIGATION FLOW ANALYSIS ===  
TASK COMPLETION STATUS:  
- Provide the at the **very top** of the output readme file during each step.
- Do **not repeat status** in other sections  
- Use one of the following:
  - Success ( only if the task is completed successfully)
    else
  - Failed to Complete Task – You may need to increase step size.

---
Target URLs: {url}  
Identify the module {module_name} starting url then find navigational flow within the url/urls {url}
MAIN PAGE ANALYSIS:

- **Main Page Field Overview**:  
  - Provide a short description of key visible fields and their function  
    - Example:  
      - **Search Box** – filters area list  
      - **Add Area Button** – opens 'Add Area' modal  
      - **Status Dropdown** – filters by active/inactive  
      - **Table** – displays areas with view/edit/delete options  

- **Modal/Dialogs Encountered**:  
  - [Modal Name or Trigger Label] *(triggered from: [base URL])*  
    - **Open via**: [e.g., Click 'Add Area' button]  
    - **Close via**: [e.g., Click ‘Cancel’ or ‘X’ icon]  
    - **Fields:**  
      - [Field 1]  
      - [Field 2]  
    - **Tabs inside modal (if present):**  
      - [Tab Name]  
        - Fields:  
          - [Field A]  
          - [Field B]  

- **Tabs on Main Page (or Form page):**  
  - [Tab Name]  
    - Fields or Content:  
      - [Field or content item]  

CHILD PAGES DISCOVERED:  
- [List of internal child pages visited, with trigger and relative URL]

USER JOURNEY FLOWS:  
- [Step-by-step task completion path]  
- [Include any modals, tabs, or branch interactions]

=== CHILD PAGE ANALYSIS: [Page Title or Purpose] ===  
Trigger: [e.g., Click 'View' in table row]  
Page URL: [Relative or absolute path]  

- **Modals/Dialogs Encountered**:  
  - [Dialog Name or Trigger]  
    - Open via:  
    - Close via:  
    - Fields:  
      - [Field 1]  
      - [Field 2]  

- **Tabs inside dialogs (if any):**  
  - [Tab Name]  
    - Fields:  
      - [Field A]  

- **Tabs on Page (outside modal):**  
  - [Tab Name]  
    - Fields or Content:  
      - [Field or Data]

Repeat this for every unique child page discovered.
"""


#gpt-4.1-nano
#gpt-4.1-mini
#o4-mini-2025-04-16

llm= ChatOpenAI(model="gpt-4.1-mini", temperature=0.3)
# ...existing code...

async def run_navigation_agent():
  
    agent1 = Agent(
        name="NavigationAgent",
        task=NAVIGATION_FLOW_PROMPT,
        llm=llm,
        browser_session=browser_session,
        file_system_path="openaisdk/output_flow",
        message_context= f"""This module {module_name} captures navigation flows primarily leading to forms. 
Describe the navigation flow of the main page as well as any child pages, including their actions. 
In addition to the main page, also navigate to 'View' and 'Add' form pages. 
Structure the output in a clear, consistent format that can be easily understood by both AI systems and humans.""",
        extend_system_message=f"Go to the following URL/urls: {url} and extract all navigation flow (to and from the page), actions and expected user behavior. Save results as text file as navigation.md in the output folder. Output should be easily readable and structured for both AI and human understanding.",
    )

    await browser_session.start()

    response = await agent1.run(max_steps=50)
    await browser_session.kill()
    print("Total Duration")
    print(response.total_duration_seconds())
    if not response.is_successful():
        history = response.final_result()
        stripped_history = history.split("Attachments:")[0].strip()
        with open("openaisdk/output_flow/browseruse_agent_data/navigation.md", "w", encoding="utf-8") as f:
            f.write(stripped_history)
    # Always return the navigation.md content
    
    

if __name__ == "__main__":
  
    asyncio.run(run_navigation_agent())


# Remove: asyncio.run(main())