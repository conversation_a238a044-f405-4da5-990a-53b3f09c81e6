from fastapi import <PERSON><PERSON><PERSON>, HTTPEx<PERSON>, Query, Path as ApiPath, Request
from pathlib import Path
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import psycopg2
import psycopg2.extras
from pydantic import BaseModel
from datetime import datetime, timezone
import uuid
from uuid import UUID
from typing import Optional, List
from dotenv import load_dotenv
import logging
from fastapi import Body
import subprocess
import sys
import os
import time
from mcpservers import presistent_server
import json
from generatetestcase import testcase_extractor 
from generatetestcase import llm_testcase_generator
from db_connection import get_db_connection
from openaisdk import client_agent
# For running Server
import pandas as pd
import re
from sse_starlette.sse import EventSourceResponse
import asyncio
from urllib.parse import urlparse
from fastapi.responses import JSONResponse
import httpx
from mcp_use import MCPAgent, MCPClient
from langchain_openai import ChatOpenAI
from psycopg2.extras import Json
from fastapi.staticfiles import StaticFiles
from automation.retriever import *
from automation.automation_function import *
from automation.s3buket_upload_script import *
from automation.testrun import *
from create_profile_process import *
from  zip_and_upload import zip_and_upload_folder
from psycopg2.extras import execute_batch

# Mount the folder as a static files directory



# grap url links
def run_profile_creation(url):
    presistent_server.run_mcp_in_background()
    time.sleep(30)
    print("Running profile creation script...")
    script_path = os.path.join(os.path.dirname(__file__), "Grab-Url\\CrawlerGrabUrl.py")
    # subprocess.run([sys.executable, script_path], check=True)
    subprocess.run([sys.executable, script_path, url], check=True)
    
load_dotenv()

# --- Logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

app.mount("/CSA", StaticFiles(directory="CSA"), name="CSA")

app.mount(
    "/openaisdk/output_flow/browseruse_agent_data",
    StaticFiles(directory="openaisdk/output_flow/browseruse_agent_data"),
    name="agent_data"
)

# Pydantic model for editing test steps
class EditTestStepRequest(BaseModel):
    steps: str

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)


#-----------------------------------------------Get Last Run Time
@app.get("/get_last_run_time/{project_id}")
def get_last_run_time(project_id: str):
    try:
        conn = get_db_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        cur.execute("SELECT max(run_ended_at) FROM TestRuns WHERE project_id = %s", (project_id,))
        result = cur.fetchone()
        cur.close()
        conn.close()

        if result and result[0]:
            last_run_time = result[0]

            # Ensure both datetimes are timezone-aware
            if last_run_time.tzinfo is None:
                last_run_time = last_run_time.replace(tzinfo=timezone.utc)

            now = datetime.now(timezone.utc)
            diff = now - last_run_time

            days = diff.days
            hours = diff.seconds // 3600
            minutes = (diff.seconds % 3600) // 60

            response = "Last Runtime: "
            if days > 0:
                response += f"{days} days "
            if hours > 0:
                response += f"{hours} hours "
            if minutes > 0 or (hours == 0 and days == 0):
                response += f"{minutes} min "
            response += "ago"

            return {"last_run_time": response.strip()}
        else:
            return {"last_run_time": "Last Runtime: Not found"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

#-----------------------------------------------Get Testcases
@app.get("/api/project/{project_id}/{projectlink_id}/testcases")
def get_testcases_by_project_name_path(project_id: str, projectlink_id: str):
    query = """
        SELECT tc.testcase_id, tc.description, tc.name
        FROM testcases tc
        JOIN projects p ON tc.project_id = p.project_id
        WHERE p.project_id = %s AND tc.link_id = %s
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query, (project_id, projectlink_id))
                result = cur.fetchall()
        
        if not result:
            raise HTTPException(status_code=404, detail="No testcases found")
        return result
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

#-----------------------------------------------Get Testcase Steps
@app.get("/api/testcase/{testcase_id}/steps", response_class=HTMLResponse)
def get_testcase_steps_by_id(testcase_id: str):
    query = """
        SELECT ts.steps
        FROM TestCases tc
        JOIN Projects p ON tc.project_id = p.project_id
        JOIN ProjectLinks pl ON tc.link_id = pl.link_id
        JOIN TestSteps ts ON tc.testcase_id = ts.testcase_id
        WHERE tc.testcase_id = %s
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query, (testcase_id,))
                result = cur.fetchall()
        
        if not result:
            print(f"[DEBUG] No steps found for testcase_id: {testcase_id}")
            raise HTTPException(status_code=404, detail="No steps found for this testcase_id")
        
        steps = [row['steps'] for row in result]
        # Render steps with Edit, Delete, and Close buttons
        html = "<h2>Testcase Steps</h2>"
        for idx, step in enumerate(steps):
            html += f"<div style='margin-bottom:10px;'><b>Step {idx+1}:</b> {step} "
            html += f"<button onclick=\"alert('Edit functionality not implemented')\">Edit</button> "
            html += f"<button onclick=\"alert('Delete functionality not implemented')\">Delete</button> "
            html += f"<button onclick=\"window.close()\">Close</button></div>"
        
        print(result)
        return html
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


#--------------------------------------------Delete Test Steps
@app.delete("/api/testcase/step/{step_id}")
def delete_test_step(step_id: int):
    query = "DELETE FROM TestSteps WHERE step_id = %s"
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(query, (step_id,))
                if cur.rowcount == 0:
                    raise HTTPException(status_code=404, detail="Step not found")
        return {"detail": "Step deleted"}
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
#-----------------------------------------------Edit Test Steps
class TestStepIn(BaseModel):
    testcase_id: str
    steps: str
 
class TestStepOut(BaseModel):
    step_id: str
    testcase_id: str
    steps: str
    created_at: str
 
# Consistent DB connection function


# UPDATE
@app.put("/teststeps/", response_model=TestStepOut)
def update_test_step(data: TestStepIn):
    conn = get_db_connection()
    cur = conn.cursor()
    created_at = datetime.utcnow()
    try:
        cur.execute("""
            UPDATE TestSteps
            SET steps = %s, created_at = %s
            WHERE testcase_id = %s
            RETURNING step_id, testcase_id, steps, created_at
        """, (data.steps, created_at, data.testcase_id))
        result = cur.fetchone()
        if not result:
            raise HTTPException(status_code=404, detail="TestStep not found")
        conn.commit()
        response = dict(zip([desc[0] for desc in cur.description], result))
        response["created_at"] = response["created_at"].isoformat()  # Convert datetime to ISO string
        return response
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    finally:
        cur.close()
        conn.close()

#-----------------------------------------------Get Project Names
@app.get("/api/projects/names")
def get_project_names():
    query = """
        SELECT project_id, project_name, project_url FROM Projects ORDER BY created_at DESC
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query)
                result = cur.fetchall()
        
        return {"project_names": result}
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
#-----------------------------------------------Create Project

class ProjectInput(BaseModel):
    project_name: str
    description: Optional[str] = None
    project_url: Optional[str] = None  # ✅ New field

class Project(ProjectInput):
    project_id: str
    created_at: datetime

#------------------------------------------------Create
@app.post("/projects/", response_model=Project)
def create_project(project: ProjectInput):
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        project_id = str(uuid.uuid4())
        cur.execute("""
            INSERT INTO Projects (project_id, project_name, description, project_url)
            VALUES (%s, %s, %s, %s)
            RETURNING created_at
        """, (
            project_id,
            project.project_name,
            project.description,
            project.project_url
        ))
        created_at = cur.fetchone()[0]
        conn.commit()
        return {
            "project_id": project_id,
            "project_name": project.project_name,
            "description": project.description,
            "project_url": project.project_url,
            "created_at": created_at
        }
    except psycopg2.errors.UniqueViolation:
        conn.rollback()
        raise HTTPException(status_code=400, detail="Project name already exists")
    except Exception as e:
        conn.rollback()
        print(f"Internal Server Error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        cur.close()
        conn.close()

#----------------------------------------------Update Project


@app.put("/projects/{project_id}", response_model=Project)
def update_project(project_id: str, project: ProjectInput):
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("SELECT project_id FROM Projects WHERE project_id = %s", (project_id,))
    row = cur.fetchone()
    if not row:
        raise HTTPException(status_code=404, detail="Project not found")
    
    cur.execute("""
        UPDATE Projects
        SET project_name = %s, description = %s, project_url = %s
        WHERE project_id = %s
        RETURNING created_at
    """, (
        project.project_name,
        project.description,
        project.project_url,
        project_id
    ))
    created_at = cur.fetchone()[0]
    conn.commit()
    return {
        "project_id": row[0],
        "project_name": project.project_name,
        "description": project.description,
        "project_url": project.project_url,
        "created_at": created_at
    }

#-----------------------------------------------Delete project
@app.delete("/projects/{project_id}")
def delete_project(project_id: str):
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("SELECT project_id FROM Projects WHERE project_id = %s", (project_id,))
    if not cur.fetchone():
        raise HTTPException(status_code=404, detail="Project not found")
    cur.execute("DELETE FROM Projects WHERE project_id = %s", (project_id,))
    conn.commit()
    return {"message": "Project deleted successfully"}

#-----------------------------------------------Get Project Links
@app.get("/api/links/{project_id}/urls")
def get_project_links(project_id: str):
    query = """
        SELECT link_id, url FROM ProjectLinks WHERE project_id = %s AND feature_id IS NOT NULL ORDER BY scraped_at DESC
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query, (project_id,))
                result = cur.fetchall()
        print("result",result)
        # Extract domain from first URL in the list (assuming all share same domain)
        if result:
            parsed_url = urlparse(result[-1]['url'])
            main_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
        else:
            main_url = None

        print(main_url)

        return {"main_url": main_url, "urls": result}
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
#---------------------------------------------Edit Links
# Pydantic model for link updates
class LinkUpdateRequest(BaseModel):
    url: str

#----------------------------------------------Update Links
@app.put("/api/links/{link_id}")
def update_link(link_id: str, request: LinkUpdateRequest):
    """Update a project link URL"""
    query = "UPDATE ProjectLinks SET url = %s WHERE link_id = %s RETURNING link_id, url, page_title"
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query, (request.url, link_id))
                updated = cur.fetchone()
                if not updated:
                    raise HTTPException(status_code=404, detail="Link not found")
                conn.commit()
        return {"message": "Link updated successfully", "link": updated}
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

#----------------------------------------------Delete Links
@app.delete("/api/links/delete/{link_id}")
def delete_link(link_id: str):
    """Delete a project link"""
    query = "DELETE FROM ProjectLinks WHERE link_id = %s RETURNING link_id"
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(query, (link_id,))
                deleted = cur.fetchone()
                if not deleted:
                    raise HTTPException(status_code=404, detail="Link not found")
                conn.commit()
        return {"message": "Link deleted successfully", "deleted_link_id": deleted[0]}
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
@app.get("/")
def root():
    return {"message": "Welcome to the API"}


# create_project_link
class ProjectLinkCreate(BaseModel):
    project_id: str
    url: str
    page_title: Optional[str] = None
 
class ProjectLinkOut(BaseModel):
    link_id: str
    project_id: str
    file_id: Optional[str]
    url: str
    page_title: Optional[str]
    scraped_at: str
def get_file_id(project_id: str):
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute("""
            SELECT file_id FROM ProjectFiles
            WHERE project_id = %s
            ORDER BY uploaded_at DESC
            LIMIT 1
        """, (project_id,))
        result = cur.fetchone()
        if not result:
            raise HTTPException(status_code=404, detail="File not found")
        return result[0]
    finally:
        cur.close()
        conn.close()
 
 
@app.post("/projectlinks/", response_model=ProjectLinkOut)
def create_project_link(data: ProjectLinkCreate):
    link_id = str(uuid.uuid4())
    conn = get_db_connection()
    cur = conn.cursor()
    created_at=datetime.utcnow()
    
    try:
        # file_id = get_file_id(data.project_id)
        file_id = None
 
        # Check for exact URL already existing
        cur.execute("""
            SELECT link_id, project_id, file_id, url, page_title, scraped_at
            FROM ProjectLinks
            WHERE project_id = %s AND url = %s
        """, (data.project_id, data.url))
        existing = cur.fetchone()
 
        if existing:
            response = dict(zip([desc[0] for desc in cur.description], existing))
            response["scraped_at"] = response["scraped_at"].isoformat()
            raise HTTPException(status_code=200, detail={"message": "Exact URL already exists", "link": response})
 
        # Insert new link
        cur.execute("""
            INSERT INTO ProjectLinks (link_id, project_id, file_id, url, page_title,scraped_at,feature_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING link_id, project_id, file_id, url, page_title, scraped_at
        """, (link_id, data.project_id, file_id, data.url, data.page_title, created_at, None))
        result = cur.fetchone()
        conn.commit()
        response = dict(zip([desc[0] for desc in cur.description], result))
        response["scraped_at"] = response["scraped_at"].isoformat()
        return JSONResponse(content=response, status_code=201)
 
    except HTTPException:
        raise
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    finally:
        cur.close()
        conn.close()

# @---------for History

class TestRunCreateRequest(BaseModel):
    project_name: str
    url: str
    run_name: Optional[str] = None

class TestRunUpdateRequest(BaseModel):
    run_ended_at: Optional[datetime] = None

class TestResultCreateRequest(BaseModel):
    run_name: str
    script_ids: List[str]
    result_file: str
    duration_seconds: Optional[int] = None
    remarks: Optional[str] = None

# --- Utility ---
def get_project_and_link_ids(project_name, url):
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("""
        SELECT p.project_id, pl.link_id
        FROM Projects p
        JOIN ProjectLinks pl ON p.project_id = pl.project_id
        WHERE p.project_name = %s AND pl.url = %s
    """, (project_name, url))
    result = cur.fetchone()
    cur.close()
    conn.close()
    if result:
        return result[0], result[1]
    else:
        raise ValueError("No matching project/link found")

def convert_to_pg_array(uuid_list):
    return '{' + ','.join(str(u) for u in uuid_list) + '}'


@app.post("/test-runs", status_code=201)
def create_test_run(req: TestRunCreateRequest):
    """Create a new test run"""
    try:
        project_id, _ = get_project_and_link_ids(req.project_name, req.url)
        run_id = str(uuid.uuid4())
        run_started_at =  datetime.now()

        conn = get_db_connection()
        cur = conn.cursor()
        run_name = req.run_name or f"Run {run_started_at.strftime('%Y-%m-%d %H:%M:%S')}"
        
        cur.execute("""
            INSERT INTO TestRuns (run_id, project_id, run_name, run_started_at)
            VALUES (%s, %s, %s, %s)
            RETURNING run_id
        """, (run_id, project_id, run_name, run_started_at))
        
        returned_run_id = cur.fetchone()[0]
        conn.commit()
        cur.close()
        conn.close()
        
        return {
            "run_id": returned_run_id, 
            "run_name": run_name,
            "message": "Test run created successfully"
        }
    except ValueError as ve:
        raise HTTPException(status_code=404, detail=str(ve))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create test run: {e}")

@app.get("/test-runs")
def list_test_runs(
    project_name: Optional[str] = Query(None, description="Filter by project name"),
    limit: int = Query(50, ge=1, le=100, description="Number of results to return"),
    offset: int = Query(0, ge=0, description="Number of results to skip")
):
    """List all test runs with optional filtering"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        base_query = """
            SELECT tr.run_id, tr.project_id, tr.run_name, tr.run_started_at, tr.run_ended_at,
                   p.project_name
            FROM TestRuns tr
            JOIN Projects p ON tr.project_id = p.project_id
        """
        params = []
        
        if project_name:
            base_query += " WHERE p.project_name = %s"
            params.append(project_name)
            
        base_query += " ORDER BY tr.run_started_at DESC LIMIT %s OFFSET %s"
        params.extend([limit, offset])
        
        cur.execute(base_query, tuple(params))
        rows = cur.fetchall()
        
        # Get total count for pagination
        count_query = """
            SELECT COUNT(*)
            FROM TestRuns tr
            JOIN Projects p ON tr.project_id = p.project_id
        """
        count_params = []
        if project_name:
            count_query += " WHERE p.project_name = %s"
            count_params.append(project_name)
            
        cur.execute(count_query, tuple(count_params))
        total_count = cur.fetchone()[0]
        
        cur.close()
        conn.close()
        
        return {
            "test_runs": [
                {
                    "run_id": row[0],
                    "project_id": row[1],
                    "run_name": row[2],
                    "run_started_at": row[3],
                    "run_ended_at": row[4],
                    "project_name": row[5]
                }
                for row in rows
            ],
            "pagination": {
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_count
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch test runs: {e}")

@app.get("/test-runs/{run_id}")
def get_test_run(run_id: str = ApiPath(..., description="Test run ID")):
    """Get a specific test run by ID"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        cur.execute("""
            SELECT tr.run_id, tr.project_id, tr.run_name, tr.run_started_at, tr.run_ended_at,
                   p.project_name
            FROM TestRuns tr
            JOIN Projects p ON tr.project_id = p.project_id
            WHERE tr.run_id = %s
        """, (run_id,))
        
        row = cur.fetchone()
        cur.close()
        conn.close()
        
        if not row:
            raise HTTPException(status_code=404, detail="Test run not found")
            
        return {
            "run_id": row[0],
            "project_id": row[1],
            "run_name": row[2],
            "run_started_at": row[3],
            "run_ended_at": row[4],
            "project_name": row[5]
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch test run: {e}")

@app.patch("/test-runs/{run_id}")
def update_test_run(
    run_id: str = ApiPath(..., description="Test run ID"),
    req: TestRunUpdateRequest = TestRunUpdateRequest()
):
    """Update test run (currently only supports updating end time)"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        # Check if run exists
        cur.execute("SELECT run_id FROM TestRuns WHERE run_id = %s", (run_id,))
        if not cur.fetchone():
            cur.close()
            conn.close()
            raise HTTPException(status_code=404, detail="Test run not found")
        
        # Update end time (default to current time if not provided)
        end_time = req.run_ended_at or  datetime.now()

        cur.execute("""
            UPDATE TestRuns
            SET run_ended_at = %s
            WHERE run_id = %s
        """, (end_time, run_id))
        
        conn.commit()
        cur.close()
        conn.close()
        
        return {"message": "Test run updated successfully", "run_ended_at": end_time}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update test run: {e}")


@app.post("/test-results", status_code=201)
def create_test_result(req: TestResultCreateRequest):
    """Create a new test result"""
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        # Get run_id from run_name
        cur.execute("SELECT run_id FROM TestRuns WHERE run_name = %s", (req.run_name,))
        run = cur.fetchone()
        if not run:
            raise HTTPException(status_code=404, detail="Run name not found")
            
        run_id = run[0]
        result_id = str(uuid.uuid4())
        executed_at = datetime.now()
        script_ids_pg = convert_to_pg_array(req.script_ids)
        
        cur.execute("""
            INSERT INTO TestResults (
                result_id, run_id, script_ids, result_file, executed_at, duration_seconds, remarks
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            result_id,
            run_id,
            script_ids_pg,
            req.result_file,
            executed_at,
            req.duration_seconds,
            req.remarks
        ))
        
        conn.commit()
        return {
            "message": "✅ Test result created successfully", 
            "result_id": result_id,
            "executed_at": executed_at
        }
    except HTTPException:
        raise
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create test result: {e}")
    finally:
        cur.close()
        conn.close()

@app.get("/test-results")
def list_test_results(
    run_id: Optional[str] = Query(None, description="Filter by run ID"),
    run_name: Optional[str] = Query(None, description="Filter by run name"),
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    limit: int = Query(50, ge=1, le=100, description="Number of results to return"),
    offset: int = Query(0, ge=0, description="Number of results to skip")
):
    """List test results with optional filtering"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        base_query = """
            SELECT tr.result_id, tr.run_id, tr.script_ids, tr.result_file, 
                   tr.executed_at, tr.duration_seconds, tr.remarks,
                   trun.run_name, trun.project_id
            FROM TestResults tr
            JOIN TestRuns trun ON tr.run_id = trun.run_id
        """
        conditions = []
        params = []
        
        if run_id:
            conditions.append("tr.run_id = %s")
            params.append(run_id)
            
        if run_name:
            conditions.append("trun.run_name = %s")
            params.append(run_name)
            
        if start_date:
            conditions.append("tr.executed_at >= %s")
            params.append(datetime.strptime(start_date, "%Y-%m-%d"))
            
        if end_date:
            conditions.append("tr.executed_at <= %s")
            params.append(datetime.strptime(end_date, "%Y-%m-%d"))
        
        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)
            
        base_query += " ORDER BY tr.executed_at DESC LIMIT %s OFFSET %s"
        params.extend([limit, offset])
        
        cur.execute(base_query, tuple(params))
        rows = cur.fetchall()
        
        cur.close()
        conn.close()
        
        if not rows:
            return {"test_results": [], "message": "No results found for the given filters"}
            
        return {
            "test_results": [
                {
                    "result_id": row[0],
                    "run_id": row[1],
                    "script_ids": row[2],
                    "result_file": row[3],
                    "executed_at": row[4],
                    "duration_seconds": row[5],
                    "remarks": row[6],
                    "run_name": row[7],
                    "project_id": row[8]
                }
                for row in rows
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch test results: {e}")

@app.get("/test-results/run/{run_id}")
def get_test_results_by_run_id(run_id: str = ApiPath(..., description="Test run ID")):
    """
    Get all test results for a specific test run by its ID
    """
    try:
        # Connect to the database (excluding DB queries for simplicity)
        conn = get_db_connection()
        cur = conn.cursor()

        # Query to fetch all test results for the given run_id
        cur.execute("""
            SELECT tr.result_id, tr.run_id, tr.script_ids, tr.result_file, 
                   tr.executed_at, tr.duration_seconds, tr.remarks,
                   trun.run_name, trun.project_id
            FROM TestResults tr
            JOIN TestRuns trun ON tr.run_id = trun.run_id
            WHERE tr.run_id = %s
            ORDER BY tr.executed_at DESC
        """, (run_id,))

        rows = cur.fetchall()
        cur.close()
        conn.close()

        # If no results are found, return a 404 error
        if not rows:
            raise HTTPException(status_code=404, detail="No test results found for the given run ID")

        # Format the response
        return {
            "run_id": run_id,
            "test_results": [
                {
                    "result_id": row[0],
                    "run_id": row[1],
                    "script_ids": row[2],
                    "result_file": row[3],
                    "executed_at": row[4],
                    "duration_seconds": row[5],
                    "remarks": row[6],
                    "run_name": row[7],
                    "project_id": row[8]
                }
                for row in rows
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch test results: {e}")
@app.get("/projects/{project_id}/urls/test-results/history")
def get_test_results_history(
    project_id: str = ApiPath(..., description="Project name"),
    
):
    """Get complete test results history for a project and URL"""
    try:
        
        conn = get_db_connection()
        cur = conn.cursor()

        # Get all run_ids for this project
        cur.execute("""
            SELECT run_id, run_name, run_started_at, run_ended_at
            FROM TestRuns
            WHERE project_id = %s
            ORDER BY run_started_at DESC
        """, (project_id,))
        runs = cur.fetchall()

        if not runs:
            return {"message": "No test runs found for this project and URL"}

        all_results = []
        for run in runs:
            cur.execute("""
                SELECT result_id, result_file, executed_at, duration_seconds, remarks
                FROM TestResults
                WHERE run_id = %s
                ORDER BY executed_at DESC
            """, (run[0],))
            results = cur.fetchall()
            
            for res in results:
                all_results.append({
                    "run_id": run[0],
                    "run_name": run[1],
                    "run_started_at": run[2],
                    "run_ended_at": run[3],
                    "result_id": res[0],
                    "result_file": res[1],
                    "executed_at": res[2],
                    "duration_seconds": res[3],
                    "remarks": res[4]
                })

        cur.close()
        conn.close()
        
        return {
            "project_id": project_id,
            
            "test_results": all_results if all_results else []
        }

    except ValueError as ve:
        raise HTTPException(status_code=404, detail=str(ve))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch test results history: {e}")

@app.get("/projects/{project_name}/urls/{url:path}/test-results/latest")
def get_latest_test_result(
    project_name: str = ApiPath(..., description="Project name"),
    url: str = ApiPath(..., description="Project URL")
):
    """Get the latest test result for a project and URL"""
    try:
        project_id, _ = get_project_and_link_ids(project_name, url)
        conn = get_db_connection()
        cur = conn.cursor()

        # Get the latest run for this project
        cur.execute("""
            SELECT run_id, run_name, run_started_at, run_ended_at
            FROM TestRuns
            WHERE project_id = %s
            ORDER BY run_started_at DESC
            LIMIT 1
        """, (project_id,))
        run = cur.fetchone()

        if not run:
            cur.close()
            conn.close()
            return {"message": "No test runs found for this project and URL"}

        run_id = run[0]

        # Get the latest result for that run
        cur.execute("""
            SELECT result_id, result_file, executed_at, duration_seconds, remarks
            FROM TestResults
            WHERE run_id = %s
            ORDER BY executed_at DESC
            LIMIT 1
        """, (run_id,))
        result = cur.fetchone()

        cur.close()
        conn.close()

        return {
            "project_name": project_name,
            "url": url,
            "run_id": run[0],
            "run_name": run[1],
            "run_started_at": run[2],
            "run_ended_at": run[3],
            "latest_result": {
                "result_id": result[0] if result else None,
                "result_file": result[1] if result else None,
                "executed_at": result[2] if result else None,
                "duration_seconds": result[3] if result else None,
                "remarks": result[4] if result else None
            }
        }

    except ValueError as ve:
        raise HTTPException(status_code=404, detail=str(ve))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch latest test result: {e}")

#--------------------------------------------------------------History
@app.get("/api/projects/{project_id}/chathistory")
def get_chathistory(project_id: str):
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute(
        """
        SELECT DISTINCT ON (ch.chat_id)
            ch.chat_id,
            ch.user_message,
            ch.response,
            ch.created_at
            FROM ChatHistory ch
            JOIN Projects p ON ch.project_id = p.project_id
            WHERE ch.project_id = %s
            ORDER BY ch.chat_id, ch.created_at DESC
            """,
            (project_id,)
        )
        rows = cur.fetchall()
        cur.close()
        conn.close()
        return {"chathistory": rows}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch chat history: {e}")

#--------------------------------------------------------------History single chat
@app.get("/api/projects/chathistory/{chat_id}")
def fetch_chat_by_id(chat_id:str):
    """
    Fetches all chat entries with the same chat_id.
    """
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("""
        SELECT ch.chat_id, p.project_name, ch.user_message, ch.url, ch.response, ch.created_at
        FROM ChatHistory ch
        JOIN Projects p ON ch.project_id = p.project_id
        WHERE ch.chat_id = %s
    """, (chat_id,))
    rows = cur.fetchall()
    if not rows:
        return None
    return [
        {
            "user_message": row[2],
            "response": row[4],
        }
        for row in rows
    ]

@app.post("/api/projects/{project_id}/{chat_id}/{user_message}/chathistory")
def save_chat(chat_id, project_id, user_message, url, response):
    """
    Saves a chat entry into the ChatHistory table.
    """
    logging.info(f"Saving chat with chat_id: {chat_id}")
    # project_id = get_project_id(project_name)
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("""
        INSERT INTO ChatHistory (chat_id, project_id, user_message, url, response, created_at)
        VALUES (%s, %s, %s, %s, %s, %s)
    """, (
        chat_id,
        project_id,
        user_message,
        url,
        response,
        datetime.utcnow()
    ))
    logging.info(f"✅ Chat saved successfully: {chat_id}")


###### add Files
class FileInsertRequest(BaseModel):
    filename: str
    s3_link: str

@app.post("/insert-project-file/{project_id}")  # Fixed: removed extra underscore
def insert_project_file_api(request: FileInsertRequest, project_id: str):
    try:
        # Validate that project_id and request data are not empty
        if not project_id or not project_id.strip():
            raise HTTPException(status_code=400, detail="Project ID is required")
        
        if not request.filename or not request.filename.strip():
            raise HTTPException(status_code=400, detail="Filename is required")
            
        if not request.s3_link or not request.s3_link.strip():
            raise HTTPException(status_code=400, detail="S3 link is required")
     
        file_id = str(uuid.uuid4())
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Check if the file already exists
                cur.execute("""
                    SELECT 1 FROM ProjectFiles
                    WHERE file_id = %s AND project_id = %s AND filename = %s
                """, (file_id, project_id, request.filename.strip()))
                
                exists = cur.fetchone()
                
                if not exists:
                    # Insert only if it doesn't exist
                    cur.execute("""
                        INSERT INTO ProjectFiles (file_id, project_id, filename, s3_link)
                        VALUES (%s, %s, %s, %s)
                    """, (file_id, project_id, request.filename.strip(), request.s3_link.strip()))
                    conn.commit()
                else:
                    print("Record already exists. Skipping insert.")

        return {"status": "success", "message": f"✅ Inserted file '{request.filename}' for project '{project_id}' successfully."}

    except HTTPException as http_err:
        raise http_err
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


class QaSpecInsertRequest(BaseModel):
    filename: str
    s3_link: str

#insert Project qa Spec
@app.post("/insert-project-file-qa-spec/{project_id}/{url_id}")  # Fixed: removed extra underscore
def insert_project_file_qa_spec(request: QaSpecInsertRequest, project_id: str, url_id: str):
    try:
        # Validate that project_id and request data are not empty
        if not project_id or not project_id.strip():
            raise HTTPException(status_code=400, detail="Project ID is required")
        
        if not request.filename or not request.filename.strip():
            raise HTTPException(status_code=400, detail="Filename is required")
            
        if not request.s3_link or not request.s3_link.strip():
            raise HTTPException(status_code=400, detail="S3 link is required")
     
        file_id = str(uuid.uuid4())
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO ProjectFiles (file_id, project_id, filename, s3_link)
                    VALUES (%s, %s, %s, %s)
                """, (file_id, project_id, request.filename.strip(), request.s3_link.strip()))
            conn.commit()

        return {"status": "success", "message": f"✅ Inserted file '{request.filename}' for project '{project_id}' successfully."}

    except HTTPException as http_err:
        raise http_err
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

#--------------------------------check the file exists with the url_id
@app.get("/check-link-exists/{project_id}/{url_id}")
def check_link_exists(project_id: str, url_id: str):
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT file_id FROM ProjectLinks WHERE project_id = %s AND link_id = %s",
                    (project_id, url_id)
                )
                result = cur.fetchone()
                print("result---------------------",result)
                if result[0] == None:
                    return {"exists": False}
                else:
                    return {"exists": True}
                
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
#--------------------------------Get Project Files
class ProjectFile(BaseModel):
    file_id: str
    filename: str
    s3_link: str

@app.get("/get-project-files/{project_id}", response_model=List[ProjectFile])
def get_project_files(project_id: str):
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT file_id,filename, s3_link
                    FROM ProjectFiles
                    WHERE project_id = %s
                """, (project_id,))
                rows = cur.fetchall()
        print([{"file_id":row[0],"filename": row[1], "s3_link": row[2]} for row in rows])
        return [{"file_id":row[0],"filename": row[1], "s3_link": row[2]} for row in rows]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/delete-project-file/{file_id}")
def delete_project_file_api(file_id: str):
    try:
        if not file_id or not file_id.strip():
            raise HTTPException(status_code=400, detail="File ID is required")
        
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # First check if file exists
                cur.execute("SELECT filename FROM ProjectFiles WHERE file_id = %s", (file_id,))
                result = cur.fetchone()
                
                if not result:
                    raise HTTPException(status_code=404, detail="File not found")
                
                filename = result[0]
                
                # Delete the file
                cur.execute("DELETE FROM ProjectFiles WHERE file_id = %s", (file_id,))
                
                if cur.rowcount == 0:
                    raise HTTPException(status_code=404, detail="File not found")
            
            conn.commit()

        return {"status": "success", "message": f"✅ File '{filename}' deleted successfully."}

    except HTTPException as http_err:
        raise http_err
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

class UrlRequest(BaseModel):
    url: str
# @app.post("/create_profile/{project_id}")
# def create_persistent_profile(req: UrlRequest, project_id: str):
#     print("Creating profile from:", req.url)
#     try:
#         # Generate profile and save URLs JSON
#         run_profile_creation(req.url)

#         file_id = get_file_id(project_id)
#         script_path = os.path.join(os.path.dirname(__file__), "Grab-Url", "extracted_urls.json")

#         # Load only Main Navigation URLs
#         with open(script_path, "r", encoding="utf-8") as f:
#             url_data = json.load(f)
#         # Collect all URLs from keys that start with "Main Navigation"
#         # main_urls = []
#         # for key, urls in url_data.items():
#         #     if key.strip().lower().startswith("main navigation") and isinstance(urls, list):
#         #         main_urls.extend(urls)

#         inserted = 0

#         with get_db_connection() as conn:
#             with conn.cursor() as cur:
#                 for url in main_urls:
#                     # Skip if URL already exists
#                     cur.execute(
#                         "SELECT 1 FROM ProjectLinks WHERE project_id = %s AND file_id = %s AND url = %s",
#                         (project_id, file_id, url)
#                     )
#                     if cur.fetchone():
#                         continue

#                     link_id = str(uuid.uuid4())
#                     cur.execute(
#                         """
#                         INSERT INTO ProjectLinks
#                         (link_id, project_id, file_id, url, page_title, scraped_at)
#                         VALUES (%s, %s, %s, %s, %s, %s)
#                         """,
#                         (link_id, project_id, file_id, url, None, datetime.utcnow())
#                     )
#                     inserted += 1

#                 conn.commit()

#         return {"status": "success", "inserted_links": inserted, "total_links": len(main_urls)}

#     except subprocess.CalledProcessError as e:
#         raise HTTPException(status_code=500, detail=f"Process error: {e}")
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Failed to save links: {e}")


@app.post("/create_profile/{project_id}")
def create_persistent_profile(req: UrlRequest, project_id: str):
    print("Creating profile from:--------------------------------", req.url)
    try:
        # Generate profile and save URLs JSON
        run_profile_creation(req.url)

        # file_id = get_file_id(project_id)
        script_path = os.path.join(os.path.dirname(__file__), "Grab-Url", "extracted_urls.json")
        print("script_path",script_path)

        # Load all URLs from all keys in the JSON
        with open(script_path, "r", encoding="utf-8") as f:
            url_data = json.load(f)
        # Flatten all URL lists
        main_urls = []
        for urls in url_data.values():
            if isinstance(urls, list):
                main_urls.extend(urls)


        # # Filter only base URLs (remove /add and similar subpaths)
        # unique_base_urls = {}
        # for url in main_urls:
        #     parsed = urlparse(url)
        #     path_parts = parsed.path.strip("/").split("/")
            
        #     if len(path_parts) >= 2 and path_parts[-1] == "add":
        #         base_path = "/".join(path_parts[:-1])
        #     else:
        #         base_path = "/".join(path_parts)
            
        #     base_url = f"{parsed.scheme}://{parsed.netloc}/{base_path}"
            
        #     # Only keep the first seen main URL
        #     if base_path not in unique_base_urls:
        #         unique_base_urls[base_path] = base_url

        # # Final list of filtered base URLs
        # filtered_urls = list(unique_base_urls.values())
        
        # print("filtered_urls",filtered_urls)

        inserted = 0
        feature_id=None

        with get_db_connection() as conn:
            with conn.cursor() as cur:
                for url in main_urls:
                    # Skip if URL already exists
                    cur.execute(
                        "SELECT 1 FROM ProjectLinks WHERE project_id = %s AND url = %s",
                        (project_id, url)
                    )
                    if cur.fetchone():
                        continue

                    link_id = str(uuid.uuid4())
                    cur.execute(
                        """
                        INSERT INTO ProjectLinks
                        (link_id, project_id, file_id, url, page_title, scraped_at, feature_id)
                        VALUES (%s, %s, %s, %s, %s, %s,%s)
                        """,
                        (link_id, project_id, None, url, None, datetime.utcnow(), feature_id)
                    )
                    inserted += 1

                conn.commit()

        return {"status": "success", "inserted_links": inserted, "total_links": len(main_urls)}

    except subprocess.CalledProcessError as e:
        raise HTTPException(status_code=500, detail=f"Process error: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save links: {e}")

# project base url
@app.get("/get-project-base-url/{project_id}")
def get_project_base_url(project_id: str):
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT project_url FROM Projects WHERE project_id = %s", (project_id,))
            result = cur.fetchone()
            if result:
                return {"main_url": result[0]}  # ✅ Structured response
            else:
                raise HTTPException(status_code=404, detail="Project not found")


######################################################Generate Test case and steps

@app.post("/generate-tests/{project_id}/{url_id}")
async def generate_tests(project_id: str, url_id: str):
    # print("generate-tests", project_id, url_id)

    # feature="CSA"
    # # get the  testcase_id from the database
    # with get_db_connection() as conn:
    #     with conn.cursor() as cur:
            
    #         cur.execute("""
    #             SELECT testcase_id, name, description
    #             FROM TestCases
    #             WHERE project_id = %s AND link_id = %s
    #             ORDER BY created_at
    #         """, (project_id, url_id))

    #         testcases= [
    #             {
    #                 "testcase_id": row[0],
    #                 "name": row[1],
    #                 "description": row[2]
    #             }
    #             for row in cur.fetchall()
    #         ]
    # # get the testcase_id from the database
    # with get_db_connection() as conn:
    #     with conn.cursor() as cur:
    #         cur.execute("""
    #             SELECT ts.testcase_id
    #             FROM TestSteps ts
    #             JOIN TestCases tc ON ts.testcase_id = tc.testcase_id
    #             JOIN Projects p ON tc.project_id = p.project_id
    #             JOIN ProjectLinks pl ON tc.link_id = pl.link_id
    #             WHERE p.project_id = %s AND pl.link_id = %s
    #         """, (project_id, url_id))
            
    #         existing_ids = {row[0] for row in cur.fetchall()}
    # for tc in testcases[:1]:
    #     print(f"Processing test case: {tc['name']}------------------------------------------" )
    #     if tc['testcase_id'] not in existing_ids:
    #         output = llm_testcase_generator.process_row(tc,project_id,url_id,feature)

    feature="CSA"
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                        SELECT spec_id, approved
                        FROM QASpecs
                        WHERE project_id = %s AND link_id = %s
                    """, (project_id, url_id))
            result = cur.fetchone()
            spec_id, approved=result
    print("approved",approved)

    if approved:
        try:
            logger.info(f"Processing Google Doc URL: {url_id}")
            extractor_test_flag =testcase_extractor.get_text_from_google_doc(project_id, url_id,feature)

            # Assuming check_test_code_count is defined; otherwise, remove or implement
                
            logger.info("Generating new test cases from QA spec")
            reasoning_steps = ""
            gen = testcase_extractor.stream_test_case_generation(testcase_extractor.get_text_from_qaspec())
            try:
                while True:
                    token = next(gen)
                    cleaned_token = re.sub(r"^\[Step\s*\d+\]\s*", "", token)
                    reasoning_steps += cleaned_token + "\n"
            except StopIteration as e:
                full_output = e.value
                
                # insert into database test case
                try:
                    testcase_extractor.insert_testcases_db(full_output, project_id, url_id)
                    try:
                        conn = get_db_connection()
                        cursor = conn.cursor()
                
                        query = """
                        SELECT tc.name , tc.testcase_id
                        FROM Projects p
                        JOIN ProjectLinks pl ON p.project_id = pl.project_id
                        JOIN TestCases tc ON pl.link_id = tc.link_id
                        WHERE p.project_id = %s
                        AND pl.link_id = %s ORDER BY tc.created_at ASC;
                        """
                
                        cursor.execute(query, (project_id, url_id))
                        rows = cursor.fetchall()
                        test_cases=[{"name": row[0], "testcase_id": row[1]} for row in rows]
                
                    except Exception as e:
                        print("Error:", e)
                        return []
                    finally:
                        cursor.close()
                        conn.close()
                    test_cases = pd.DataFrame.from_records(test_cases)
                    test_cases=test_cases.to_dict(orient='records')
                    
                    
                except Exception as err:
                    print(f"Error extracting CSV: {str(err)}")
                    
                logger.info("Test cases generated successfully")
                
                
                # get the  testcase_id from the database
                with get_db_connection() as conn:
                    with conn.cursor() as cur:
                        
                        cur.execute("""
                            SELECT testcase_id, name, description
                            FROM TestCases
                            WHERE project_id = %s AND link_id = %s
                            ORDER BY created_at
                        """, (project_id, url_id))

                        testcases= [
                            {
                                "testcase_id": row[0],
                                "name": row[1],
                                "description": row[2]
                            }
                            for row in cur.fetchall()
                        ]
                # get the testcase_id from the database
                with get_db_connection() as conn:
                    with conn.cursor() as cur:
                        cur.execute("""
                            SELECT ts.testcase_id
                            FROM TestSteps ts
                            JOIN TestCases tc ON ts.testcase_id = tc.testcase_id
                            JOIN Projects p ON tc.project_id = p.project_id
                            JOIN ProjectLinks pl ON tc.link_id = pl.link_id
                            WHERE p.project_id = %s AND pl.link_id = %s
                        """, (project_id, url_id))
                        
                        existing_ids = {row[0] for row in cur.fetchall()}
                for tc in testcases[:1]:
                    print(f"Processing test case: {tc['name']}------------------------------------------" )
                    if tc['testcase_id'] not in existing_ids:
                        output = llm_testcase_generator.process_row(tc,project_id,url_id,feature)
                        
                        try:
                            with get_db_connection() as conn:
                                with conn.cursor() as cur:
                                    step_id = str(uuid.uuid4())
                                    cur.execute("""
                                        INSERT INTO TestSteps (step_id, testcase_id, steps, created_at)
                                        VALUES (%s, %s, %s, NOW())
                                    """, (step_id, tc['testcase_id'], output))
                                    logging.info(f"Inserted test step for testcase_id: {tc['testcase_id']}")
                        except Exception as e:
                            logging.error(f"Failed to insert test step for testcase_id {tc['testcase_id']}: {e}")
                            raise

                    else:
                        print(f"⏩ Skipped: Test step already exists for test case '{tc['name']}'")

                try:
                    conn = get_db_connection()
                    cursor = conn.cursor()

                    query = """
                    SELECT ts.testcase_id, ts.steps
                    FROM Projects p
                    JOIN ProjectLinks pl ON p.project_id = pl.project_id
                    JOIN TestCases tc ON pl.link_id = tc.link_id
                    JOIN TestSteps ts ON tc.testcase_id = ts.testcase_id
                    WHERE p.project_id= %s
                    AND pl.link_id = %s;
                    """

                    cursor.execute(query, (project_id, url_id))
                    rows = cursor.fetchall()

                    # Convert to list of dicts with keys "testcase_id" and "steps"
                    detailed_test_steps= [{"testcase_id": row[0], "steps": row[1]} for row in rows]

                except Exception as e:
                    logging.error(f"Error fetching testcase steps for project '{project_id}' and URL '{url_id}': {e}")
                    return []
                finally:
                    cursor.close()
                    conn.close()
                
                detailed_test_steps = pd.DataFrame.from_records(detailed_test_steps)
                detailed_test_steps=detailed_test_steps.to_dict(orient='records')
            except Exception as e:
                print(f"Error fetching test cases: {str(e)}")

            

        

        except Exception as e:
            logger.error(f"API error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")

    else:
        logger.error(f"API error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")




@app.get("/regenerate-test-stream/{project_id}/{url_id}/")
async def regenerate_test_stream(project_id: str, url_id: str):
    async def event_generator():
        
        start_time = time.time()
        yield {"data": "Remove all existing test cases..."}
        
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM TestCases WHERE project_id = %s AND link_id = %s", (project_id, url_id))
        cursor.execute("DELETE FROM TestSteps WHERE testcase_id IN (SELECT testcase_id FROM TestCases WHERE project_id = %s AND link_id = %s)", (project_id, url_id))

        conn.commit()
        cursor.close()
        conn.close()
        await asyncio.sleep(3)  # simulate work

        yield {"data": "Regenerating test Cases and Steps..."}
        await generate_tests(project_id, url_id)
         # simulate work
        elapsed = round(time.time() - start_time, 2)
       
        yield {"data":  f"Regenerated test cases and steps are ready....(Time Taken: {elapsed} seconds)"}

    return EventSourceResponse(event_generator())


@app.get("/api/project/{project_id}/{url_id}/testcases/approve")
def approve_test_cases(project_id: str, url_id: str):
    print("project_idss", project_id)
    # try:
    with get_db_connection() as conn:
        print("process started")
        with conn.cursor() as cur:
            print("process started updation")
            cur.execute("""UPDATE TestSteps ts
                SET approved = TRUE
                FROM TestCases tc
                WHERE ts.testcase_id = tc.testcase_id
                AND tc.project_id = %s
                AND tc.link_id = %s""", (project_id, url_id))
            conn.commit()
    print("project_idsss", project_id)
    return {"status": "success", "message": "Test cases approved successfully"}
    # except Exception as e:
    #     raise HTTPException(status_code=500, detail=str(e))

#-------------------upload qaspec file
@app.post("/upload-qaspec-file/{project_id}/{url_id}")
def upload_qaspec_file(project_id: str, url_id: str):

    try:
        print("project_id", project_id)
        return {"status": "success", "message": "QASpec file uploaded successfully"}
    except Exception as e:  
        raise HTTPException(status_code=500, detail=str(e))


########################################## A2A QA SERCO Function call


# @app.get("/api/start-a2a/{project_id}/{url_id}")
# async def trigger_a2a(project_id: str, url_id: str, request: Request):
#     feature="CSA"
#     print("qaspec")
#     message = "Generate a QA spec for the project"
#     print(message)
#     url_fetch="https://smt-serco-web-qa.azurewebsites.net/csa"
#     # await client_agent.main_qa_spec(url_fetch)

#     # 🔍 Dynamically get local base URL
#     base_url = str(request.base_url).rstrip("/")  # e.g., http://127.0.0.1:8000

#     file_id = str(uuid.uuid4())
#     url_file = f"{base_url}/CSA/CSA_Unified_QA_Spec.md"
#     url_file_navigation = f"{base_url}/navigation/navigation.md"
#     fearure_name_navigation=f"{feature}_navigation"
#     fearure_name_qaspec=f"{feature}_qaspec"
#     file_id_dict = {}
#     try:
#         with get_db_connection() as conn:
#             with conn.cursor() as cur:
#                 # Check if CSA file already exists
#                 cur.execute("""
#                     SELECT file_id FROM ProjectFiles
#                     WHERE project_id = %s AND filename = %s
#                 """, (project_id, fearure_name_qaspec))
#                 existing_csa = cur.fetchone()
                

#                 if existing_csa:
#                     print("existing_csa", existing_csa)
#                     # Update existing CSA file link
#                     cur.execute("""
#                         UPDATE ProjectFiles
#                         SET s3_link = %s
#                         WHERE file_id = %s
#                     """, (url_file, existing_csa[0]))
#                     csa_file_id = existing_csa[0]
#                 else:
#                     # Insert new CSA file
#                     csa_file_id = str(uuid.uuid4())
#                     cur.execute("""
#                         INSERT INTO ProjectFiles (file_id, project_id, filename, s3_link)
#                         VALUES (%s, %s, %s, %s)
#                     """, (csa_file_id, project_id, fearure_name_qaspec, url_file))

                

#                 # Check if navigation file already exists
#                 cur.execute("""
#                     SELECT file_id FROM ProjectFiles
#                     WHERE project_id = %s AND filename = %s
#                 """, (project_id, fearure_name_navigation))
#                 existing_nav = cur.fetchone()

#                 if existing_nav:
#                     cur.execute("""
#                         UPDATE ProjectFiles
#                         SET s3_link = %s
#                         WHERE file_id = %s
#                     """, (url_file_navigation, existing_nav[0]))
#                 else:
#                     nav_file_id = str(uuid.uuid4())
#                     cur.execute("""
#                         INSERT INTO ProjectFiles (file_id, project_id, filename, s3_link)
#                         VALUES (%s, %s, %s, %s)
#                     """, (nav_file_id, project_id, fearure_name_navigation, url_file_navigation))
#                 file_id_dict[fearure_name_qaspec] = csa_file_id
#                 file_id_dict[fearure_name_navigation] = nav_file_id
#                 # Convert dict to JSON string
#                 file_id_json_str = json.dumps(file_id_dict) # Convert dict to JSON string
#                 # Update ProjectLinks with CSA file_id
#                 cur.execute("""
#                     UPDATE ProjectLinks
#                     SET file_id = %s
#                     WHERE project_id = %s AND link_id = %s
#                 """, (file_id_json_str, project_id, url_id))
#             conn.commit()

#         time.sleep(10)
#         return {
#             "status": "success",
#             "file_url": url_file,
#             "navigation_url": url_file_navigation
#         }

#     except Exception as e:
#         return JSONResponse(status_code=500, content={"status": "error", "detail": str(e)})



@app.get("/api/start-a2a/{project_id}/{url_id}")
async def trigger_a2a(project_id: str, url_id: str, request: Request):
    feature = "CSA"
    print("qaspec")
    message = "Generate a QA spec for the project"
    print(message)
    url_fetch="https://smt-serco-web-qa.azurewebsites.net/csa"
    await client_agent.main_qa_spec(url_fetch, project_id, url_id)
    # URL base and final output URLs
    base_url = str(request.base_url).rstrip("/")  # e.g., http://127.0.0.1:8000
    url_file = f"{base_url}/{feature}/{feature}_Unified_QA_Spec.md"
    url_file_navigation = f"{base_url}/openaisdk/output_flow/browseruse_agent_data/navigation.md"

    # Feature names
    feature_name_qaspec = f"{feature}_qaspec"
    feature_name_navigation = f"{feature}_navigation"

    # try:
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            # QASPEC
            cur.execute("""
                SELECT file_id FROM ProjectFiles
                WHERE project_id = %s AND filename = %s
            """, (project_id, feature_name_qaspec))
            existing_csa = cur.fetchone()

            if existing_csa:
                csa_file_id = existing_csa[0]
                cur.execute("""
                    UPDATE ProjectFiles
                    SET s3_link = %s
                    WHERE file_id = %s
                """, (url_file, csa_file_id))
            else:
                csa_file_id = str(uuid.uuid4())
                cur.execute("""
                    INSERT INTO ProjectFiles (file_id, project_id, filename, s3_link)
                    VALUES (%s, %s, %s, %s)
                """, (csa_file_id, project_id, feature_name_qaspec, url_file))

            # NAVIGATION
            cur.execute("""
                SELECT file_id FROM ProjectFiles
                WHERE project_id = %s AND filename = %s
            """, (project_id, feature_name_navigation))
            existing_nav = cur.fetchone()

            if existing_nav:
                nav_file_id = existing_nav[0]
                cur.execute("""
                    UPDATE ProjectFiles
                    SET s3_link = %s
                    WHERE file_id = %s
                """, (url_file_navigation, nav_file_id))
            else:
                nav_file_id = str(uuid.uuid4())
                cur.execute("""
                    INSERT INTO ProjectFiles (file_id, project_id, filename, s3_link)
                    VALUES (%s, %s, %s, %s)
                """, (nav_file_id, project_id, feature_name_navigation, url_file_navigation))

            # ✅ Store as JSONB
            file_id_dict = {
                "qaspec": csa_file_id,
                "navigation": nav_file_id
            }

            cur.execute("""
                UPDATE ProjectLinks
                SET file_id = %s
                WHERE project_id = %s AND link_id = %s
            """, (Json(file_id_dict), project_id, url_id))

        conn.commit()

    time.sleep(10)
    return {
        "status": "success",
        "file_url": url_file,
        "navigation_url": url_file_navigation
    }

    # except Exception as e:
    #     return JSONResponse(status_code=500, content={"status": "error", "detail": str(e)})

# npx @playwright/mcp@latest --user-data-dir "C:\Users\<USER>\Documents\GitHub\MCP-Project-FullStack-local\MCP-Project-FullStack\mcp-fastapi\Grab-Url\profile" --port 8937



@app.get("/automation/{project_id}/{url_id}")
async def automation(project_id: str, url_id: str):
    async def event_generator():
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) = 0 AS all_approved
                FROM TestSteps ts
                JOIN TestCases tc ON ts.testcase_id = tc.testcase_id
                WHERE tc.project_id = %s
                AND tc.link_id = %s
                AND ts.approved = FALSE;
                """, (project_id, url_id))
            
            result = cursor.fetchone()
            all_approved = result[0]  # True or False
                
        if not all_approved:
                yield {"data": "Approve all test cases before starting automation."}
                return
        
        print("Automation")
        typescript_scripts=[]
        start_time_automation = time.time()
        
        # try:
        yield {"data": "Automation started..."}
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT p.project_name, pl.url
                    FROM Projects p
                    JOIN ProjectLinks pl ON p.project_id = pl.project_id
                    WHERE p.project_id = %s AND pl.link_id = %s
                """, (project_id, url_id))

                result = cur.fetchone()
                
                if result:
                    project_name, project_url = result
                    print("Project Name:", project_name)
                    print("Project URL:", project_url)
                else:
                    print("No matching project/link found")


        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                SELECT ts.*
                FROM TestSteps ts
                JOIN TestCases tc ON ts.testcase_id = tc.testcase_id
                JOIN Projects p ON tc.project_id = p.project_id
                JOIN ProjectLinks pl ON tc.link_id = pl.link_id
                WHERE p.project_name = %s AND pl.url = %s
                """, (project_name, project_url))
                rows = cur.fetchall()
                columns = [desc[0] for desc in cur.description]
                test_steps_data = [dict(zip(columns, row)) for row in rows]
                print("test_steps_data", test_steps_data)


        # test_steps_data = test_steps_data.to_dict(orient='records')
        

        detailed_test_steps=[]
        test_cases=None
        for index, row in enumerate(test_steps_data):
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT 1 FROM AutomationScripts WHERE step_id = %s ", (row["step_id"],))
                    existing_script = cur.fetchone() is not None
                    print("test_steps_data", test_steps_data)

            if existing_script:
                logger.info(f"Automation script already exists for step_id: {row['step_id']}, skipping.")
                continue
            else:
                logger.info(f"Generating automation scripts for test case no. : {index+1}")
                test_steps = row["steps"]
                if isinstance(test_steps, list):
                        test_steps = "\n".join(test_steps)
                elif isinstance(test_steps, str):
                    steps_list = [step.strip() for step in test_steps.split(";") if step.strip()]
                    test_steps = "\n".join(steps_list)
                docs = ask_playwright_docs(test_steps)
                if docs is None:
                    raise ValueError(f"Documentation retrieval failed for test steps: {test_steps}")
                refine_prompt = f"{test_steps}\n\n{docs}"
                references = add_instructions(refine_prompt)
                config_file = "mcp-playwright.json"
                client = MCPClient.from_config_file(config_file)
                llm = ChatOpenAI(
                    model="o4-mini-2025-04-16",
                    temperature=1,
                    reasoning_effort="low",
                )
                agent = MCPAgent(
                    llm=llm,
                    client=client,
                    max_steps=30,
                    memory_enabled=True,
                    system_prompt=build_system_prompt()
                )
                response = await agent.run(f"\n\nUser Instructions:\n{references.strip()}")
                # total_tokens = count_total_tokens(build_system_prompt(),references.strip(), model="gpt-4.1-mini")
                # print("Estimated prompt token usage:", total_tokens)
                # output_tokens=count_total_output_tokens(response,model="gpt-4.1-mini")
                # print("output tokens",output_tokens)
                # cost=calculate_o4_mini_cost(total_tokens, output_tokens)
                # print ("######",cost)
                code = extract_typescript_code(response)
                # step = {
                #     "No": row["No"],
                #     "Test Description": row["Test Description"]
                # }
                # detailed_test_steps.append(step)

                # Save TypeScript code to file    

                if code:
                    logger.info(f"Extracted TypeScript code:")
                    test_results = save_and_run_ts_code(code, index)

                    with get_db_connection() as conn:
                        with conn.cursor() as cur:
                            script_id = str(uuid.uuid4())
                            timestamp = datetime.now()
                            cur.execute("""
                                INSERT INTO AutomationScripts (script_id, step_id, language, code, created_at, folder_path)
                                VALUES (%s, %s, %s, %s, %s, %s)
                            """, (script_id, row["step_id"], "TypeScript", code, timestamp, "tests"))
                        conn.commit()
                    # Run Playwright tests
                    # stdout_log, stderr_log = run_playwright_tests(index+1)
                    # if stderr_log:
                    #     errors.append(f"Playwright test execution error: {stderr_log}")
                    # # Open Playwright report
                    # open_playwright_report()
                    # cleaned_output=clean_playwright_output(stdout_log)

                    with get_db_connection() as conn:
                        with conn.cursor() as cur:
                            cur.execute("""
                                SELECT script_id FROM AutomationScripts WHERE step_id = %s
                            """, (row['step_id'],))
                            result = cur.fetchone()
                            script_id= result[0] if result else None
                            
                                
                    with get_db_connection() as conn:
                        with conn.cursor() as cur:
                            cur.execute("""
                                SELECT tc.name
                                FROM TestSteps ts
                                JOIN TestCases tc ON ts.testcase_id = tc.testcase_id
                                WHERE ts.step_id = %s
                            """, (row['step_id'],))
                            result = cur.fetchone()
                            name= result[0] if result else None
                
                    typescript_scripts.append({
                        'script_id': script_id,
                        'test_description': name,
                    })
                
                else:
                    logger.info(f"No TypeScript code generated for test case {row['step_id']}")
        
        
        run_id = start_test_run(project_name, project_url)
        print("Run ID:", run_id)

        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT a.script_id
                    FROM Projects p
                    JOIN ProjectLinks pl ON p.project_id = pl.project_id
                    JOIN TestCases tc ON p.project_id = tc.project_id AND pl.link_id = tc.link_id
                    JOIN TestSteps ts ON tc.testcase_id = ts.testcase_id
                    JOIN AutomationScripts a ON ts.step_id = a.step_id
                    WHERE p.project_name = %s AND pl.url = %s
                """, (project_name, project_url))
            
                rows = cur.fetchall()
                script_ids = [row[0] for row in rows]
            
                
        # script_ids = fetch_script_ids_for_project_url(project_name, project_url)

        start_time = time.time()

        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT DISTINCT a.folder_path
                    FROM AutomationScripts a
                    JOIN TestSteps ts ON a.step_id = ts.step_id
                    JOIN TestCases tc ON ts.testcase_id = tc.testcase_id
                    JOIN ProjectLinks pl ON tc.link_id = pl.link_id
                    JOIN Projects p ON tc.project_id = p.project_id
                    WHERE p.project_name = %s
                    AND pl.url = %s
                    LIMIT 1;
                """, (project_name, project_url))
                path_result = cur.fetchone()
                path=path_result[0] if path_result else None
                

        print(path)
        stdout_log, stderr_log = run_playwright_tests(path)
        print("stdout_log", stdout_log)
        # if stderr_log:
        #     logger.info(f"Playwright test execution error: {stderr_log}")
        # # cleaned_output=clean_playwright_output(stdout_log)
        # end_time = time.time()
        # result=list(result)
        # result.append({
        #     'typescript_code': cleaned_output,
        # }) 
        # duration = int(end_time - start_time)

        open_playwright_report()

        source="playwright-report/index.html"
        target_dir="playwright-report"
        # Resolve the calling script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # Convert to absolute paths
        source_path = os.path.abspath(os.path.join(script_dir, source))
        target_path = os.path.abspath(os.path.join(script_dir, target_dir))

        # Check if source file exists
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"Report source file not found: {source_path}")

        # Check if target dir exists
        if not os.path.isdir(target_path):
            raise NotADirectoryError(f"Target directory does not exist: {target_path}")

        # New file name and path
        new_filename = f"testresult_{run_id}.html"
        result_file_path = os.path.join(target_path, new_filename)

        # Copy and rename the report
        shutil.copy(source_path, result_file_path)

        
        # result_file_path = copy_result_html.copy_playwright_report(run_id)
        upload_link=upload_to_s3(result_file_path)

        base_dir_json = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))  # goes to mcp-fastapi
        script_path_json = os.path.join(base_dir_json, 'mcp-fastapi','playwright-report','custom-reports','report.json')

        with open(script_path_json, "r", encoding="utf-8") as f:
            result_json = json.load(f)

        print("result_json", type(result_json))
        

        insert_test_result(run_id, script_ids, upload_link,json.dumps(result_json), duration_seconds=None)
        update_test_run_end_time(run_id)
            
        # except Exception as e:
        #     logger.info(f"Error generating TypeScript scripts: {str(e)}")

        end_time_automation = time.time()
        duration_automation = (end_time_automation - start_time_automation) / 60
        logger.info(f"Automation scripts generation completed in {duration_automation:.2f} minutes")
        yield {"data": "All steps completed"}
            
    return EventSourceResponse(event_generator())
    



#create persistent profile api
@app.get("/create-persistent-profile/{project_id}")
def create_persistent_profile(project_id: str):
    print("project_id", project_id)
    print("Create Persistent Profile")
    try:
        ensure_profile_sync()
    except KeyboardInterrupt:
        print("\n⏹️ Profile creation interrupted by user")
    except Exception as e:
        print(f"❌ Failed to create profile: {str(e)}")
        sys.exit(1)
    return {"status": "success", "message": "Persistent profile created..."}

@app.post("/get-persistent-profile/{project_id}")
def get_persistent_profile(project_id: str):
    print("project_id", project_id)
    print("Get Persistent Profile")
    return {"status": "success", "message": "Persistent profile created..."}



#############markdown editing

class MarkdownUpdateRequest(BaseModel):
    content: str

@app.patch("/update-markdown-file/{file_id}/{link_id}/{project_id}")
def update_markdown_file(file_id: str, link_id: str, project_id: str, req: MarkdownUpdateRequest):
    print("file_id", file_id)
    print("link_id", link_id)
    print("project_id", project_id)
    
    # Validate that we have non-empty parameters
    if not file_id or not link_id or not project_id:
        raise HTTPException(status_code=400, detail="Missing required parameters")
    
    """
    Update the content of a markdown file in ProjectFiles.
    """
    try:
        # Fetch file info
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT s3_link FROM ProjectFiles WHERE file_id = %s", (file_id,))
                row = cur.fetchone()
                if not row:
                    raise HTTPException(status_code=404, detail="File not found")
                s3_link = row[0]

                # Convert string parameters to UUID for database query
                try:
                    project_uuid = UUID(project_id)
                    link_uuid = UUID(link_id)
                except ValueError as e:
                    raise HTTPException(status_code=400, detail=f"Invalid UUID format: {e}")

                cur.execute("""
                    SELECT spec_id FROM QASpecs
                    WHERE project_id = %s AND link_id = %s
                """, (str(project_uuid), str(link_uuid)))
                result = cur.fetchone()
                if not result:
                    raise HTTPException(status_code=404, detail=f"QA Spec not found for project_id={project_id} and link_id={link_id}")
        
                spec_id = result[0]
        
                # Write new content to the file (assuming s3_link is a local file path or accessible URL)
                # If s3_link is a URL, you need to handle upload to S3 or your storage.
                # For local file:
                parsed = urlparse(s3_link)
                relative_path = parsed.path.lstrip("/")
                # try:
                with open(relative_path, "w", encoding="utf-8") as f:
                    f.write(req.content)

                # except Exception as e:
                #     raise HTTPException(status_code=500, detail=f"Failed to write file: {e}")
                
                with get_db_connection() as conn:
                    with conn.cursor() as cur:
                        cur.execute("""
                            UPDATE QASpecs
                            SET spec_content = %s, updated_at = NOW()
                            WHERE spec_id = %s
                        """, (req.content, str(spec_id)))
                        conn.commit()

        return {"message": "Markdown file saved!"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class QASpecApprovalByLink(BaseModel):
    project_id: str  # Changed from UUID to str to match path parameter
    link_id: str     # Changed from UUID to str to match path parameter
    approved_at: datetime = None
    
@app.post("/approve-markdown-file/{project_id}/{link_id}")
def approve_qa_spec_by_link(project_id: str, link_id: str, spec: QASpecApprovalByLink = None):
    print("project_id", project_id)
    print("link_id", link_id)
    
    # Validate that we have non-empty parameters
    if not project_id or not link_id:
        raise HTTPException(status_code=400, detail="Missing required parameters")
    
    try:
        # Convert string parameters to UUID for database query
        try:
            project_uuid = UUID(project_id)
            link_uuid = UUID(link_id)
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid UUID format: {e}")
            
        # Use current time if not provided in request body
        approved_at = datetime.utcnow()
        if spec and spec.approved_at:
            approved_at = spec.approved_at
            
        with get_db_connection() as conn:
            with conn.cursor() as cur:
 
                # Fetch the spec_id using project_id and link_id
                cur.execute("""
                    SELECT spec_id FROM QASpecs
                    WHERE project_id = %s AND link_id = %s
                """, (str(project_uuid), str(link_uuid)))
        
                row = cur.fetchone()
                if not row:
                    raise HTTPException(status_code=404, detail=f"QA Spec not found for project_id={project_id} and link_id={link_id}")
        
                spec_id = row[0]
        
                # Approve the spec
                cur.execute("""
                    UPDATE QASpecs
                    SET approved = TRUE,
                        approved_at = %s,
                        updated_at = NOW()
                    WHERE spec_id = %s
                """, (approved_at, str(spec_id)))
        
                conn.commit()
        
                return {"message": "QA Spec approved", "spec_id": spec_id}
 
    except HTTPException:
        raise
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    

####################################################################
@app.get("/api/testresults", response_model=List[dict])
def get_all_test_results():
    query = """
        SELECT result_id, run_id, executed_at, duration_seconds, remarks
        FROM TestResults
        ORDER BY executed_at DESC
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query)
                return cur.fetchall()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
@app.get("/api/testresults/{result_id}/{run_id}/details", response_model=List[dict])
def get_detailed_results(result_id: str, run_id: str):
    query = """
        SELECT id, test_name, file_name, status, error_message, stack_trace,
               start_time, duration_ms, project_name, retry
        FROM DetailedTestResults
        WHERE report_id = %s AND run_id = %s
        ORDER BY start_time DESC
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query, (result_id, run_id))
                return cur.fetchall()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



###################################################################### ✅ POST /features — Create Feature

class FeatureCreateRequest(BaseModel):
    project_id: str
    feature_name: str
    description: Optional[str] = ""
@app.post("/api/features")
def create_feature(data: FeatureCreateRequest):
    conn = get_db_connection()
    cur = conn.cursor()

    try:
        feature_id = str(uuid.uuid4())

        # Check for duplicate feature name in same project
        cur.execute("""
            SELECT 1 FROM Features WHERE project_id = %s AND feature_name = %s
        """, (data.project_id, data.feature_name))
        if cur.fetchone():
            raise HTTPException(status_code=400, detail="Feature name already exists in this project")

        # Insert into Features table
        cur.execute("""
            INSERT INTO Features (feature_id, project_id, feature_name, description, created_at)
            VALUES (%s, %s, %s, %s, %s)
        """, (
            feature_id,
            data.project_id,
            data.feature_name,
            data.description,
            datetime.utcnow()
        ))

        conn.commit()
        return {
            "feature_id": feature_id,
            "feature_name": data.feature_name
        }

    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        cur.close()
        conn.close()

@app.get("/api/projects/{project_id}/links")
def get_project_links(project_id: str):
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute("""
            SELECT link_id, url, page_title, feature_id
            FROM ProjectLinks
            WHERE project_id = %s
        """, (project_id,))
        rows = cur.fetchall()

        result = [
            {
                "id": row[0],
                "url": row[1],
                "title": row[2],
                "feature_id": row[3]
            }
            for row in rows
        ]
        return result
    except Exception as e:
        print("❌ Error in get_project_links:", e)
        return []
    finally:
        cur.close()
        conn.close()
class LinkFeatureAssignment(BaseModel):
    link_id: UUID
    feature_id: UUID

class AssignmentPayload(BaseModel):
    assignments: List[LinkFeatureAssignment]
@app.post("/api/links/assign-features")
def assign_links_to_features(payload: AssignmentPayload):
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        update_query = """
            UPDATE ProjectLinks
            SET feature_id = %s
            WHERE link_id = %s
        """
        batch_data = [(str(item.feature_id), str(item.link_id)) for item in payload.assignments]
        execute_batch(cur, update_query, batch_data)

        conn.commit()
        return {"status": "success", "updated": len(batch_data)}
    except Exception as e:
        print("❌ Error during DB update:", e)
        raise HTTPException(status_code=500, detail="Failed to update group assignments.")
    finally:
        cur.close()
        conn.close()


class Feature(BaseModel):
    feature_id: str
    feature_name: str
    description: str | None = None
    created_at: datetime  # ⬅️ Use datetime, not str
@app.get("/api/projects/{project_id}/features", response_model=List[Feature])
def get_features(project_id: str):
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT feature_id, feature_name, description, created_at FROM Features WHERE project_id = %s",
                    (project_id,)
                )
                rows = cur.fetchall()
                features = [
                    {
                        "feature_id": row[0],
                        "feature_name": row[1],
                        "description": row[2],
                        "created_at": row[3]
                    }
                    for row in rows
                ]
                return features
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# delete group links
@app.delete("/api/groups/{link_id}/links.delete")
def delete_group_links(link_id: str):
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute("""UPDATE ProjectLinks
            SET feature_id = %s
            WHERE link_id = %s""", (None,link_id))
        # cur.execute("DELETE FROM ProjectLinks WHERE link_id = %s", (link_id,))
        conn.commit()
        return {"status": "success", "message": "Link deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        cur.close()
        conn.close()




###################qaspec page
@app.get("/api/groups-with-link-count/{project_id}")
def get_groups_with_link_count(project_id: str):
    """
    Return groups (features) along with count of links in each group.
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT 
                        f.feature_id,
                        f.feature_name,
                        COUNT(pl.link_id) AS link_count
                    FROM Features f
                    LEFT JOIN ProjectLinks pl ON f.feature_id = pl.feature_id
                    WHERE f.project_id = %s
                    GROUP BY f.feature_id, f.feature_name
                    ORDER BY f.feature_name
                """, (project_id,))
                
                rows = cur.fetchall()
                return [
                    {
                        "feature_id": row[0],
                        "feature_name": row[1],
                        "link_count": row[2]
                    }
                    for row in rows
                ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching group data: {e}")


@app.get("/api/groups/{feature_id}/qaspecs")
def get_qaspecs_by_group(feature_id: str):
    """
    Return QA Specs for all links under a feature group (group = feature).
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Get all link_ids under this feature
                cur.execute("""
                    SELECT pl.link_id
                    FROM ProjectLinks pl
                    WHERE pl.feature_id = %s
                """, (feature_id,))
                link_ids = [row[0] for row in cur.fetchall()]

                if not link_ids:
                    return {"message": "No links under this group", "qaspecs": []}

                # Get QASpecs for those links
                cur.execute("""
                    SELECT q.spec_id, q.link_id, q.spec_content, q.generated, q.approved,
                           q.generated_at, q.updated_at
                    FROM QASpecs q
                    WHERE q.link_id = ANY(%s)
                """, (link_ids,))
                qaspecs = cur.fetchall()

                return [
                    {
                        "spec_id": row[0],
                        "link_id": row[1],
                        "spec_content": row[2],
                        "generated": row[3],
                        "approved": row[4],
                        "generated_at": row[5],
                        "updated_at": row[6]
                    }
                    for row in qaspecs
                ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching QA Specs: {e}")
