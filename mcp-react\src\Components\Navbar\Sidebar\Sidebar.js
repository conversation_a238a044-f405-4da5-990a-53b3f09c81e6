import React, { useState, useEffect } from 'react';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';

function Sidebar({ activeProjectId, setActiveProjectId }) {
  const [show, setShow] = useState(false);
  const [appName, setAppName] = useState('');
  const [projectNames, setProjectNames] = useState([]);
  const [flashMsg, setFlashMsg] = useState(null);
  const [editProjectId, setEditProjectId] = useState(null);
  const [editProjectName, setEditProjectName] = useState('');
  const [projectUrl, setProjectUrl] = useState('');
  const [editProjectUrl, setEditProjectUrl] = useState('');

  useEffect(() => {
    fetch('http://127.0.0.1:8000/api/projects/names')
      .then(res => {
        if (!res.ok) throw new Error('Network response was not ok');
        return res.json();
      })
      .then(data => {
        if (data && Array.isArray(data.project_names)) {
          setProjectNames(data.project_names);
          setProjectUrl(data.project_names[0].project_url);
          // Only set the first project as active if not already set
          if (data.project_names.length > 0 && !activeProjectId) {
            setActiveProjectId(data.project_names[0].project_id);
          }
        } else {
          setProjectNames([]);
        }
      })
      .catch((err) => {
        console.error('Failed to fetch project names:', err);
        setProjectNames([]);
      });
  }, [setActiveProjectId, activeProjectId]);

  // Initialize Bootstrap dropdowns after component mounts and updates
  useEffect(() => {
    // Add debugging
    console.log('Bootstrap check:', typeof window !== 'undefined' && window.bootstrap);
    console.log('Bootstrap object:', window.bootstrap);
    
    const initializeDropdowns = () => {
      const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');
      console.log('Found dropdown elements:', dropdownElements.length);
      
      dropdownElements.forEach((element, index) => {
        console.log(`Initializing dropdown ${index}:`, element);
        if (window.bootstrap && window.bootstrap.Dropdown) {
          if (!window.bootstrap.Dropdown.getInstance(element)) {
            try {
              new window.bootstrap.Dropdown(element);
              console.log(`Successfully initialized dropdown ${index}`);
            } catch (error) {
              console.error(`Failed to initialize dropdown ${index}:`, error);
            }
          }
        }
      });
    };

    // Try to initialize immediately
    if (typeof window !== 'undefined' && window.bootstrap) {
      initializeDropdowns();
    } else {
      // If Bootstrap isn't loaded yet, wait a bit and try again
      const timeout = setTimeout(() => {
        console.log('Retrying Bootstrap initialization...');
        if (window.bootstrap) {
          initializeDropdowns();
        } else {
          console.error('Bootstrap is still not loaded');
        }
      }, 500);
      
      return () => clearTimeout(timeout);
    }
  }, [projectNames]); // Re-run when projectNames changes

  const handleInputChange = (e) => {
    setAppName(e.target.value);
  };

  const handleSubmit = () => {
    if (!appName.trim()) {
      setFlashMsg({ type: 'warning', text: 'Please enter an application name.' });
      return;
    }
  
    fetch('http://127.0.0.1:8000/projects/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        project_name: appName,
        description: '',
        project_url: projectUrl
      })
    })
      .then(async res => {
        let data;
        try {
          data = await res.json();
        } catch {
          data = {};
        }
  
        if (!res.ok && data.detail === "Project name already exists") {
          setFlashMsg({ type: 'warning', text: "Project name already exists." });
        } else if (res.ok) {
          setFlashMsg({ type: 'success', text: 'Application added successfully!' });
          setProjectNames(prev => [{...data}, ...prev]);
        } else {
          setFlashMsg({ type: 'danger', text: data.detail || 'Failed to add application' });
        }
  
        setAppName('');
        setProjectUrl('');
        setTimeout(() => {
          const modalEl = document.getElementById('addAppModal');
          if (window.bootstrap && window.bootstrap.Modal && modalEl) {
            const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);
            modal.hide();
          }
        }, 100);
      })
      .catch(() => {
        setFlashMsg({ type: 'danger', text: 'Failed to add application' });
        setTimeout(() => {
          const modalEl = document.getElementById('addAppModal');
          if (window.bootstrap && window.bootstrap.Modal && modalEl) {
            const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);
            modal.hide();
          }
        }, 100);
      });
  };

  // Open Edit Modal and set project info
  const openEditModal = (projectId, projectName, projectUrl = '') => {
    setEditProjectId(projectId);
    setEditProjectName(projectName);
    setEditProjectUrl(projectUrl);
  
    setTimeout(() => {
      const modalEl = document.getElementById('editAppModal');
      if (modalEl && window.bootstrap && window.bootstrap.Modal) {
        const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);
        modal.show();
      }
    }, 100);
  };
  

  // Handle edit input change
  const handleEditInputChange = (e) => {
    setEditProjectName(e.target.value);
  };

  // Handle update project
  const handleUpdateProject = () => {
    if (!editProjectName.trim()) {
      setFlashMsg({ type: 'warning', text: 'Please enter an application name.' });
      return;
    }
  
    fetch(`http://127.0.0.1:8000/projects/${editProjectId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        project_name: editProjectName,
        description: '',
        project_url: editProjectUrl
      })
    })
      .then(async res => {
        let data;
        try {
          data = await res.json();
        } catch {
          data = {};
        }
  
        if (!res.ok) {
          setFlashMsg({ type: 'warning', text: data.detail || "Failed to update project." });
        } else {
          setFlashMsg({ type: 'success', text: 'Application updated successfully!' });
          setProjectNames(prev => prev.map(p =>
            p.project_id === editProjectId
              ? { ...p, project_name: editProjectName, project_url: editProjectUrl }
              : p
          ));
        }
  
        resetEditForm();
      })
      .catch(() => {
        setFlashMsg({ type: 'danger', text: 'Failed to update application' });
        resetEditForm();
      });
  };
  

  // Reset edit modal state and close modal
  const resetEditForm = () => {
    setEditProjectId(null);
    setEditProjectName('');
    setTimeout(() => {
      const modalEl = document.getElementById('editAppModal');
      if (window.bootstrap && window.bootstrap.Modal && modalEl) {
        const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);
        modal.hide();
      }
    }, 100);
  };

  // Handle delete project with confirmation
  const handleDeleteProject = (projectId, projectName) => {
    setFlashMsg({
      type: 'warning',
      text: (
        <>
          Are you sure you want to delete <b>{projectName}</b>?
          <div className="mt-2 d-flex gap-2 justify-content-end">
            <button
              className="btn btn-sm btn-outline-secondary"
              onClick={() => setFlashMsg(null)}
            >
              Cancel
            </button>
            <button
              className="btn btn-sm btn-danger"
              onClick={() => confirmDeleteProject(projectId)}
            >
              Delete
            </button>
          </div>
        </>
      )
    });
  };

  // Actually delete the project
  const confirmDeleteProject = (projectId) => {
    setFlashMsg(null);
    fetch(`http://127.0.0.1:8000/projects/${projectId}`, {
      method: 'DELETE',
    })
      .then(async res => {
        let data;
        try {
          data = await res.json();
        } catch {
          data = {};
        }
        if (!res.ok) {
          setFlashMsg({ type: 'danger', text: data.detail || 'Failed to delete application' });
        } else {
          setFlashMsg({ type: 'success', text: 'Application deleted successfully!' });
          setProjectNames(prev => prev.filter(p => p.project_id !== projectId));
          // If active project was deleted, set active to first available or null
          if (activeProjectId === projectId) {
            const firstProject = projectNames.find(p => p.project_id !== projectId);
            setActiveProjectId(firstProject ? firstProject.project_id : null);
          }
        }
      })
      .catch(() => {
        setFlashMsg({ type: 'danger', text: 'Failed to delete application' });
      });
  };

  // Handle project item click (prevent dropdown from interfering)
  const handleProjectClick = (projectId) => {
    console.log(projectId);
    setActiveProjectId(projectId);
  };

  // Handle dropdown menu clicks
  const handleDropdownClick = (e, action, projectId, projectName, projectUrl = '') => {
    e.preventDefault();
    e.stopPropagation();
  
    const dropdownButton = e.target.closest('.dropdown').querySelector('[data-bs-toggle="dropdown"]');
    if (dropdownButton && window.bootstrap && window.bootstrap.Dropdown) {
      const dropdown = window.bootstrap.Dropdown.getInstance(dropdownButton);
      if (dropdown) {
        dropdown.hide();
      }
    }
  
    if (action === 'edit') {
      openEditModal(projectId, projectName, projectUrl);
    } else if (action === 'delete') {
      handleDeleteProject(projectId, projectName);
    }
  };
  

  // Alternative click handler for testing
  const handleDropdownToggle = (e) => {
    e.stopPropagation();
    console.log('Dropdown toggle clicked');
    
    const dropdownButton = e.currentTarget;
    console.log('Dropdown button:', dropdownButton);
    
    if (window.bootstrap && window.bootstrap.Dropdown) {
      const dropdown = window.bootstrap.Dropdown.getInstance(dropdownButton) || new window.bootstrap.Dropdown(dropdownButton);
      console.log('Dropdown instance:', dropdown);
      dropdown.toggle();
    } else {
      console.error('Bootstrap Dropdown not available');
    }
  };

  // Sidebar classes for responsive behavior
  const sidebarClass =
    'd-flex flex-column flex-shrink-0 p-2 border-end position-fixed h-100 m-2 rounded border border-2' +
    ' ' +
    (show ? 'd-block' : 'd-none d-md-flex');

  return (
    <>
      {/* Flash message */}
      {flashMsg && (
        <div
          className={`alert alert-${flashMsg.type} alert-dismissible show position-fixed top-0 start-50 translate-middle-x mt-3 shadow `}
          style={{
            zIndex: 2000,
            minWidth: 320,
            maxWidth: 500,
            borderRadius: 12,
            border: 'none',
            background:
              flashMsg.type === 'success'
                ? 'linear-gradient(90deg, #d1fae5 0%, #bbf7d0 100%)'
                : flashMsg.type === 'danger'
                ? 'linear-gradient(90deg, #fee2e2 0%, #fecaca 100%)'
                : flashMsg.type === 'warning'
                ? 'linear-gradient(90deg, #fef9c3 0%, #fde68a 100%)'
                : '#f9f9f9',
            color:
              flashMsg.type === 'success'
                ? '#065f46'
                : flashMsg.type === 'danger'
                ? '#991b1b'
                : flashMsg.type === 'warning'
                ? '#92400e'
                : '#222',
            fontWeight: 500,
            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.18)',
            padding: '1rem 1.5rem',
            display: 'flex',
            alignItems: 'center',
            gap: 12,
          }}
          role="alert"
        >
          <span style={{ flex: 1 }}>{flashMsg.text}</span>
          <button
            type="button"
            className="btn-close"
            aria-label="Close"
            style={{
              filter: 'invert(0.5)',
              opacity: 0.7,
              marginLeft: 8,
            }}
            onClick={() => setFlashMsg(null)}
          ></button>
        </div>
      )}
      
      {/* Hamburger for mobile */}
      <button
        className="btn btn-outline-secondary d-md-none m-2 position-fixed"
        style={{ zIndex: 400 }}
        onClick={() => setShow(!show)}
      >
        <i className="bi bi-list"></i>
      </button>

      {/* Sidebar */}
      <nav
        className={sidebarClass}
        style={{
          width: 280,
          top: 0,
          left: 0,
          zIndex: 1040,
          transition: 'transform 0.3s ease-in-out',
          backgroundColor: 'white'
        }}
      >
        {/* X Close Button for mobile */}
        {show && (
          <button
            className="btn btn-outline-secondary d-md-none ms-auto mb-2"
            style={{ position: 'absolute', top: 10, right: 10, zIndex: 1060 }}
            onClick={() => setShow(false)}
            aria-label="Close sidebar"
          >
            <i className="bi bi-x-lg"></i>
          </button>
        )}
        
        {/* Add New Application */}
        <button
          className="btn btn-outline-dark d-flex align-items-center mb-2 w-100 fs-6"
          data-bs-toggle="modal"
          data-bs-target="#addAppModal"
          style={{
            marginTop: show ? 60 : 0
          }}
        >
          <i className="bi bi-folder-plus me-2"></i>
          Add New Application
        </button>

        {/* Application List */}
        <ul className="nav nav-pills flex-column mb-auto">
          {projectNames.length === 0 ? (
            <li className="nav-item fs-6 text-muted px-3 py-2">No Applications</li>
          ) : (
            projectNames.map((proj, idx) => (
              <li className="nav-item fs-6 position-relative m-1 " key={proj.project_id || idx}>
                <button
                  type="button"
                  className={
                    "nav-link d-flex align-items-center fs-6 btn btn-link w-100 text-start position-relative border pb-2 " +
                    (activeProjectId === proj.project_id ? " active" : " text-dark")
                  }
                  style={
                    activeProjectId === proj.project_id
                      ? { background: '#d3d3d4', color: '#000' }
                      : {}
                  }
                  onClick={() => handleProjectClick(proj.project_id)}
                >
                  <i className="bi bi-folder me-2"></i>
                  <span className="flex-grow-1">{proj.project_name}</span>
                  
                  {/* Three-dot menu */}
                  <div className="dropdown">
                    <button
                      className="btn btn-link p-0 text-dark"
                      type="button"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                      onClick={handleDropdownToggle}
                      style={{ 
                        marginLeft: 'auto',
                        fontSize: '1.2rem',
                        lineHeight: 1
                      }}
                    >
                      <i className="bi bi-three-dots-vertical"></i>
                    </button>
                    <ul className="dropdown-menu dropdown-menu-end">
                      <li>
                        <button
                          className="dropdown-item"
                          onClick={(e) => handleDropdownClick(e, 'edit', proj.project_id, proj.project_name, proj.project_url)}

                        >
                          <i className="bi bi-pencil me-2"></i>Edit
                        </button>
                      </li>
                      <li>
                        <button
                          className="dropdown-item text-danger"
                          onClick={(e) => handleDropdownClick(e, 'delete', proj.project_id, proj.project_name)}
                        >
                          <i className="bi bi-trash me-2"></i>Delete
                        </button>
                      </li>
                    </ul>
                  </div>
                </button>
              </li>
            ))
          )}
        </ul>

        {/* Spacer */}
        <div className="flex-grow-1"></div>

        {/* User Info */}
        <div className="border rounded d-flex align-items-center p-2 mb-2">
          <div
            className="rounded-circle bg-secondary me-2"
            style={{ width: 32, height: 32 }}
          ></div>
          <div>
            <span
              className="badge bg-light text-secondary mb-1"
              style={{ fontSize: '0.7rem' }}
            >
              Free member
            </span>
            <div style={{ fontSize: '1rem' }}>First Last Name</div>
          </div>
          <div className="dropdown ms-auto">
            <button
              className="btn btn-link ms-auto p-0 text-dark"
              type="button"
              id="userMenuDropdown"
              data-bs-toggle="dropdown"
              aria-expanded="false"
              style={{ boxShadow: "none" }}
            >
              <i className="bi bi-three-dots-vertical"></i>
            </button>
            <ul className="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuDropdown">
              <li>
                <button className="dropdown-item" onClick={() => {
                  const link = document.createElement("a");
                  link.href = "/extension.zip";
                  link.download = "extension.zip";
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}>
                  <i className="bi bi-download me-2"></i>Download Extension
                </button>
              </li>
              <li>
                <button className="dropdown-item" onClick={() => alert('Settings clicked')}>
                  <i className="bi bi-gear me-2"></i>Settings
                </button>
              </li>
              <li>
                <button className="dropdown-item text-danger" onClick={() => alert('Logout clicked')}>
                  <i className="bi bi-box-arrow-right me-2"></i>Logout
                </button>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      {/* Overlay for mobile when sidebar is open */}
      {show && (
        <div
          className="position-fixed top-0 start-0 w-100 h-100"
          style={{ background: 'rgba(0,0,0,0.2)', zIndex: 1039 }}
          onClick={() => setShow(false)}
        ></div>
      )}

      {/* Add Application Modal */}
      <div
        className="modal fade"
        id="addAppModal"
        tabIndex="-1"
        aria-labelledby="addAppModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title" id="addAppModalLabel">Add Application</h5>
              <button
                type="button"
                className="btn btn-light rounded-circle d-flex align-items-center justify-content-center shadow-sm ms-auto"
                style={{
                  width: '32px',
                  height: '32px',
                  padding: 0,
                  fontSize: '1rem',
                  border: 'none'
                }}
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                &times;
              </button>

            </div>
            <div className="modal-body">
              <form>
                <label htmlFor="appName" className='mb-2 text-dark'> Application Name:</label>
                <input
                  type="text"
                  className="form-control mb-2 p-3"
                  placeholder="Enter application name..."
                  value={appName}
                  onChange={(e) => setAppName(e.target.value)}
                />

                <label  htmlFor="appIcon" className='mt-2 mb-2 text-dark'>Base URL:</label>
                
                <div className="input-group mb-2">
                  
                  <input
                    type="text"
                    className="form-control p-3"
                    placeholder="Enter base url..."
                    value={projectUrl}
                    onChange={(e) => setProjectUrl(e.target.value)}
                  />
                  
                  <button
                    type="submit"
                    className="btn btn-dark"
                    style={{ whiteSpace: 'nowrap' }}
                    onClick={() => {
                      // Call the generate persistent profile function
                      const encodedUrl = encodeURIComponent(projectUrl || 'default');
                      fetch(`http://127.0.0.1:8000/generate-persistent-profile/${activeProjectId || 'default'}/${encodedUrl}`)
                        .then(response => response.json())
                        .then(data => {
                          console.log('Profile generation response:', data);
                          setFlashMsg({ type: 'success', text: 'Profile generation initiated successfully!' });
                        })
                        .catch(error => {
                          console.error('Error generating profile:', error);
                          setFlashMsg({ type: 'danger', text: 'Failed to generate profile' });
                        });
                    }}
                  >
                    Login
                  </button>
                    
                  </div>
                </form>
            </div>

            <div className="modal-footer">
           
              <button
                type="button"
                className="btn btn-dark me-auto"
                onClick={handleSubmit}
              >
                Submit Application
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Application Modal */}
      <div
        className="modal fade"
        id="editAppModal"
        tabIndex="-1"
        aria-labelledby="editAppModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title" id="editAppModalLabel">Edit Application</h5>
              <button
                type="button"
                className="btn btn-light rounded-circle d-flex align-items-center justify-content-center shadow-sm ms-auto"
                style={{
                  width: '32px',
                  height: '32px',
                  padding: 0,
                  fontSize: '1rem',
                  border: 'none'
                }}
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                &times;
              </button>

            </div>
            <div className="modal-body">
              <label htmlFor="appName" className='mb-2 text-dark'> Application Name:</label>
              <input
                type="text"
                className="form-control mb-2 p-3"
                placeholder="Edit application name..."
                value={editProjectName}
                onChange={(e) => setEditProjectName(e.target.value)}
              />

              <label  htmlFor="appIcon" className='mt-2 mb-2 text-dark'>Base URL:</label>
              <div className="input-group mb-2">
                <input
                  type="text"
                  className="form-control p-3"
                  placeholder="Edit Project URL"
                  value={editProjectUrl}
                  onChange={(e) => setEditProjectUrl(e.target.value)}
                />
                <button
                  type="button"
                  className="btn btn-dark"
                  style={{ whiteSpace: 'nowrap' }}
                  onClick={() => {
                    // Call the generate persistent profile function
                    const encodedUrl = encodeURIComponent(editProjectUrl || 'default');
                    fetch(`http://127.0.0.1:8000/generate-persistent-profile/${editProjectId || 'default'}/${encodedUrl}`)
                      .then(response => response.json())
                      .then(data => {
                        console.log('Profile generation response:', data);
                        setFlashMsg({ type: 'success', text: 'Profile generation initiated successfully!' });
                      })
                      .catch(error => {
                        console.error('Error generating profile:', error);
                        setFlashMsg({ type: 'danger', text: 'Failed to generate profile' });
                      });
                  }}
                >
                  Login
                </button>
              </div>
            </div>

            <div className="modal-footer">
              
              <button
                type="button"
                className="btn btn-dark me-auto"
                onClick={handleUpdateProject}
              >
                Update Application Name
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Sidebar;