{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\MCP-Project-FullStack-local\\\\MCP-Project-FullStack\\\\mcp-react\\\\src\\\\Components\\\\Navbar\\\\Sidebar\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Sidebar({\n  activeProjectId,\n  setActiveProjectId\n}) {\n  _s();\n  const [show, setShow] = useState(false);\n  const [appName, setAppName] = useState('');\n  const [projectNames, setProjectNames] = useState([]);\n  const [flashMsg, setFlashMsg] = useState(null);\n  const [editProjectId, setEditProjectId] = useState(null);\n  const [editProjectName, setEditProjectName] = useState('');\n  const [projectUrl, setProjectUrl] = useState('');\n  const [editProjectUrl, setEditProjectUrl] = useState('');\n  useEffect(() => {\n    fetch('http://127.0.0.1:8000/api/projects/names').then(res => {\n      if (!res.ok) throw new Error('Network response was not ok');\n      return res.json();\n    }).then(data => {\n      if (data && Array.isArray(data.project_names)) {\n        setProjectNames(data.project_names);\n        setProjectUrl(data.project_names[0].project_url);\n        // Only set the first project as active if not already set\n        if (data.project_names.length > 0 && !activeProjectId) {\n          setActiveProjectId(data.project_names[0].project_id);\n        }\n      } else {\n        setProjectNames([]);\n      }\n    }).catch(err => {\n      /* eslint-disable */console.error(...oo_tx(`1105938802_33_8_33_60_11`, 'Failed to fetch project names:', err));\n      setProjectNames([]);\n    });\n  }, [setActiveProjectId, activeProjectId]);\n\n  // Initialize Bootstrap dropdowns after component mounts and updates\n  useEffect(() => {\n    // Add debugging\n    /* eslint-disable */\n    console.log(...oo_oo(`1105938802_41_4_41_86_4`, 'Bootstrap check:', typeof window !== 'undefined' && window.bootstrap));\n    /* eslint-disable */\n    console.log(...oo_oo(`1105938802_42_4_42_54_4`, 'Bootstrap object:', window.bootstrap));\n    const initializeDropdowns = () => {\n      const dropdownElements = document.querySelectorAll('[data-bs-toggle=\"dropdown\"]');\n      /* eslint-disable */\n      console.log(...oo_oo(`1105938802_46_6_46_70_4`, 'Found dropdown elements:', dropdownElements.length));\n      dropdownElements.forEach((element, index) => {\n        /* eslint-disable */console.log(...oo_oo(`1105938802_49_8_49_63_4`, `Initializing dropdown ${index}:`, element));\n        if (window.bootstrap && window.bootstrap.Dropdown) {\n          if (!window.bootstrap.Dropdown.getInstance(element)) {\n            try {\n              new window.bootstrap.Dropdown(element);\n              /* eslint-disable */\n              console.log(...oo_oo(`1105938802_54_14_54_71_4`, `Successfully initialized dropdown ${index}`));\n            } catch (error) {\n              /* eslint-disable */console.error(...oo_tx(`1105938802_56_14_56_77_11`, `Failed to initialize dropdown ${index}:`, error));\n            }\n          }\n        }\n      });\n    };\n\n    // Try to initialize immediately\n    if (typeof window !== 'undefined' && window.bootstrap) {\n      initializeDropdowns();\n    } else {\n      // If Bootstrap isn't loaded yet, wait a bit and try again\n      const timeout = setTimeout(() => {\n        /* eslint-disable */console.log(...oo_oo(`1105938802_69_8_69_59_4`, 'Retrying Bootstrap initialization...'));\n        if (window.bootstrap) {\n          initializeDropdowns();\n        } else {\n          /* eslint-disable */console.error(...oo_tx(`1105938802_73_10_73_56_11`, 'Bootstrap is still not loaded'));\n        }\n      }, 500);\n      return () => clearTimeout(timeout);\n    }\n  }, [projectNames]); // Re-run when projectNames changes\n\n  const handleInputChange = e => {\n    setAppName(e.target.value);\n  };\n  const handleSubmit = () => {\n    if (!appName.trim()) {\n      setFlashMsg({\n        type: 'warning',\n        text: 'Please enter an application name.'\n      });\n      return;\n    }\n    fetch('http://127.0.0.1:8000/projects/', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        project_name: appName,\n        description: '',\n        project_url: projectUrl\n      })\n    }).then(async res => {\n      let data;\n      try {\n        data = await res.json();\n      } catch {\n        data = {};\n      }\n      if (!res.ok && data.detail === \"Project name already exists\") {\n        setFlashMsg({\n          type: 'warning',\n          text: \"Project name already exists.\"\n        });\n      } else if (res.ok) {\n        setFlashMsg({\n          type: 'success',\n          text: 'Application added successfully!'\n        });\n        setProjectNames(prev => [{\n          ...data\n        }, ...prev]);\n      } else {\n        setFlashMsg({\n          type: 'danger',\n          text: data.detail || 'Failed to add application'\n        });\n      }\n      setAppName('');\n      setProjectUrl('');\n      setTimeout(() => {\n        const modalEl = document.getElementById('addAppModal');\n        if (window.bootstrap && window.bootstrap.Modal && modalEl) {\n          const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);\n          modal.hide();\n        }\n      }, 100);\n    }).catch(() => {\n      setFlashMsg({\n        type: 'danger',\n        text: 'Failed to add application'\n      });\n      setTimeout(() => {\n        const modalEl = document.getElementById('addAppModal');\n        if (window.bootstrap && window.bootstrap.Modal && modalEl) {\n          const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);\n          modal.hide();\n        }\n      }, 100);\n    });\n  };\n\n  // Open Edit Modal and set project info\n  const openEditModal = (projectId, projectName, projectUrl = '') => {\n    setEditProjectId(projectId);\n    setEditProjectName(projectName);\n    setEditProjectUrl(projectUrl);\n    setTimeout(() => {\n      const modalEl = document.getElementById('editAppModal');\n      if (modalEl && window.bootstrap && window.bootstrap.Modal) {\n        const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);\n        modal.show();\n      }\n    }, 100);\n  };\n\n  // Handle edit input change\n  const handleEditInputChange = e => {\n    setEditProjectName(e.target.value);\n  };\n\n  // Handle update project\n  const handleUpdateProject = () => {\n    if (!editProjectName.trim()) {\n      setFlashMsg({\n        type: 'warning',\n        text: 'Please enter an application name.'\n      });\n      return;\n    }\n    fetch(`http://127.0.0.1:8000/projects/${editProjectId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        project_name: editProjectName,\n        description: '',\n        project_url: editProjectUrl\n      })\n    }).then(async res => {\n      let data;\n      try {\n        data = await res.json();\n      } catch {\n        data = {};\n      }\n      if (!res.ok) {\n        setFlashMsg({\n          type: 'warning',\n          text: data.detail || \"Failed to update project.\"\n        });\n      } else {\n        setFlashMsg({\n          type: 'success',\n          text: 'Application updated successfully!'\n        });\n        setProjectNames(prev => prev.map(p => p.project_id === editProjectId ? {\n          ...p,\n          project_name: editProjectName,\n          project_url: editProjectUrl\n        } : p));\n      }\n      resetEditForm();\n    }).catch(() => {\n      setFlashMsg({\n        type: 'danger',\n        text: 'Failed to update application'\n      });\n      resetEditForm();\n    });\n  };\n\n  // Reset edit modal state and close modal\n  const resetEditForm = () => {\n    setEditProjectId(null);\n    setEditProjectName('');\n    setTimeout(() => {\n      const modalEl = document.getElementById('editAppModal');\n      if (window.bootstrap && window.bootstrap.Modal && modalEl) {\n        const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);\n        modal.hide();\n      }\n    }, 100);\n  };\n\n  // Handle delete project with confirmation\n  const handleDeleteProject = (projectId, projectName) => {\n    setFlashMsg({\n      type: 'warning',\n      text: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [\"Are you sure you want to delete \", /*#__PURE__*/_jsxDEV(\"b\", {\n          children: projectName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 43\n        }, this), \"?\", /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 d-flex gap-2 justify-content-end\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-sm btn-outline-secondary\",\n            onClick: () => setFlashMsg(null),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-sm btn-danger\",\n            onClick: () => confirmDeleteProject(projectId),\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true)\n    });\n  };\n\n  // Actually delete the project\n  const confirmDeleteProject = projectId => {\n    setFlashMsg(null);\n    fetch(`http://127.0.0.1:8000/projects/${projectId}`, {\n      method: 'DELETE'\n    }).then(async res => {\n      let data;\n      try {\n        data = await res.json();\n      } catch {\n        data = {};\n      }\n      if (!res.ok) {\n        setFlashMsg({\n          type: 'danger',\n          text: data.detail || 'Failed to delete application'\n        });\n      } else {\n        setFlashMsg({\n          type: 'success',\n          text: 'Application deleted successfully!'\n        });\n        setProjectNames(prev => prev.filter(p => p.project_id !== projectId));\n        // If active project was deleted, set active to first available or null\n        if (activeProjectId === projectId) {\n          const firstProject = projectNames.find(p => p.project_id !== projectId);\n          setActiveProjectId(firstProject ? firstProject.project_id : null);\n        }\n      }\n    }).catch(() => {\n      setFlashMsg({\n        type: 'danger',\n        text: 'Failed to delete application'\n      });\n    });\n  };\n\n  // Handle project item click (prevent dropdown from interfering)\n  const handleProjectClick = projectId => {\n    /* eslint-disable */console.log(...oo_oo(`1105938802_275_4_275_26_4`, projectId));\n    setActiveProjectId(projectId);\n  };\n\n  // Handle dropdown menu clicks\n  const handleDropdownClick = (e, action, projectId, projectName, projectUrl = '') => {\n    e.preventDefault();\n    e.stopPropagation();\n    const dropdownButton = e.target.closest('.dropdown').querySelector('[data-bs-toggle=\"dropdown\"]');\n    if (dropdownButton && window.bootstrap && window.bootstrap.Dropdown) {\n      const dropdown = window.bootstrap.Dropdown.getInstance(dropdownButton);\n      if (dropdown) {\n        dropdown.hide();\n      }\n    }\n    if (action === 'edit') {\n      openEditModal(projectId, projectName, projectUrl);\n    } else if (action === 'delete') {\n      handleDeleteProject(projectId, projectName);\n    }\n  };\n\n  // Alternative click handler for testing\n  const handleDropdownToggle = e => {\n    e.stopPropagation();\n    /* eslint-disable */\n    console.log(...oo_oo(`1105938802_303_4_303_42_4`, 'Dropdown toggle clicked'));\n    const dropdownButton = e.currentTarget;\n    /* eslint-disable */\n    console.log(...oo_oo(`1105938802_306_4_306_51_4`, 'Dropdown button:', dropdownButton));\n    if (window.bootstrap && window.bootstrap.Dropdown) {\n      const dropdown = window.bootstrap.Dropdown.getInstance(dropdownButton) || new window.bootstrap.Dropdown(dropdownButton);\n      /* eslint-disable */\n      console.log(...oo_oo(`1105938802_310_6_310_49_4`, 'Dropdown instance:', dropdown));\n      dropdown.toggle();\n    } else {\n      /* eslint-disable */console.error(...oo_tx(`1105938802_313_6_313_55_11`, 'Bootstrap Dropdown not available'));\n    }\n  };\n\n  // Sidebar classes for responsive behavior\n  const sidebarClass = 'd-flex flex-column flex-shrink-0 p-2 border-end position-fixed h-100 m-2 rounded border border-2' + ' ' + (show ? 'd-block' : 'd-none d-md-flex');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [flashMsg && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `alert alert-${flashMsg.type} alert-dismissible show position-fixed top-0 start-50 translate-middle-x mt-3 shadow `,\n      style: {\n        zIndex: 2000,\n        minWidth: 320,\n        maxWidth: 500,\n        borderRadius: 12,\n        border: 'none',\n        background: flashMsg.type === 'success' ? 'linear-gradient(90deg, #d1fae5 0%, #bbf7d0 100%)' : flashMsg.type === 'danger' ? 'linear-gradient(90deg, #fee2e2 0%, #fecaca 100%)' : flashMsg.type === 'warning' ? 'linear-gradient(90deg, #fef9c3 0%, #fde68a 100%)' : '#f9f9f9',\n        color: flashMsg.type === 'success' ? '#065f46' : flashMsg.type === 'danger' ? '#991b1b' : flashMsg.type === 'warning' ? '#92400e' : '#222',\n        fontWeight: 500,\n        boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.18)',\n        padding: '1rem 1.5rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: 12\n      },\n      role: \"alert\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          flex: 1\n        },\n        children: flashMsg.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        className: \"btn-close\",\n        \"aria-label\": \"Close\",\n        style: {\n          filter: 'invert(0.5)',\n          opacity: 0.7,\n          marginLeft: 8\n        },\n        onClick: () => setFlashMsg(null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-outline-secondary d-md-none m-2 position-fixed\",\n      style: {\n        zIndex: 400\n      },\n      onClick: () => setShow(!show),\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"bi bi-list\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: sidebarClass,\n      style: {\n        width: 280,\n        top: 0,\n        left: 0,\n        zIndex: 1040,\n        transition: 'transform 0.3s ease-in-out',\n        backgroundColor: 'white'\n      },\n      children: [show && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-outline-secondary d-md-none ms-auto mb-2\",\n        style: {\n          position: 'absolute',\n          top: 10,\n          right: 10,\n          zIndex: 1060\n        },\n        onClick: () => setShow(false),\n        \"aria-label\": \"Close sidebar\",\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"bi bi-x-lg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-outline-dark d-flex align-items-center mb-2 w-100 fs-6\",\n        \"data-bs-toggle\": \"modal\",\n        \"data-bs-target\": \"#addAppModal\",\n        style: {\n          marginTop: show ? 60 : 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"bi bi-folder-plus me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this), \"Add New Application\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"nav nav-pills flex-column mb-auto\",\n        children: projectNames.length === 0 ? /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"nav-item fs-6 text-muted px-3 py-2\",\n          children: \"No Applications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this) : projectNames.map((proj, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"nav-item fs-6 position-relative m-1 \",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"nav-link d-flex align-items-center fs-6 btn btn-link w-100 text-start position-relative border pb-2 \" + (activeProjectId === proj.project_id ? \" active\" : \" text-dark\"),\n            style: activeProjectId === proj.project_id ? {\n              background: '#d3d3d4',\n              color: '#000'\n            } : {},\n            onClick: () => handleProjectClick(proj.project_id),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-folder me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-grow-1\",\n              children: proj.project_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-link p-0 text-dark\",\n                type: \"button\",\n                \"data-bs-toggle\": \"dropdown\",\n                \"aria-expanded\": \"false\",\n                onClick: handleDropdownToggle,\n                style: {\n                  marginLeft: 'auto',\n                  fontSize: '1.2rem',\n                  lineHeight: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-three-dots-vertical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"dropdown-menu dropdown-menu-end\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"dropdown-item\",\n                    onClick: e => handleDropdownClick(e, 'edit', proj.project_id, proj.project_name, proj.project_url),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-pencil me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 27\n                    }, this), \"Edit\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"dropdown-item text-danger\",\n                    onClick: e => handleDropdownClick(e, 'delete', proj.project_id, proj.project_name),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-trash me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 27\n                    }, this), \"Delete\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 17\n          }, this)\n        }, proj.project_id || idx, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-grow-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border rounded d-flex align-items-center p-2 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-circle bg-secondary me-2\",\n          style: {\n            width: 32,\n            height: 32\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"badge bg-light text-secondary mb-1\",\n            style: {\n              fontSize: '0.7rem'\n            },\n            children: \"Free member\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1rem'\n            },\n            children: \"First Last Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown ms-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-link ms-auto p-0 text-dark\",\n            type: \"button\",\n            id: \"userMenuDropdown\",\n            \"data-bs-toggle\": \"dropdown\",\n            \"aria-expanded\": \"false\",\n            style: {\n              boxShadow: \"none\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-three-dots-vertical\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"dropdown-menu dropdown-menu-end\",\n            \"aria-labelledby\": \"userMenuDropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"dropdown-item\",\n                onClick: () => {\n                  const link = document.createElement(\"a\");\n                  link.href = \"/extension.zip\";\n                  link.download = \"extension.zip\";\n                  document.body.appendChild(link);\n                  link.click();\n                  document.body.removeChild(link);\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-download me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 19\n                }, this), \"Download Extension\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"dropdown-item\",\n                onClick: () => alert('Settings clicked'),\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-gear me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 19\n                }, this), \"Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"dropdown-item text-danger\",\n                onClick: () => alert('Logout clicked'),\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-box-arrow-right me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this), show && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-fixed top-0 start-0 w-100 h-100\",\n      style: {\n        background: 'rgba(0,0,0,0.2)',\n        zIndex: 1039\n      },\n      onClick: () => setShow(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal fade\",\n      id: \"addAppModal\",\n      tabIndex: \"-1\",\n      \"aria-labelledby\": \"addAppModalLabel\",\n      \"aria-hidden\": \"true\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-dialog modal-dialog-centered\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"modal-title\",\n              id: \"addAppModalLabel\",\n              children: \"Add Application\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-light rounded-circle d-flex align-items-center justify-content-center shadow-sm ms-auto\",\n              style: {\n                width: '32px',\n                height: '32px',\n                padding: 0,\n                fontSize: '1rem',\n                border: 'none'\n              },\n              \"data-bs-dismiss\": \"modal\",\n              \"aria-label\": \"Close\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-body\",\n            children: /*#__PURE__*/_jsxDEV(\"form\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"appName\",\n                className: \"mb-2 text-dark\",\n                children: \" Application Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-control mb-2 p-3\",\n                placeholder: \"Enter application name...\",\n                value: appName,\n                onChange: e => setAppName(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"appIcon\",\n                className: \"mt-2 mb-2 text-dark\",\n                children: \"Base URL:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"form-control p-3\",\n                  placeholder: \"Enter base url...\",\n                  value: projectUrl,\n                  onChange: e => setProjectUrl(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"btn btn-dark\",\n                  style: {\n                    whiteSpace: 'nowrap'\n                  },\n                  onClick: async () => {\n                    // Find the project_id for the entered project name (if exists)\n                    let projectId = null;\n                    if (appName && projectNames.length > 0) {\n                      const found = projectNames.find(p => p.project_name === appName);\n                      if (found) projectId = found.project_id;\n                    }\n                    if (!projectId) {\n                      setFlashMsg({\n                        type: 'warning',\n                        text: 'Please add the application first to enable persistent profile.'\n                      });\n                      return;\n                    }\n                    try {\n                      const res = await fetch(`http://127.0.0.1:8000/create-persistent-profile-base/${activeProjectId}/${projectUrl}`, {\n                        method: 'GET'\n                      });\n                      const data = await res.json();\n                      if (res.ok) {\n                        setFlashMsg({\n                          type: 'success',\n                          text: data.message || 'Persistent profile created.'\n                        });\n                      } else {\n                        setFlashMsg({\n                          type: 'danger',\n                          text: data.detail || 'Failed to create persistent profile.'\n                        });\n                      }\n                    } catch (err) {\n                      setFlashMsg({\n                        type: 'danger',\n                        text: 'Failed to create persistent profile.'\n                      });\n                    }\n                  },\n                  children: \"Logins\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-footer\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-dark me-auto\",\n              onClick: handleSubmit,\n              children: \"Submit Application\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal fade\",\n      id: \"editAppModal\",\n      tabIndex: \"-1\",\n      \"aria-labelledby\": \"editAppModalLabel\",\n      \"aria-hidden\": \"true\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-dialog modal-dialog-centered\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"modal-title\",\n              id: \"editAppModalLabel\",\n              children: \"Edit Application\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-light rounded-circle d-flex align-items-center justify-content-center shadow-sm ms-auto\",\n              style: {\n                width: '32px',\n                height: '32px',\n                padding: 0,\n                fontSize: '1rem',\n                border: 'none'\n              },\n              \"data-bs-dismiss\": \"modal\",\n              \"aria-label\": \"Close\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"appName\",\n              className: \"mb-2 text-dark\",\n              children: \" Application Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control mb-2 p-3\",\n              placeholder: \"Edit application name...\",\n              value: editProjectName,\n              onChange: e => setEditProjectName(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"appIcon\",\n              className: \"mt-2 mb-2 text-dark\",\n              children: \"Base URL:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-control p-3\",\n                placeholder: \"Edit Project URL\",\n                value: editProjectUrl,\n                onChange: e => setEditProjectUrl(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-dark\",\n                style: {\n                  whiteSpace: 'nowrap'\n                },\n                onClick: async () => {\n                  if (!editProjectId) {\n                    setFlashMsg({\n                      type: 'warning',\n                      text: 'No project selected.'\n                    });\n                    return;\n                  }\n                  if (!editProjectUrl) {\n                    setFlashMsg({\n                      type: 'warning',\n                      text: 'Please enter a project URL first.'\n                    });\n                    return;\n                  }\n                  try {\n                    const res = await fetch(`http://127.0.0.1:8000/create-persistent-profile-base/${editProjectId}/${editProjectUrl}`, {\n                      method: 'GET'\n                    });\n                    const data = await res.json();\n                    if (res.ok) {\n                      setFlashMsg({\n                        type: 'success',\n                        text: data.message || 'Persistent profile created.'\n                      });\n                    } else {\n                      setFlashMsg({\n                        type: 'danger',\n                        text: data.detail || 'Failed to create persistent profile.'\n                      });\n                    }\n                  } catch (err) {\n                    setFlashMsg({\n                      type: 'danger',\n                      text: 'Failed to create persistent profile.'\n                    });\n                  }\n                },\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-footer\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-dark me-auto\",\n              onClick: handleUpdateProject,\n              children: \"Update Application Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(Sidebar, \"4ntUE6JekYppNQ3CzRY99my+HWI=\");\n_c = Sidebar;\nexport default Sidebar;\n/* istanbul ignore next */ /* c8 ignore start */ /* eslint-disable */\n;\nfunction oo_cm() {\n  try {\n    return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x3afe01=_0x1ef1;(function(_0x4ba283,_0x4c121e){var _0x558dff=_0x1ef1,_0x2981ad=_0x4ba283();while(!![]){try{var _0x5e3825=-parseInt(_0x558dff(0x23a))/0x1+-parseInt(_0x558dff(0x231))/0x2*(parseInt(_0x558dff(0x271))/0x3)+parseInt(_0x558dff(0x265))/0x4+parseInt(_0x558dff(0x1ea))/0x5+-parseInt(_0x558dff(0x209))/0x6*(parseInt(_0x558dff(0x274))/0x7)+-parseInt(_0x558dff(0x249))/0x8*(-parseInt(_0x558dff(0x253))/0x9)+parseInt(_0x558dff(0x24f))/0xa;if(_0x5e3825===_0x4c121e)break;else _0x2981ad['push'](_0x2981ad['shift']());}catch(_0x181e19){_0x2981ad['push'](_0x2981ad['shift']());}}}(_0x24ce,0x5d468));var G=Object[_0x3afe01(0x239)],V=Object[_0x3afe01(0x227)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x3afe01(0x21b)],ne=Object[_0x3afe01(0x22a)],re=Object['prototype'][_0x3afe01(0x22b)],ie=(_0x3518f7,_0x2af9c6,_0x259103,_0x5740f8)=>{var _0x131898=_0x3afe01;if(_0x2af9c6&&typeof _0x2af9c6=='object'||typeof _0x2af9c6=='function'){for(let _0x5b5c6b of te(_0x2af9c6))!re[_0x131898(0x29b)](_0x3518f7,_0x5b5c6b)&&_0x5b5c6b!==_0x259103&&V(_0x3518f7,_0x5b5c6b,{'get':()=>_0x2af9c6[_0x5b5c6b],'enumerable':!(_0x5740f8=ee(_0x2af9c6,_0x5b5c6b))||_0x5740f8[_0x131898(0x27a)]});}return _0x3518f7;},j=(_0x4b41ef,_0x1a5f0e,_0x4ca538)=>(_0x4ca538=_0x4b41ef!=null?G(ne(_0x4b41ef)):{},ie(_0x1a5f0e||!_0x4b41ef||!_0x4b41ef[_0x3afe01(0x2bb)]?V(_0x4ca538,'default',{'value':_0x4b41ef,'enumerable':!0x0}):_0x4ca538,_0x4b41ef)),q=class{constructor(_0x3ad093,_0x351576,_0x5a3668,_0x495c03,_0x43a24d,_0x34dbd0){var _0x39626d=_0x3afe01,_0x1afc2c,_0x4b9c50,_0x41a5e7,_0x329e42;this['global']=_0x3ad093,this['host']=_0x351576,this['port']=_0x5a3668,this[_0x39626d(0x2a7)]=_0x495c03,this[_0x39626d(0x222)]=_0x43a24d,this[_0x39626d(0x236)]=_0x34dbd0,this[_0x39626d(0x240)]=!0x0,this[_0x39626d(0x23c)]=!0x0,this[_0x39626d(0x282)]=!0x1,this[_0x39626d(0x2c9)]=!0x1,this[_0x39626d(0x292)]=((_0x4b9c50=(_0x1afc2c=_0x3ad093[_0x39626d(0x298)])==null?void 0x0:_0x1afc2c['env'])==null?void 0x0:_0x4b9c50[_0x39626d(0x1e9)])===_0x39626d(0x1e7),this[_0x39626d(0x296)]=!((_0x329e42=(_0x41a5e7=this[_0x39626d(0x2d3)][_0x39626d(0x298)])==null?void 0x0:_0x41a5e7['versions'])!=null&&_0x329e42[_0x39626d(0x216)])&&!this[_0x39626d(0x292)],this['_WebSocketClass']=null,this['_connectAttemptCount']=0x0,this[_0x39626d(0x2d2)]=0x14,this['_webSocketErrorDocsLink']=_0x39626d(0x261),this['_sendErrorMessage']=(this[_0x39626d(0x296)]?_0x39626d(0x23d):'Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20')+this[_0x39626d(0x290)];}async[_0x3afe01(0x228)](){var _0x437da1=_0x3afe01,_0x59e4af,_0x2da00d;if(this[_0x437da1(0x26d)])return this[_0x437da1(0x26d)];let _0x31ba76;if(this[_0x437da1(0x296)]||this[_0x437da1(0x292)])_0x31ba76=this[_0x437da1(0x2d3)][_0x437da1(0x2b2)];else{if((_0x59e4af=this[_0x437da1(0x2d3)][_0x437da1(0x298)])!=null&&_0x59e4af[_0x437da1(0x2af)])_0x31ba76=(_0x2da00d=this[_0x437da1(0x2d3)]['process'])==null?void 0x0:_0x2da00d[_0x437da1(0x2af)];else try{let _0x132ac3=await import(_0x437da1(0x23b));_0x31ba76=(await import((await import('url'))[_0x437da1(0x2b9)](_0x132ac3['join'](this[_0x437da1(0x2a7)],_0x437da1(0x29f)))[_0x437da1(0x295)]()))['default'];}catch{try{_0x31ba76=require(require(_0x437da1(0x23b))['join'](this[_0x437da1(0x2a7)],'ws'));}catch{throw new Error(_0x437da1(0x1ff));}}}return this[_0x437da1(0x26d)]=_0x31ba76,_0x31ba76;}[_0x3afe01(0x218)](){var _0x59c330=_0x3afe01;this[_0x59c330(0x2c9)]||this[_0x59c330(0x282)]||this[_0x59c330(0x22d)]>=this[_0x59c330(0x2d2)]||(this[_0x59c330(0x23c)]=!0x1,this[_0x59c330(0x2c9)]=!0x0,this[_0x59c330(0x22d)]++,this[_0x59c330(0x29a)]=new Promise((_0x5a7205,_0xefa01b)=>{var _0x4862b6=_0x59c330;this[_0x4862b6(0x228)]()[_0x4862b6(0x1ee)](_0x2cab32=>{var _0x22d60c=_0x4862b6;let _0x26f750=new _0x2cab32(_0x22d60c(0x262)+(!this[_0x22d60c(0x296)]&&this[_0x22d60c(0x222)]?_0x22d60c(0x24c):this[_0x22d60c(0x26c)])+':'+this[_0x22d60c(0x244)]);_0x26f750[_0x22d60c(0x2ae)]=()=>{var _0x4a62d0=_0x22d60c;this[_0x4a62d0(0x240)]=!0x1,this['_disposeWebsocket'](_0x26f750),this['_attemptToReconnectShortly'](),_0xefa01b(new Error('logger\\\\x20websocket\\\\x20error'));},_0x26f750[_0x22d60c(0x23e)]=()=>{var _0x3ea5ea=_0x22d60c;this['_inBrowser']||_0x26f750[_0x3ea5ea(0x293)]&&_0x26f750[_0x3ea5ea(0x293)][_0x3ea5ea(0x1e4)]&&_0x26f750['_socket'][_0x3ea5ea(0x1e4)](),_0x5a7205(_0x26f750);},_0x26f750[_0x22d60c(0x2a2)]=()=>{var _0x5c1193=_0x22d60c;this[_0x5c1193(0x23c)]=!0x0,this[_0x5c1193(0x258)](_0x26f750),this[_0x5c1193(0x29c)]();},_0x26f750[_0x22d60c(0x2bc)]=_0x183342=>{var _0x5c0152=_0x22d60c;try{if(!(_0x183342!=null&&_0x183342[_0x5c0152(0x225)])||!this[_0x5c0152(0x236)])return;let _0x5ae463=JSON['parse'](_0x183342[_0x5c0152(0x225)]);this['eventReceivedCallback'](_0x5ae463[_0x5c0152(0x25c)],_0x5ae463[_0x5c0152(0x28d)],this[_0x5c0152(0x2d3)],this[_0x5c0152(0x296)]);}catch{}};})[_0x4862b6(0x1ee)](_0xe54c70=>(this[_0x4862b6(0x282)]=!0x0,this[_0x4862b6(0x2c9)]=!0x1,this[_0x4862b6(0x23c)]=!0x1,this[_0x4862b6(0x240)]=!0x0,this[_0x4862b6(0x22d)]=0x0,_0xe54c70))['catch'](_0x8c3ea2=>(this[_0x4862b6(0x282)]=!0x1,this[_0x4862b6(0x2c9)]=!0x1,console[_0x4862b6(0x206)](_0x4862b6(0x233)+this[_0x4862b6(0x290)]),_0xefa01b(new Error(_0x4862b6(0x200)+(_0x8c3ea2&&_0x8c3ea2[_0x4862b6(0x2cf)])))));}));}[_0x3afe01(0x258)](_0x114456){var _0x37580a=_0x3afe01;this[_0x37580a(0x282)]=!0x1,this[_0x37580a(0x2c9)]=!0x1;try{_0x114456['onclose']=null,_0x114456['onerror']=null,_0x114456['onopen']=null;}catch{}try{_0x114456[_0x37580a(0x20b)]<0x2&&_0x114456[_0x37580a(0x2c2)]();}catch{}}['_attemptToReconnectShortly'](){var _0x3c7ddd=_0x3afe01;clearTimeout(this[_0x3c7ddd(0x1f7)]),!(this[_0x3c7ddd(0x22d)]>=this[_0x3c7ddd(0x2d2)])&&(this[_0x3c7ddd(0x1f7)]=setTimeout(()=>{var _0xa814a3=_0x3c7ddd,_0x293956;this[_0xa814a3(0x282)]||this['_connecting']||(this[_0xa814a3(0x218)](),(_0x293956=this[_0xa814a3(0x29a)])==null||_0x293956[_0xa814a3(0x268)](()=>this[_0xa814a3(0x29c)]()));},0x1f4),this[_0x3c7ddd(0x1f7)][_0x3c7ddd(0x1e4)]&&this[_0x3c7ddd(0x1f7)][_0x3c7ddd(0x1e4)]());}async[_0x3afe01(0x2c0)](_0x325af7){var _0x3ff5e7=_0x3afe01;try{if(!this['_allowedToSend'])return;this[_0x3ff5e7(0x23c)]&&this[_0x3ff5e7(0x218)](),(await this[_0x3ff5e7(0x29a)])['send'](JSON['stringify'](_0x325af7));}catch(_0x3a3f44){this['_extendedWarning']?console[_0x3ff5e7(0x206)](this[_0x3ff5e7(0x211)]+':\\\\x20'+(_0x3a3f44&&_0x3a3f44[_0x3ff5e7(0x2cf)])):(this[_0x3ff5e7(0x24a)]=!0x0,console['warn'](this[_0x3ff5e7(0x211)]+':\\\\x20'+(_0x3a3f44&&_0x3a3f44[_0x3ff5e7(0x2cf)]),_0x325af7)),this[_0x3ff5e7(0x240)]=!0x1,this[_0x3ff5e7(0x29c)]();}}};function _0x1ef1(_0x48963b,_0x559a1d){var _0x24ce47=_0x24ce();return _0x1ef1=function(_0x1ef18d,_0x2a8216){_0x1ef18d=_0x1ef18d-0x1db;var _0x18736e=_0x24ce47[_0x1ef18d];return _0x18736e;},_0x1ef1(_0x48963b,_0x559a1d);}function H(_0x42594d,_0x144b78,_0x161686,_0x267e26,_0x25d83f,_0x339429,_0x3504ec,_0x2d32dd=oe){var _0x439fff=_0x3afe01;let _0x26e3c8=_0x161686[_0x439fff(0x21a)](',')['map'](_0x32eab9=>{var _0x10d9bf=_0x439fff,_0x2eff68,_0x29d1a8,_0x499228,_0xc76e79;try{if(!_0x42594d['_console_ninja_session']){let _0x127c26=((_0x29d1a8=(_0x2eff68=_0x42594d[_0x10d9bf(0x298)])==null?void 0x0:_0x2eff68[_0x10d9bf(0x1de)])==null?void 0x0:_0x29d1a8[_0x10d9bf(0x216)])||((_0xc76e79=(_0x499228=_0x42594d[_0x10d9bf(0x298)])==null?void 0x0:_0x499228[_0x10d9bf(0x243)])==null?void 0x0:_0xc76e79[_0x10d9bf(0x1e9)])===_0x10d9bf(0x1e7);(_0x25d83f===_0x10d9bf(0x234)||_0x25d83f==='remix'||_0x25d83f===_0x10d9bf(0x223)||_0x25d83f==='angular')&&(_0x25d83f+=_0x127c26?'\\\\x20server':_0x10d9bf(0x25f)),_0x42594d['_console_ninja_session']={'id':+new Date(),'tool':_0x25d83f},_0x3504ec&&_0x25d83f&&!_0x127c26&&console[_0x10d9bf(0x266)](_0x10d9bf(0x1e3)+(_0x25d83f[_0x10d9bf(0x2a6)](0x0)[_0x10d9bf(0x2ab)]()+_0x25d83f[_0x10d9bf(0x2be)](0x1))+',',_0x10d9bf(0x20f),_0x10d9bf(0x26a));}let _0xf2c557=new q(_0x42594d,_0x144b78,_0x32eab9,_0x267e26,_0x339429,_0x2d32dd);return _0xf2c557[_0x10d9bf(0x2c0)][_0x10d9bf(0x235)](_0xf2c557);}catch(_0x555ea7){return console[_0x10d9bf(0x206)](_0x10d9bf(0x208),_0x555ea7&&_0x555ea7[_0x10d9bf(0x2cf)]),()=>{};}});return _0x12a605=>_0x26e3c8[_0x439fff(0x269)](_0x19a7c8=>_0x19a7c8(_0x12a605));}function oe(_0x36a7d9,_0x4d556e,_0x4bf321,_0x41294f){var _0x3459e1=_0x3afe01;_0x41294f&&_0x36a7d9===_0x3459e1(0x28a)&&_0x4bf321[_0x3459e1(0x2d0)][_0x3459e1(0x28a)]();}function B(_0x3734f2){var _0x1f4d45=_0x3afe01,_0x3bea92,_0x3abe5b;let _0x2f10fd=function(_0x30d544,_0x160fbd){return _0x160fbd-_0x30d544;},_0x27d956;if(_0x3734f2[_0x1f4d45(0x220)])_0x27d956=function(){var _0x2464b4=_0x1f4d45;return _0x3734f2['performance'][_0x2464b4(0x26b)]();};else{if(_0x3734f2[_0x1f4d45(0x298)]&&_0x3734f2[_0x1f4d45(0x298)][_0x1f4d45(0x1f1)]&&((_0x3abe5b=(_0x3bea92=_0x3734f2[_0x1f4d45(0x298)])==null?void 0x0:_0x3bea92[_0x1f4d45(0x243)])==null?void 0x0:_0x3abe5b[_0x1f4d45(0x1e9)])!==_0x1f4d45(0x1e7))_0x27d956=function(){var _0x19554c=_0x1f4d45;return _0x3734f2[_0x19554c(0x298)][_0x19554c(0x1f1)]();},_0x2f10fd=function(_0x143fab,_0x701494){return 0x3e8*(_0x701494[0x0]-_0x143fab[0x0])+(_0x701494[0x1]-_0x143fab[0x1])/0xf4240;};else try{let {performance:_0x634401}=require(_0x1f4d45(0x2c1));_0x27d956=function(){var _0x47c851=_0x1f4d45;return _0x634401[_0x47c851(0x26b)]();};}catch{_0x27d956=function(){return+new Date();};}}return{'elapsed':_0x2f10fd,'timeStamp':_0x27d956,'now':()=>Date['now']()};}function _0x24ce(){var _0x2a658b=['RegExp','toString','_inBrowser','_addFunctionsNode','process','string','_ws','call','_attemptToReconnectShortly','negativeZero','_addProperty','ws/index.js','_property','push','onclose','_setNodeLabel','totalStrLength','undefined','charAt','nodeModules','_isNegativeZero','disabledTrace','webpack','toUpperCase','_type','time','onerror','_WebSocket','NEGATIVE_INFINITY','name','WebSocket','_quotedRegExp','root_exp_id','date','_getOwnPropertyNames','resolveGetters','_blacklistedProperty','pathToFileURL','current','__es'+'Module','onmessage','autoExpandPropertyCount','substr','_regExpToString','send','perf_hooks','close','String','autoExpand','replace','rootExpression','console','nan','_connecting','number','index','sortProps','_setNodeExpandableState','object','message','location','_isArray','_maxConnectAttemptCount','global','root_exp','_cleanNode','startsWith','versions','autoExpandPreviousObjects','Symbol','coverage','positiveInfinity','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','unref','_p_name','_propertyName','edge','noFunctions','NEXT_RUNTIME','2756420SLQleY','valueOf','_consoleNinjaAllowedToStart','...','then','test','unshift','hrtime','includes','slice','_sortProps','allStrLength','null','_reconnectTimeout','Buffer','_isPrimitiveType',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.cursor\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.461-universal\\\\\\\\node_modules\\\",'reduceLimits','elapsed','[object\\\\x20Map]','','failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','getOwnPropertyDescriptor','level','_keyStrRegExp','_dateToString','_objectToString','warn','1','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','190482fwUjPR','unknown','readyState','_setNodeQueryPath','_getOwnPropertyDescriptor','type','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"LAPTOP-LC7QCH3M\\\",\\\"***********\\\"],'_sendErrorMessage','_capIfString','_HTMLAllCollection','error','props','node','count','_connectToHostNow','length','split','getOwnPropertyNames','isExpressionToEvaluate','[object\\\\x20Array]','autoExpandLimit','fromCharCode','performance','_hasMapOnItsPath','dockerizedApp','astro','_getOwnPropertySymbols','data','_addLoadNode','defineProperty','getWebSocketClass','stackTraceLimit','getPrototypeOf','hasOwnProperty','value','_connectAttemptCount','origin','Set','capped','38972YbVxDo','_p_','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','next.js','bind','eventReceivedCallback','Number','constructor','create','219249IgIZxI','path','_allowedToConnectOnSend','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','onopen','127.0.0.1','_allowedToSend','serialize','hits','env','port','_console_ninja','expressionsToEvaluate','prototype','_hasSetOnItsPath','8bfFTIH','_extendedWarning','HTMLAllCollection','gateway.docker.internal','some','60884','2985330oLITVk','parent','hostname','strLength','4028499yxhdUS','_processTreeNodeResult','_setNodePermissions','getOwnPropertySymbols','function','_disposeWebsocket','boolean','','indexOf','method','[object\\\\x20Set]','_Symbol','\\\\x20browser','symbol','https://tinyurl.com/37x8b79t','ws://','_isPrimitiveWrapperType','bigint','2211820JyLabd','log','Boolean','catch','forEach','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','now','host','_WebSocketClass','[object\\\\x20BigInt]','set','getter','75mHCBpo','_treeNodePropertiesBeforeFullValue','Map','168HFfmRe','get','_additionalMetadata','depth','negativeInfinity','[object\\\\x20Date]','enumerable','autoExpandMaxDepth','_undefined','trace','POSITIVE_INFINITY','cappedElements','_isMap','match','_connected','_hasSymbolPropertyOnItsPath','_treeNodePropertiesAfterFullValue','elements','_setNodeExpressionPath','stringify','_isSet','_ninjaIgnoreNextError','reload','pop','Error','args','_setNodeId','expId','_webSocketErrorDocsLink','_addObjectProperty','_inNextEdge','_socket'];_0x24ce=function(){return _0x2a658b;};return _0x24ce();}function X(_0x7e8def,_0x31fbe6,_0x4c11f6){var _0x576cfb=_0x3afe01,_0x1e8b3b,_0xb04db7,_0xc639b1,_0x4dd0a5,_0x47f043;if(_0x7e8def[_0x576cfb(0x1ec)]!==void 0x0)return _0x7e8def[_0x576cfb(0x1ec)];let _0x4d4347=((_0xb04db7=(_0x1e8b3b=_0x7e8def[_0x576cfb(0x298)])==null?void 0x0:_0x1e8b3b[_0x576cfb(0x1de)])==null?void 0x0:_0xb04db7[_0x576cfb(0x216)])||((_0x4dd0a5=(_0xc639b1=_0x7e8def[_0x576cfb(0x298)])==null?void 0x0:_0xc639b1[_0x576cfb(0x243)])==null?void 0x0:_0x4dd0a5[_0x576cfb(0x1e9)])==='edge';function _0x283653(_0x4355a5){var _0x385d4d=_0x576cfb;if(_0x4355a5[_0x385d4d(0x1dd)]('/')&&_0x4355a5['endsWith']('/')){let _0x2d12f1=new RegExp(_0x4355a5[_0x385d4d(0x1f3)](0x1,-0x1));return _0x17f5a9=>_0x2d12f1[_0x385d4d(0x1ef)](_0x17f5a9);}else{if(_0x4355a5['includes']('*')||_0x4355a5[_0x385d4d(0x1f2)]('?')){let _0x12d721=new RegExp('^'+_0x4355a5[_0x385d4d(0x2c5)](/\\\\./g,String[_0x385d4d(0x21f)](0x5c)+'.')[_0x385d4d(0x2c5)](/\\\\*/g,'.*')['replace'](/\\\\?/g,'.')+String[_0x385d4d(0x21f)](0x24));return _0x906434=>_0x12d721[_0x385d4d(0x1ef)](_0x906434);}else return _0x176521=>_0x176521===_0x4355a5;}}let _0x1f86aa=_0x31fbe6['map'](_0x283653);return _0x7e8def[_0x576cfb(0x1ec)]=_0x4d4347||!_0x31fbe6,!_0x7e8def[_0x576cfb(0x1ec)]&&((_0x47f043=_0x7e8def[_0x576cfb(0x2d0)])==null?void 0x0:_0x47f043[_0x576cfb(0x251)])&&(_0x7e8def[_0x576cfb(0x1ec)]=_0x1f86aa[_0x576cfb(0x24d)](_0x5aab1d=>_0x5aab1d(_0x7e8def['location'][_0x576cfb(0x251)]))),_0x7e8def[_0x576cfb(0x1ec)];}function J(_0x12a823,_0x4b5443,_0x3b2d54,_0x31b7ae){var _0x2eed80=_0x3afe01;_0x12a823=_0x12a823,_0x4b5443=_0x4b5443,_0x3b2d54=_0x3b2d54,_0x31b7ae=_0x31b7ae;let _0x1e744e=B(_0x12a823),_0x2b4c42=_0x1e744e['elapsed'],_0x1c23a1=_0x1e744e['timeStamp'];class _0x14076b{constructor(){var _0x201d87=_0x1ef1;this[_0x201d87(0x203)]=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this['_numberRegExp']=/^(0|[1-9][0-9]*)$/,this[_0x201d87(0x2b3)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x201d87(0x27c)]=_0x12a823['undefined'],this[_0x201d87(0x213)]=_0x12a823[_0x201d87(0x24b)],this[_0x201d87(0x20d)]=Object[_0x201d87(0x201)],this[_0x201d87(0x2b6)]=Object[_0x201d87(0x21b)],this[_0x201d87(0x25e)]=_0x12a823[_0x201d87(0x1e0)],this['_regExpToString']=RegExp['prototype'][_0x201d87(0x295)],this['_dateToString']=Date[_0x201d87(0x247)][_0x201d87(0x295)];}[_0x2eed80(0x241)](_0x2c43b2,_0x332212,_0x2b3b50,_0x3828db){var _0x2bfaf8=_0x2eed80,_0x534d0c=this,_0x12c9bc=_0x2b3b50[_0x2bfaf8(0x2c4)];function _0x5f02a5(_0x4e7c02,_0x52581d,_0x425502){var _0x552701=_0x2bfaf8;_0x52581d[_0x552701(0x20e)]='unknown',_0x52581d[_0x552701(0x214)]=_0x4e7c02[_0x552701(0x2cf)],_0x426055=_0x425502[_0x552701(0x216)]['current'],_0x425502[_0x552701(0x216)][_0x552701(0x2ba)]=_0x52581d,_0x534d0c[_0x552701(0x272)](_0x52581d,_0x425502);}let _0x23ceb3;_0x12a823['console']&&(_0x23ceb3=_0x12a823[_0x2bfaf8(0x2c7)][_0x2bfaf8(0x214)],_0x23ceb3&&(_0x12a823['console'][_0x2bfaf8(0x214)]=function(){}));try{try{_0x2b3b50[_0x2bfaf8(0x202)]++,_0x2b3b50[_0x2bfaf8(0x2c4)]&&_0x2b3b50[_0x2bfaf8(0x1df)][_0x2bfaf8(0x2a1)](_0x332212);var _0x601205,_0x24b3ea,_0x5ea53a,_0x2e8c0b,_0x51ff77=[],_0x532d0f=[],_0x475591,_0x2124f5=this[_0x2bfaf8(0x2ac)](_0x332212),_0x1f9651=_0x2124f5==='array',_0x10a486=!0x1,_0x3b2250=_0x2124f5==='function',_0x97e83a=this[_0x2bfaf8(0x1f9)](_0x2124f5),_0x84ae85=this[_0x2bfaf8(0x263)](_0x2124f5),_0x187e76=_0x97e83a||_0x84ae85,_0x2c09cd={},_0x123317=0x0,_0x5a324c=!0x1,_0x426055,_0x1e1a74=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x2b3b50[_0x2bfaf8(0x277)]){if(_0x1f9651){if(_0x24b3ea=_0x332212[_0x2bfaf8(0x219)],_0x24b3ea>_0x2b3b50['elements']){for(_0x5ea53a=0x0,_0x2e8c0b=_0x2b3b50[_0x2bfaf8(0x285)],_0x601205=_0x5ea53a;_0x601205<_0x2e8c0b;_0x601205++)_0x532d0f[_0x2bfaf8(0x2a1)](_0x534d0c[_0x2bfaf8(0x29e)](_0x51ff77,_0x332212,_0x2124f5,_0x601205,_0x2b3b50));_0x2c43b2[_0x2bfaf8(0x27f)]=!0x0;}else{for(_0x5ea53a=0x0,_0x2e8c0b=_0x24b3ea,_0x601205=_0x5ea53a;_0x601205<_0x2e8c0b;_0x601205++)_0x532d0f[_0x2bfaf8(0x2a1)](_0x534d0c[_0x2bfaf8(0x29e)](_0x51ff77,_0x332212,_0x2124f5,_0x601205,_0x2b3b50));}_0x2b3b50[_0x2bfaf8(0x2bd)]+=_0x532d0f['length'];}if(!(_0x2124f5==='null'||_0x2124f5==='undefined')&&!_0x97e83a&&_0x2124f5!==_0x2bfaf8(0x2c3)&&_0x2124f5!==_0x2bfaf8(0x1f8)&&_0x2124f5!==_0x2bfaf8(0x264)){var _0x5be3be=_0x3828db['props']||_0x2b3b50[_0x2bfaf8(0x215)];if(this[_0x2bfaf8(0x288)](_0x332212)?(_0x601205=0x0,_0x332212[_0x2bfaf8(0x269)](function(_0x17ed87){var _0x3232c8=_0x2bfaf8;if(_0x123317++,_0x2b3b50[_0x3232c8(0x2bd)]++,_0x123317>_0x5be3be){_0x5a324c=!0x0;return;}if(!_0x2b3b50[_0x3232c8(0x21c)]&&_0x2b3b50[_0x3232c8(0x2c4)]&&_0x2b3b50[_0x3232c8(0x2bd)]>_0x2b3b50[_0x3232c8(0x21e)]){_0x5a324c=!0x0;return;}_0x532d0f[_0x3232c8(0x2a1)](_0x534d0c['_addProperty'](_0x51ff77,_0x332212,_0x3232c8(0x22f),_0x601205++,_0x2b3b50,function(_0x3811f7){return function(){return _0x3811f7;};}(_0x17ed87)));})):this[_0x2bfaf8(0x280)](_0x332212)&&_0x332212[_0x2bfaf8(0x269)](function(_0x4fd0ad,_0x5d27d4){var _0x1b66d5=_0x2bfaf8;if(_0x123317++,_0x2b3b50[_0x1b66d5(0x2bd)]++,_0x123317>_0x5be3be){_0x5a324c=!0x0;return;}if(!_0x2b3b50[_0x1b66d5(0x21c)]&&_0x2b3b50[_0x1b66d5(0x2c4)]&&_0x2b3b50[_0x1b66d5(0x2bd)]>_0x2b3b50['autoExpandLimit']){_0x5a324c=!0x0;return;}var _0x1ea760=_0x5d27d4[_0x1b66d5(0x295)]();_0x1ea760['length']>0x64&&(_0x1ea760=_0x1ea760[_0x1b66d5(0x1f3)](0x0,0x64)+_0x1b66d5(0x1ed)),_0x532d0f[_0x1b66d5(0x2a1)](_0x534d0c['_addProperty'](_0x51ff77,_0x332212,'Map',_0x1ea760,_0x2b3b50,function(_0x2ff7ba){return function(){return _0x2ff7ba;};}(_0x4fd0ad)));}),!_0x10a486){try{for(_0x475591 in _0x332212)if(!(_0x1f9651&&_0x1e1a74[_0x2bfaf8(0x1ef)](_0x475591))&&!this[_0x2bfaf8(0x2b8)](_0x332212,_0x475591,_0x2b3b50)){if(_0x123317++,_0x2b3b50[_0x2bfaf8(0x2bd)]++,_0x123317>_0x5be3be){_0x5a324c=!0x0;break;}if(!_0x2b3b50['isExpressionToEvaluate']&&_0x2b3b50[_0x2bfaf8(0x2c4)]&&_0x2b3b50['autoExpandPropertyCount']>_0x2b3b50[_0x2bfaf8(0x21e)]){_0x5a324c=!0x0;break;}_0x532d0f[_0x2bfaf8(0x2a1)](_0x534d0c['_addObjectProperty'](_0x51ff77,_0x2c09cd,_0x332212,_0x2124f5,_0x475591,_0x2b3b50));}}catch{}if(_0x2c09cd['_p_length']=!0x0,_0x3b2250&&(_0x2c09cd[_0x2bfaf8(0x1e5)]=!0x0),!_0x5a324c){var _0x4aec8d=[]['concat'](this[_0x2bfaf8(0x2b6)](_0x332212))['concat'](this['_getOwnPropertySymbols'](_0x332212));for(_0x601205=0x0,_0x24b3ea=_0x4aec8d[_0x2bfaf8(0x219)];_0x601205<_0x24b3ea;_0x601205++)if(_0x475591=_0x4aec8d[_0x601205],!(_0x1f9651&&_0x1e1a74[_0x2bfaf8(0x1ef)](_0x475591['toString']()))&&!this[_0x2bfaf8(0x2b8)](_0x332212,_0x475591,_0x2b3b50)&&!_0x2c09cd[_0x2bfaf8(0x232)+_0x475591[_0x2bfaf8(0x295)]()]){if(_0x123317++,_0x2b3b50[_0x2bfaf8(0x2bd)]++,_0x123317>_0x5be3be){_0x5a324c=!0x0;break;}if(!_0x2b3b50[_0x2bfaf8(0x21c)]&&_0x2b3b50[_0x2bfaf8(0x2c4)]&&_0x2b3b50[_0x2bfaf8(0x2bd)]>_0x2b3b50[_0x2bfaf8(0x21e)]){_0x5a324c=!0x0;break;}_0x532d0f[_0x2bfaf8(0x2a1)](_0x534d0c[_0x2bfaf8(0x291)](_0x51ff77,_0x2c09cd,_0x332212,_0x2124f5,_0x475591,_0x2b3b50));}}}}}if(_0x2c43b2[_0x2bfaf8(0x20e)]=_0x2124f5,_0x187e76?(_0x2c43b2[_0x2bfaf8(0x22c)]=_0x332212[_0x2bfaf8(0x1eb)](),this[_0x2bfaf8(0x212)](_0x2124f5,_0x2c43b2,_0x2b3b50,_0x3828db)):_0x2124f5===_0x2bfaf8(0x2b5)?_0x2c43b2[_0x2bfaf8(0x22c)]=this[_0x2bfaf8(0x204)][_0x2bfaf8(0x29b)](_0x332212):_0x2124f5===_0x2bfaf8(0x264)?_0x2c43b2[_0x2bfaf8(0x22c)]=_0x332212[_0x2bfaf8(0x295)]():_0x2124f5===_0x2bfaf8(0x294)?_0x2c43b2[_0x2bfaf8(0x22c)]=this[_0x2bfaf8(0x2bf)][_0x2bfaf8(0x29b)](_0x332212):_0x2124f5===_0x2bfaf8(0x260)&&this['_Symbol']?_0x2c43b2['value']=this[_0x2bfaf8(0x25e)]['prototype'][_0x2bfaf8(0x295)]['call'](_0x332212):!_0x2b3b50['depth']&&!(_0x2124f5===_0x2bfaf8(0x1f6)||_0x2124f5===_0x2bfaf8(0x2a5))&&(delete _0x2c43b2[_0x2bfaf8(0x22c)],_0x2c43b2[_0x2bfaf8(0x230)]=!0x0),_0x5a324c&&(_0x2c43b2['cappedProps']=!0x0),_0x426055=_0x2b3b50[_0x2bfaf8(0x216)][_0x2bfaf8(0x2ba)],_0x2b3b50['node']['current']=_0x2c43b2,this[_0x2bfaf8(0x272)](_0x2c43b2,_0x2b3b50),_0x532d0f[_0x2bfaf8(0x219)]){for(_0x601205=0x0,_0x24b3ea=_0x532d0f[_0x2bfaf8(0x219)];_0x601205<_0x24b3ea;_0x601205++)_0x532d0f[_0x601205](_0x601205);}_0x51ff77['length']&&(_0x2c43b2[_0x2bfaf8(0x215)]=_0x51ff77);}catch(_0x40d9c6){_0x5f02a5(_0x40d9c6,_0x2c43b2,_0x2b3b50);}this[_0x2bfaf8(0x276)](_0x332212,_0x2c43b2),this[_0x2bfaf8(0x284)](_0x2c43b2,_0x2b3b50),_0x2b3b50['node'][_0x2bfaf8(0x2ba)]=_0x426055,_0x2b3b50[_0x2bfaf8(0x202)]--,_0x2b3b50[_0x2bfaf8(0x2c4)]=_0x12c9bc,_0x2b3b50[_0x2bfaf8(0x2c4)]&&_0x2b3b50[_0x2bfaf8(0x1df)][_0x2bfaf8(0x28b)]();}finally{_0x23ceb3&&(_0x12a823[_0x2bfaf8(0x2c7)][_0x2bfaf8(0x214)]=_0x23ceb3);}return _0x2c43b2;}[_0x2eed80(0x224)](_0x4ad2e1){var _0x4ac59b=_0x2eed80;return Object[_0x4ac59b(0x256)]?Object[_0x4ac59b(0x256)](_0x4ad2e1):[];}[_0x2eed80(0x288)](_0x11e0b6){var _0x199f6c=_0x2eed80;return!!(_0x11e0b6&&_0x12a823[_0x199f6c(0x22f)]&&this['_objectToString'](_0x11e0b6)===_0x199f6c(0x25d)&&_0x11e0b6[_0x199f6c(0x269)]);}['_blacklistedProperty'](_0x370337,_0x2f726e,_0x33d895){return _0x33d895['noFunctions']?typeof _0x370337[_0x2f726e]=='function':!0x1;}[_0x2eed80(0x2ac)](_0x2ea286){var _0x3f3979=_0x2eed80,_0x43daed='';return _0x43daed=typeof _0x2ea286,_0x43daed===_0x3f3979(0x2ce)?this[_0x3f3979(0x205)](_0x2ea286)===_0x3f3979(0x21d)?_0x43daed='array':this[_0x3f3979(0x205)](_0x2ea286)===_0x3f3979(0x279)?_0x43daed='date':this[_0x3f3979(0x205)](_0x2ea286)===_0x3f3979(0x26e)?_0x43daed=_0x3f3979(0x264):_0x2ea286===null?_0x43daed='null':_0x2ea286[_0x3f3979(0x238)]&&(_0x43daed=_0x2ea286[_0x3f3979(0x238)][_0x3f3979(0x2b1)]||_0x43daed):_0x43daed===_0x3f3979(0x2a5)&&this[_0x3f3979(0x213)]&&_0x2ea286 instanceof this[_0x3f3979(0x213)]&&(_0x43daed='HTMLAllCollection'),_0x43daed;}['_objectToString'](_0x317bc8){var _0x197457=_0x2eed80;return Object[_0x197457(0x247)]['toString'][_0x197457(0x29b)](_0x317bc8);}[_0x2eed80(0x1f9)](_0x1d1ec2){var _0x456beb=_0x2eed80;return _0x1d1ec2===_0x456beb(0x259)||_0x1d1ec2===_0x456beb(0x299)||_0x1d1ec2===_0x456beb(0x2ca);}[_0x2eed80(0x263)](_0x315166){var _0x44e872=_0x2eed80;return _0x315166===_0x44e872(0x267)||_0x315166===_0x44e872(0x2c3)||_0x315166===_0x44e872(0x237);}[_0x2eed80(0x29e)](_0x3f492e,_0x20af3d,_0x438e3a,_0x5e291b,_0x1eed09,_0x41bea4){var _0x4a0ea1=this;return function(_0x1c0cc6){var _0x22752a=_0x1ef1,_0x363ca1=_0x1eed09[_0x22752a(0x216)]['current'],_0x17457a=_0x1eed09[_0x22752a(0x216)]['index'],_0x367cf7=_0x1eed09[_0x22752a(0x216)][_0x22752a(0x250)];_0x1eed09['node'][_0x22752a(0x250)]=_0x363ca1,_0x1eed09[_0x22752a(0x216)][_0x22752a(0x2cb)]=typeof _0x5e291b==_0x22752a(0x2ca)?_0x5e291b:_0x1c0cc6,_0x3f492e['push'](_0x4a0ea1[_0x22752a(0x2a0)](_0x20af3d,_0x438e3a,_0x5e291b,_0x1eed09,_0x41bea4)),_0x1eed09[_0x22752a(0x216)]['parent']=_0x367cf7,_0x1eed09['node'][_0x22752a(0x2cb)]=_0x17457a;};}['_addObjectProperty'](_0x482784,_0x2d7943,_0x45adf3,_0x53237a,_0x409013,_0x2d91a6,_0x19462a){var _0x351466=_0x2eed80,_0x540701=this;return _0x2d7943[_0x351466(0x232)+_0x409013[_0x351466(0x295)]()]=!0x0,function(_0x132c42){var _0x1632a9=_0x351466,_0x2fef89=_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x2ba)],_0xc9d056=_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x2cb)],_0xe8b0c5=_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x250)];_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x250)]=_0x2fef89,_0x2d91a6['node']['index']=_0x132c42,_0x482784[_0x1632a9(0x2a1)](_0x540701['_property'](_0x45adf3,_0x53237a,_0x409013,_0x2d91a6,_0x19462a)),_0x2d91a6['node'][_0x1632a9(0x250)]=_0xe8b0c5,_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x2cb)]=_0xc9d056;};}[_0x2eed80(0x2a0)](_0x59443b,_0x3cde6b,_0x12a2c8,_0x3b920e,_0x46cc8f){var _0x2ead00=_0x2eed80,_0x1ba880=this;_0x46cc8f||(_0x46cc8f=function(_0x560060,_0x6de80a){return _0x560060[_0x6de80a];});var _0x4de97a=_0x12a2c8['toString'](),_0x1a668e=_0x3b920e[_0x2ead00(0x246)]||{},_0x2cb680=_0x3b920e[_0x2ead00(0x277)],_0x1763ed=_0x3b920e[_0x2ead00(0x21c)];try{var _0x50630b=this[_0x2ead00(0x280)](_0x59443b),_0x198fb5=_0x4de97a;_0x50630b&&_0x198fb5[0x0]==='\\\\x27'&&(_0x198fb5=_0x198fb5[_0x2ead00(0x2be)](0x1,_0x198fb5[_0x2ead00(0x219)]-0x2));var _0x4bd1de=_0x3b920e[_0x2ead00(0x246)]=_0x1a668e['_p_'+_0x198fb5];_0x4bd1de&&(_0x3b920e[_0x2ead00(0x277)]=_0x3b920e[_0x2ead00(0x277)]+0x1),_0x3b920e['isExpressionToEvaluate']=!!_0x4bd1de;var _0xb5e5e5=typeof _0x12a2c8==_0x2ead00(0x260),_0x14ad95={'name':_0xb5e5e5||_0x50630b?_0x4de97a:this[_0x2ead00(0x1e6)](_0x4de97a)};if(_0xb5e5e5&&(_0x14ad95['symbol']=!0x0),!(_0x3cde6b==='array'||_0x3cde6b===_0x2ead00(0x28c))){var _0x1b45af=this[_0x2ead00(0x20d)](_0x59443b,_0x12a2c8);if(_0x1b45af&&(_0x1b45af[_0x2ead00(0x26f)]&&(_0x14ad95['setter']=!0x0),_0x1b45af[_0x2ead00(0x275)]&&!_0x4bd1de&&!_0x3b920e[_0x2ead00(0x2b7)]))return _0x14ad95[_0x2ead00(0x270)]=!0x0,this[_0x2ead00(0x254)](_0x14ad95,_0x3b920e),_0x14ad95;}var _0x161919;try{_0x161919=_0x46cc8f(_0x59443b,_0x12a2c8);}catch(_0x3bec8b){return _0x14ad95={'name':_0x4de97a,'type':_0x2ead00(0x20a),'error':_0x3bec8b[_0x2ead00(0x2cf)]},this[_0x2ead00(0x254)](_0x14ad95,_0x3b920e),_0x14ad95;}var _0x21c064=this[_0x2ead00(0x2ac)](_0x161919),_0x1bbe71=this['_isPrimitiveType'](_0x21c064);if(_0x14ad95[_0x2ead00(0x20e)]=_0x21c064,_0x1bbe71)this['_processTreeNodeResult'](_0x14ad95,_0x3b920e,_0x161919,function(){var _0x37c0da=_0x2ead00;_0x14ad95[_0x37c0da(0x22c)]=_0x161919[_0x37c0da(0x1eb)](),!_0x4bd1de&&_0x1ba880[_0x37c0da(0x212)](_0x21c064,_0x14ad95,_0x3b920e,{});});else{var _0x5eec00=_0x3b920e[_0x2ead00(0x2c4)]&&_0x3b920e[_0x2ead00(0x202)]<_0x3b920e[_0x2ead00(0x27b)]&&_0x3b920e[_0x2ead00(0x1df)][_0x2ead00(0x25b)](_0x161919)<0x0&&_0x21c064!=='function'&&_0x3b920e[_0x2ead00(0x2bd)]<_0x3b920e[_0x2ead00(0x21e)];_0x5eec00||_0x3b920e[_0x2ead00(0x202)]<_0x2cb680||_0x4bd1de?(this[_0x2ead00(0x241)](_0x14ad95,_0x161919,_0x3b920e,_0x4bd1de||{}),this['_additionalMetadata'](_0x161919,_0x14ad95)):this[_0x2ead00(0x254)](_0x14ad95,_0x3b920e,_0x161919,function(){var _0x39a200=_0x2ead00;_0x21c064===_0x39a200(0x1f6)||_0x21c064===_0x39a200(0x2a5)||(delete _0x14ad95[_0x39a200(0x22c)],_0x14ad95[_0x39a200(0x230)]=!0x0);});}return _0x14ad95;}finally{_0x3b920e[_0x2ead00(0x246)]=_0x1a668e,_0x3b920e['depth']=_0x2cb680,_0x3b920e[_0x2ead00(0x21c)]=_0x1763ed;}}[_0x2eed80(0x212)](_0xf2a721,_0x4f986e,_0x310ad3,_0x66d14c){var _0x5ad663=_0x2eed80,_0x4978fc=_0x66d14c[_0x5ad663(0x252)]||_0x310ad3[_0x5ad663(0x252)];if((_0xf2a721===_0x5ad663(0x299)||_0xf2a721===_0x5ad663(0x2c3))&&_0x4f986e['value']){let _0xae7309=_0x4f986e[_0x5ad663(0x22c)][_0x5ad663(0x219)];_0x310ad3[_0x5ad663(0x1f5)]+=_0xae7309,_0x310ad3['allStrLength']>_0x310ad3[_0x5ad663(0x2a4)]?(_0x4f986e[_0x5ad663(0x230)]='',delete _0x4f986e[_0x5ad663(0x22c)]):_0xae7309>_0x4978fc&&(_0x4f986e[_0x5ad663(0x230)]=_0x4f986e['value'][_0x5ad663(0x2be)](0x0,_0x4978fc),delete _0x4f986e[_0x5ad663(0x22c)]);}}[_0x2eed80(0x280)](_0x3fd355){var _0x431870=_0x2eed80;return!!(_0x3fd355&&_0x12a823['Map']&&this[_0x431870(0x205)](_0x3fd355)===_0x431870(0x1fd)&&_0x3fd355[_0x431870(0x269)]);}[_0x2eed80(0x1e6)](_0x316386){var _0x3810c7=_0x2eed80;if(_0x316386[_0x3810c7(0x281)](/^\\\\d+$/))return _0x316386;var _0x3a1753;try{_0x3a1753=JSON[_0x3810c7(0x287)](''+_0x316386);}catch{_0x3a1753='\\\\x22'+this[_0x3810c7(0x205)](_0x316386)+'\\\\x22';}return _0x3a1753[_0x3810c7(0x281)](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x3a1753=_0x3a1753[_0x3810c7(0x2be)](0x1,_0x3a1753['length']-0x2):_0x3a1753=_0x3a1753[_0x3810c7(0x2c5)](/'/g,'\\\\x5c\\\\x27')[_0x3810c7(0x2c5)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x3810c7(0x2c5)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x3a1753;}[_0x2eed80(0x254)](_0x3009ee,_0x177a76,_0x5d7522,_0x295842){var _0x58439e=_0x2eed80;this['_treeNodePropertiesBeforeFullValue'](_0x3009ee,_0x177a76),_0x295842&&_0x295842(),this[_0x58439e(0x276)](_0x5d7522,_0x3009ee),this['_treeNodePropertiesAfterFullValue'](_0x3009ee,_0x177a76);}['_treeNodePropertiesBeforeFullValue'](_0x2a26ef,_0x51d3af){var _0x3e6192=_0x2eed80;this[_0x3e6192(0x28e)](_0x2a26ef,_0x51d3af),this[_0x3e6192(0x20c)](_0x2a26ef,_0x51d3af),this[_0x3e6192(0x286)](_0x2a26ef,_0x51d3af),this['_setNodePermissions'](_0x2a26ef,_0x51d3af);}['_setNodeId'](_0x2146df,_0x58005f){}['_setNodeQueryPath'](_0x3a7d8c,_0x2e6fbe){}[_0x2eed80(0x2a3)](_0x33f0c1,_0x47d9ca){}['_isUndefined'](_0x14f6b7){var _0x53e599=_0x2eed80;return _0x14f6b7===this[_0x53e599(0x27c)];}[_0x2eed80(0x284)](_0x543407,_0x40f05c){var _0x5424e7=_0x2eed80;this['_setNodeLabel'](_0x543407,_0x40f05c),this['_setNodeExpandableState'](_0x543407),_0x40f05c[_0x5424e7(0x2cc)]&&this[_0x5424e7(0x1f4)](_0x543407),this[_0x5424e7(0x297)](_0x543407,_0x40f05c),this[_0x5424e7(0x226)](_0x543407,_0x40f05c),this[_0x5424e7(0x1dc)](_0x543407);}[_0x2eed80(0x276)](_0x563a02,_0x52668c){var _0x55bbe4=_0x2eed80;try{_0x563a02&&typeof _0x563a02[_0x55bbe4(0x219)]==_0x55bbe4(0x2ca)&&(_0x52668c[_0x55bbe4(0x219)]=_0x563a02['length']);}catch{}if(_0x52668c[_0x55bbe4(0x20e)]===_0x55bbe4(0x2ca)||_0x52668c[_0x55bbe4(0x20e)]===_0x55bbe4(0x237)){if(isNaN(_0x52668c[_0x55bbe4(0x22c)]))_0x52668c[_0x55bbe4(0x2c8)]=!0x0,delete _0x52668c[_0x55bbe4(0x22c)];else switch(_0x52668c[_0x55bbe4(0x22c)]){case Number[_0x55bbe4(0x27e)]:_0x52668c[_0x55bbe4(0x1e2)]=!0x0,delete _0x52668c[_0x55bbe4(0x22c)];break;case Number['NEGATIVE_INFINITY']:_0x52668c[_0x55bbe4(0x278)]=!0x0,delete _0x52668c[_0x55bbe4(0x22c)];break;case 0x0:this['_isNegativeZero'](_0x52668c[_0x55bbe4(0x22c)])&&(_0x52668c[_0x55bbe4(0x29d)]=!0x0);break;}}else _0x52668c[_0x55bbe4(0x20e)]===_0x55bbe4(0x257)&&typeof _0x563a02[_0x55bbe4(0x2b1)]==_0x55bbe4(0x299)&&_0x563a02[_0x55bbe4(0x2b1)]&&_0x52668c[_0x55bbe4(0x2b1)]&&_0x563a02[_0x55bbe4(0x2b1)]!==_0x52668c[_0x55bbe4(0x2b1)]&&(_0x52668c['funcName']=_0x563a02['name']);}[_0x2eed80(0x2a8)](_0x3f3440){var _0x3f6395=_0x2eed80;return 0x1/_0x3f3440===Number[_0x3f6395(0x2b0)];}[_0x2eed80(0x1f4)](_0x4db13d){var _0x2f34a9=_0x2eed80;!_0x4db13d['props']||!_0x4db13d[_0x2f34a9(0x215)][_0x2f34a9(0x219)]||_0x4db13d[_0x2f34a9(0x20e)]==='array'||_0x4db13d['type']===_0x2f34a9(0x273)||_0x4db13d[_0x2f34a9(0x20e)]===_0x2f34a9(0x22f)||_0x4db13d[_0x2f34a9(0x215)]['sort'](function(_0x59e2dd,_0x422c21){var _0x2c2d1f=_0x2f34a9,_0x2d3101=_0x59e2dd[_0x2c2d1f(0x2b1)]['toLowerCase'](),_0x1ec453=_0x422c21['name']['toLowerCase']();return _0x2d3101<_0x1ec453?-0x1:_0x2d3101>_0x1ec453?0x1:0x0;});}[_0x2eed80(0x297)](_0x5e7d7d,_0x19076e){var _0x205273=_0x2eed80;if(!(_0x19076e[_0x205273(0x1e8)]||!_0x5e7d7d[_0x205273(0x215)]||!_0x5e7d7d['props']['length'])){for(var _0x337c24=[],_0xb81ecc=[],_0x164226=0x0,_0xe5cbb0=_0x5e7d7d['props'][_0x205273(0x219)];_0x164226<_0xe5cbb0;_0x164226++){var _0x4bc7f6=_0x5e7d7d[_0x205273(0x215)][_0x164226];_0x4bc7f6[_0x205273(0x20e)]===_0x205273(0x257)?_0x337c24['push'](_0x4bc7f6):_0xb81ecc[_0x205273(0x2a1)](_0x4bc7f6);}if(!(!_0xb81ecc[_0x205273(0x219)]||_0x337c24['length']<=0x1)){_0x5e7d7d[_0x205273(0x215)]=_0xb81ecc;var _0x18a450={'functionsNode':!0x0,'props':_0x337c24};this['_setNodeId'](_0x18a450,_0x19076e),this[_0x205273(0x2a3)](_0x18a450,_0x19076e),this[_0x205273(0x2cd)](_0x18a450),this['_setNodePermissions'](_0x18a450,_0x19076e),_0x18a450['id']+='\\\\x20f',_0x5e7d7d['props'][_0x205273(0x1f0)](_0x18a450);}}}['_addLoadNode'](_0x188d85,_0x22b0e8){}[_0x2eed80(0x2cd)](_0x3f4796){}[_0x2eed80(0x2d1)](_0x14f503){var _0x5c5ac4=_0x2eed80;return Array['isArray'](_0x14f503)||typeof _0x14f503==_0x5c5ac4(0x2ce)&&this['_objectToString'](_0x14f503)===_0x5c5ac4(0x21d);}[_0x2eed80(0x255)](_0x4b2180,_0x47a780){}[_0x2eed80(0x1dc)](_0x4da028){var _0x40cde9=_0x2eed80;delete _0x4da028[_0x40cde9(0x283)],delete _0x4da028[_0x40cde9(0x248)],delete _0x4da028[_0x40cde9(0x221)];}[_0x2eed80(0x286)](_0x6c9a30,_0x4e37ff){}}let _0x2255ee=new _0x14076b(),_0x5e758c={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x229f10={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x185e9c(_0x23f67b,_0x30d583,_0x45911d,_0xa171e2,_0x16294e,_0x701f43){var _0x7272de=_0x2eed80;let _0x49befc,_0x534046;try{_0x534046=_0x1c23a1(),_0x49befc=_0x3b2d54[_0x30d583],!_0x49befc||_0x534046-_0x49befc['ts']>0x1f4&&_0x49befc[_0x7272de(0x217)]&&_0x49befc[_0x7272de(0x2ad)]/_0x49befc[_0x7272de(0x217)]<0x64?(_0x3b2d54[_0x30d583]=_0x49befc={'count':0x0,'time':0x0,'ts':_0x534046},_0x3b2d54[_0x7272de(0x242)]={}):_0x534046-_0x3b2d54[_0x7272de(0x242)]['ts']>0x32&&_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x217)]&&_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x2ad)]/_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x217)]<0x64&&(_0x3b2d54[_0x7272de(0x242)]={});let _0x1642e9=[],_0x2032bd=_0x49befc[_0x7272de(0x1fb)]||_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x1fb)]?_0x229f10:_0x5e758c,_0x4bc7a5=_0x4350dc=>{var _0x4e8323=_0x7272de;let _0x3fd97={};return _0x3fd97[_0x4e8323(0x215)]=_0x4350dc['props'],_0x3fd97[_0x4e8323(0x285)]=_0x4350dc['elements'],_0x3fd97['strLength']=_0x4350dc[_0x4e8323(0x252)],_0x3fd97[_0x4e8323(0x2a4)]=_0x4350dc[_0x4e8323(0x2a4)],_0x3fd97[_0x4e8323(0x21e)]=_0x4350dc[_0x4e8323(0x21e)],_0x3fd97[_0x4e8323(0x27b)]=_0x4350dc['autoExpandMaxDepth'],_0x3fd97[_0x4e8323(0x2cc)]=!0x1,_0x3fd97[_0x4e8323(0x1e8)]=!_0x4b5443,_0x3fd97[_0x4e8323(0x277)]=0x1,_0x3fd97[_0x4e8323(0x202)]=0x0,_0x3fd97[_0x4e8323(0x28f)]=_0x4e8323(0x2b4),_0x3fd97[_0x4e8323(0x2c6)]=_0x4e8323(0x1db),_0x3fd97[_0x4e8323(0x2c4)]=!0x0,_0x3fd97[_0x4e8323(0x1df)]=[],_0x3fd97[_0x4e8323(0x2bd)]=0x0,_0x3fd97[_0x4e8323(0x2b7)]=!0x0,_0x3fd97[_0x4e8323(0x1f5)]=0x0,_0x3fd97[_0x4e8323(0x216)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x3fd97;};for(var _0x495e5f=0x0;_0x495e5f<_0x16294e[_0x7272de(0x219)];_0x495e5f++)_0x1642e9[_0x7272de(0x2a1)](_0x2255ee['serialize']({'timeNode':_0x23f67b===_0x7272de(0x2ad)||void 0x0},_0x16294e[_0x495e5f],_0x4bc7a5(_0x2032bd),{}));if(_0x23f67b==='trace'||_0x23f67b===_0x7272de(0x214)){let _0xe53cb5=Error['stackTraceLimit'];try{Error[_0x7272de(0x229)]=0x1/0x0,_0x1642e9['push'](_0x2255ee[_0x7272de(0x241)]({'stackNode':!0x0},new Error()['stack'],_0x4bc7a5(_0x2032bd),{'strLength':0x1/0x0}));}finally{Error['stackTraceLimit']=_0xe53cb5;}}return{'method':_0x7272de(0x266),'version':_0x31b7ae,'args':[{'ts':_0x45911d,'session':_0xa171e2,'args':_0x1642e9,'id':_0x30d583,'context':_0x701f43}]};}catch(_0x39377c){return{'method':_0x7272de(0x266),'version':_0x31b7ae,'args':[{'ts':_0x45911d,'session':_0xa171e2,'args':[{'type':_0x7272de(0x20a),'error':_0x39377c&&_0x39377c['message']}],'id':_0x30d583,'context':_0x701f43}]};}finally{try{if(_0x49befc&&_0x534046){let _0x24bf47=_0x1c23a1();_0x49befc[_0x7272de(0x217)]++,_0x49befc[_0x7272de(0x2ad)]+=_0x2b4c42(_0x534046,_0x24bf47),_0x49befc['ts']=_0x24bf47,_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x217)]++,_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x2ad)]+=_0x2b4c42(_0x534046,_0x24bf47),_0x3b2d54[_0x7272de(0x242)]['ts']=_0x24bf47,(_0x49befc[_0x7272de(0x217)]>0x32||_0x49befc['time']>0x64)&&(_0x49befc[_0x7272de(0x1fb)]=!0x0),(_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x217)]>0x3e8||_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x2ad)]>0x12c)&&(_0x3b2d54[_0x7272de(0x242)]['reduceLimits']=!0x0);}}catch{}}}return _0x185e9c;}((_0x1c4338,_0x5e9340,_0x31589f,_0xdf79ea,_0x2b930f,_0x1cdd37,_0x3bea31,_0x47320d,_0x4ceab9,_0x3a521d,_0x2685b4)=>{var _0x44bcf6=_0x3afe01;if(_0x1c4338[_0x44bcf6(0x245)])return _0x1c4338[_0x44bcf6(0x245)];if(!X(_0x1c4338,_0x47320d,_0x2b930f))return _0x1c4338[_0x44bcf6(0x245)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x1c4338[_0x44bcf6(0x245)];let _0x5465b7=B(_0x1c4338),_0x229b1c=_0x5465b7[_0x44bcf6(0x1fc)],_0x44593f=_0x5465b7['timeStamp'],_0x233152=_0x5465b7[_0x44bcf6(0x26b)],_0x16eb8c={'hits':{},'ts':{}},_0xb3957=J(_0x1c4338,_0x4ceab9,_0x16eb8c,_0x1cdd37),_0x33c402=_0x15a9ae=>{_0x16eb8c['ts'][_0x15a9ae]=_0x44593f();},_0x1976d2=(_0x3116b1,_0xb164e6)=>{var _0xf8d0c4=_0x44bcf6;let _0x4216b4=_0x16eb8c['ts'][_0xb164e6];if(delete _0x16eb8c['ts'][_0xb164e6],_0x4216b4){let _0x1cac4f=_0x229b1c(_0x4216b4,_0x44593f());_0x395c1c(_0xb3957(_0xf8d0c4(0x2ad),_0x3116b1,_0x233152(),_0x5297a1,[_0x1cac4f],_0xb164e6));}},_0xe0d8e0=_0x1d5b88=>{var _0x2b7a80=_0x44bcf6,_0x12ff84;return _0x2b930f===_0x2b7a80(0x234)&&_0x1c4338[_0x2b7a80(0x22e)]&&((_0x12ff84=_0x1d5b88==null?void 0x0:_0x1d5b88[_0x2b7a80(0x28d)])==null?void 0x0:_0x12ff84[_0x2b7a80(0x219)])&&(_0x1d5b88['args'][0x0][_0x2b7a80(0x22e)]=_0x1c4338[_0x2b7a80(0x22e)]),_0x1d5b88;};_0x1c4338[_0x44bcf6(0x245)]={'consoleLog':(_0x353fbf,_0x9587ce)=>{var _0x286e08=_0x44bcf6;_0x1c4338[_0x286e08(0x2c7)][_0x286e08(0x266)]['name']!=='disabledLog'&&_0x395c1c(_0xb3957(_0x286e08(0x266),_0x353fbf,_0x233152(),_0x5297a1,_0x9587ce));},'consoleTrace':(_0x2e6a95,_0x4fce44)=>{var _0x954962=_0x44bcf6,_0x1a1c91,_0x18d302;_0x1c4338[_0x954962(0x2c7)][_0x954962(0x266)]['name']!==_0x954962(0x2a9)&&((_0x18d302=(_0x1a1c91=_0x1c4338[_0x954962(0x298)])==null?void 0x0:_0x1a1c91[_0x954962(0x1de)])!=null&&_0x18d302['node']&&(_0x1c4338[_0x954962(0x289)]=!0x0),_0x395c1c(_0xe0d8e0(_0xb3957(_0x954962(0x27d),_0x2e6a95,_0x233152(),_0x5297a1,_0x4fce44))));},'consoleError':(_0x81b750,_0x437d74)=>{var _0x217a4e=_0x44bcf6;_0x1c4338[_0x217a4e(0x289)]=!0x0,_0x395c1c(_0xe0d8e0(_0xb3957(_0x217a4e(0x214),_0x81b750,_0x233152(),_0x5297a1,_0x437d74)));},'consoleTime':_0x2b6249=>{_0x33c402(_0x2b6249);},'consoleTimeEnd':(_0x5cf646,_0x1e04f7)=>{_0x1976d2(_0x1e04f7,_0x5cf646);},'autoLog':(_0x19290d,_0x149495)=>{var _0x45b206=_0x44bcf6;_0x395c1c(_0xb3957(_0x45b206(0x266),_0x149495,_0x233152(),_0x5297a1,[_0x19290d]));},'autoLogMany':(_0x4a789,_0x5f465c)=>{_0x395c1c(_0xb3957('log',_0x4a789,_0x233152(),_0x5297a1,_0x5f465c));},'autoTrace':(_0x1cd22d,_0x15ecc1)=>{var _0x20cec0=_0x44bcf6;_0x395c1c(_0xe0d8e0(_0xb3957(_0x20cec0(0x27d),_0x15ecc1,_0x233152(),_0x5297a1,[_0x1cd22d])));},'autoTraceMany':(_0x22fd11,_0x4965a1)=>{var _0x552a3c=_0x44bcf6;_0x395c1c(_0xe0d8e0(_0xb3957(_0x552a3c(0x27d),_0x22fd11,_0x233152(),_0x5297a1,_0x4965a1)));},'autoTime':(_0x4ada81,_0x24f7ff,_0x5050a6)=>{_0x33c402(_0x5050a6);},'autoTimeEnd':(_0x12cdfb,_0x54b3fc,_0x52c0ad)=>{_0x1976d2(_0x54b3fc,_0x52c0ad);},'coverage':_0x104f1c=>{var _0x2425e6=_0x44bcf6;_0x395c1c({'method':_0x2425e6(0x1e1),'version':_0x1cdd37,'args':[{'id':_0x104f1c}]});}};let _0x395c1c=H(_0x1c4338,_0x5e9340,_0x31589f,_0xdf79ea,_0x2b930f,_0x3a521d,_0x2685b4),_0x5297a1=_0x1c4338['_console_ninja_session'];return _0x1c4338[_0x44bcf6(0x245)];})(globalThis,_0x3afe01(0x23f),_0x3afe01(0x24e),_0x3afe01(0x1fa),_0x3afe01(0x2aa),'1.0.0','1754039962045',_0x3afe01(0x210),_0x3afe01(0x25a),_0x3afe01(0x1fe),_0x3afe01(0x207));\");\n  } catch (e) {}\n}\n; /* istanbul ignore next */\nfunction oo_oo(/**@type{any}**/i, /**@type{any}**/...v) {\n  try {\n    oo_cm().consoleLog(i, v);\n  } catch (e) {}\n  return v;\n}\n; /* istanbul ignore next */\nfunction oo_tr(/**@type{any}**/i, /**@type{any}**/...v) {\n  try {\n    oo_cm().consoleTrace(i, v);\n  } catch (e) {}\n  return v;\n}\n; /* istanbul ignore next */\nfunction oo_tx(/**@type{any}**/i, /**@type{any}**/...v) {\n  try {\n    oo_cm().consoleError(i, v);\n  } catch (e) {}\n  return v;\n}\n; /* istanbul ignore next */\nfunction oo_ts(/**@type{any}**/v) {\n  try {\n    oo_cm().consoleTime(v);\n  } catch (e) {}\n  return v;\n}\n; /* istanbul ignore next */\nfunction oo_te(/**@type{any}**/v, /**@type{any}**/i) {\n  try {\n    oo_cm().consoleTimeEnd(v, i);\n  } catch (e) {}\n  return v;\n}\n; /*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "activeProjectId", "setActiveProjectId", "_s", "show", "setShow", "appName", "setAppName", "projectNames", "setProjectNames", "flashMsg", "setFlashMsg", "editProjectId", "setEditProjectId", "editProjectName", "setEditProjectName", "projectUrl", "setProjectUrl", "editProjectUrl", "setEditProjectUrl", "fetch", "then", "res", "ok", "Error", "json", "data", "Array", "isArray", "project_names", "project_url", "length", "project_id", "catch", "err", "console", "error", "oo_tx", "log", "oo_oo", "window", "bootstrap", "initializeDropdowns", "dropdownElements", "document", "querySelectorAll", "for<PERSON>ach", "element", "index", "Dropdown", "getInstance", "timeout", "setTimeout", "clearTimeout", "handleInputChange", "e", "target", "value", "handleSubmit", "trim", "type", "text", "method", "headers", "body", "JSON", "stringify", "project_name", "description", "detail", "prev", "modalEl", "getElementById", "Modal", "modal", "hide", "openEditModal", "projectId", "projectName", "handleEditInputChange", "handleUpdateProject", "map", "p", "resetEditForm", "handleDeleteProject", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "confirmDeleteProject", "filter", "firstProject", "find", "handleProjectClick", "handleDropdownClick", "action", "preventDefault", "stopPropagation", "dropdownButton", "closest", "querySelector", "dropdown", "handleDropdownToggle", "currentTarget", "toggle", "sidebarClass", "style", "zIndex", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "borderRadius", "border", "background", "color", "fontWeight", "boxShadow", "padding", "display", "alignItems", "gap", "role", "flex", "opacity", "marginLeft", "width", "top", "left", "transition", "backgroundColor", "position", "right", "marginTop", "proj", "idx", "fontSize", "lineHeight", "height", "id", "link", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "alert", "tabIndex", "htmlFor", "placeholder", "onChange", "whiteSpace", "found", "message", "_c", "oo_cm", "eval", "i", "v", "consoleLog", "oo_tr", "consoleTrace", "consoleError", "oo_ts", "consoleTime", "oo_te", "consoleTimeEnd", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/MCP-Project-FullStack-local/MCP-Project-FullStack/mcp-react/src/Components/Navbar/Sidebar/Sidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\n\nfunction Sidebar({ activeProjectId, setActiveProjectId }) {\n  const [show, setShow] = useState(false);\n  const [appName, setAppName] = useState('');\n  const [projectNames, setProjectNames] = useState([]);\n  const [flashMsg, setFlashMsg] = useState(null);\n  const [editProjectId, setEditProjectId] = useState(null);\n  const [editProjectName, setEditProjectName] = useState('');\n  const [projectUrl, setProjectUrl] = useState('');\n  const [editProjectUrl, setEditProjectUrl] = useState('');\n\n  useEffect(() => {\n    fetch('http://127.0.0.1:8000/api/projects/names')\n      .then(res => {\n        if (!res.ok) throw new Error('Network response was not ok');\n        return res.json();\n      })\n      .then(data => {\n        if (data && Array.isArray(data.project_names)) {\n          setProjectNames(data.project_names);\n          setProjectUrl(data.project_names[0].project_url);\n          // Only set the first project as active if not already set\n          if (data.project_names.length > 0 && !activeProjectId) {\n            setActiveProjectId(data.project_names[0].project_id);\n          }\n        } else {\n          setProjectNames([]);\n        }\n      })\n      .catch((err) => {\n        /* eslint-disable */console.error(...oo_tx(`1105938802_33_8_33_60_11`,'Failed to fetch project names:', err));\n        setProjectNames([]);\n      });\n  }, [setActiveProjectId, activeProjectId]);\n\n  // Initialize Bootstrap dropdowns after component mounts and updates\n  useEffect(() => {\n    // Add debugging\n    /* eslint-disable */console.log(...oo_oo(`1105938802_41_4_41_86_4`,'Bootstrap check:', typeof window !== 'undefined' && window.bootstrap));\n    /* eslint-disable */console.log(...oo_oo(`1105938802_42_4_42_54_4`,'Bootstrap object:', window.bootstrap));\n    \n    const initializeDropdowns = () => {\n      const dropdownElements = document.querySelectorAll('[data-bs-toggle=\"dropdown\"]');\n      /* eslint-disable */console.log(...oo_oo(`1105938802_46_6_46_70_4`,'Found dropdown elements:', dropdownElements.length));\n      \n      dropdownElements.forEach((element, index) => {\n        /* eslint-disable */console.log(...oo_oo(`1105938802_49_8_49_63_4`,`Initializing dropdown ${index}:`, element));\n        if (window.bootstrap && window.bootstrap.Dropdown) {\n          if (!window.bootstrap.Dropdown.getInstance(element)) {\n            try {\n              new window.bootstrap.Dropdown(element);\n              /* eslint-disable */console.log(...oo_oo(`1105938802_54_14_54_71_4`,`Successfully initialized dropdown ${index}`));\n            } catch (error) {\n              /* eslint-disable */console.error(...oo_tx(`1105938802_56_14_56_77_11`,`Failed to initialize dropdown ${index}:`, error));\n            }\n          }\n        }\n      });\n    };\n\n    // Try to initialize immediately\n    if (typeof window !== 'undefined' && window.bootstrap) {\n      initializeDropdowns();\n    } else {\n      // If Bootstrap isn't loaded yet, wait a bit and try again\n      const timeout = setTimeout(() => {\n        /* eslint-disable */console.log(...oo_oo(`1105938802_69_8_69_59_4`,'Retrying Bootstrap initialization...'));\n        if (window.bootstrap) {\n          initializeDropdowns();\n        } else {\n          /* eslint-disable */console.error(...oo_tx(`1105938802_73_10_73_56_11`,'Bootstrap is still not loaded'));\n        }\n      }, 500);\n      \n      return () => clearTimeout(timeout);\n    }\n  }, [projectNames]); // Re-run when projectNames changes\n\n  const handleInputChange = (e) => {\n    setAppName(e.target.value);\n  };\n\n  const handleSubmit = () => {\n    if (!appName.trim()) {\n      setFlashMsg({ type: 'warning', text: 'Please enter an application name.' });\n      return;\n    }\n  \n    fetch('http://127.0.0.1:8000/projects/', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({\n        project_name: appName,\n        description: '',\n        project_url: projectUrl\n      })\n    })\n      .then(async res => {\n        let data;\n        try {\n          data = await res.json();\n        } catch {\n          data = {};\n        }\n  \n        if (!res.ok && data.detail === \"Project name already exists\") {\n          setFlashMsg({ type: 'warning', text: \"Project name already exists.\" });\n        } else if (res.ok) {\n          setFlashMsg({ type: 'success', text: 'Application added successfully!' });\n          setProjectNames(prev => [{...data}, ...prev]);\n        } else {\n          setFlashMsg({ type: 'danger', text: data.detail || 'Failed to add application' });\n        }\n  \n        setAppName('');\n        setProjectUrl('');\n        setTimeout(() => {\n          const modalEl = document.getElementById('addAppModal');\n          if (window.bootstrap && window.bootstrap.Modal && modalEl) {\n            const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);\n            modal.hide();\n          }\n        }, 100);\n      })\n      .catch(() => {\n        setFlashMsg({ type: 'danger', text: 'Failed to add application' });\n        setTimeout(() => {\n          const modalEl = document.getElementById('addAppModal');\n          if (window.bootstrap && window.bootstrap.Modal && modalEl) {\n            const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);\n            modal.hide();\n          }\n        }, 100);\n      });\n  };\n\n  // Open Edit Modal and set project info\n  const openEditModal = (projectId, projectName, projectUrl = '') => {\n    setEditProjectId(projectId);\n    setEditProjectName(projectName);\n    setEditProjectUrl(projectUrl);\n  \n    setTimeout(() => {\n      const modalEl = document.getElementById('editAppModal');\n      if (modalEl && window.bootstrap && window.bootstrap.Modal) {\n        const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);\n        modal.show();\n      }\n    }, 100);\n  };\n  \n\n  // Handle edit input change\n  const handleEditInputChange = (e) => {\n    setEditProjectName(e.target.value);\n  };\n\n  // Handle update project\n  const handleUpdateProject = () => {\n    if (!editProjectName.trim()) {\n      setFlashMsg({ type: 'warning', text: 'Please enter an application name.' });\n      return;\n    }\n  \n    fetch(`http://127.0.0.1:8000/projects/${editProjectId}`, {\n      method: 'PUT',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({\n        project_name: editProjectName,\n        description: '',\n        project_url: editProjectUrl\n      })\n    })\n      .then(async res => {\n        let data;\n        try {\n          data = await res.json();\n        } catch {\n          data = {};\n        }\n  \n        if (!res.ok) {\n          setFlashMsg({ type: 'warning', text: data.detail || \"Failed to update project.\" });\n        } else {\n          setFlashMsg({ type: 'success', text: 'Application updated successfully!' });\n          setProjectNames(prev => prev.map(p =>\n            p.project_id === editProjectId\n              ? { ...p, project_name: editProjectName, project_url: editProjectUrl }\n              : p\n          ));\n        }\n  \n        resetEditForm();\n      })\n      .catch(() => {\n        setFlashMsg({ type: 'danger', text: 'Failed to update application' });\n        resetEditForm();\n      });\n  };\n  \n\n  // Reset edit modal state and close modal\n  const resetEditForm = () => {\n    setEditProjectId(null);\n    setEditProjectName('');\n    setTimeout(() => {\n      const modalEl = document.getElementById('editAppModal');\n      if (window.bootstrap && window.bootstrap.Modal && modalEl) {\n        const modal = window.bootstrap.Modal.getInstance(modalEl) || new window.bootstrap.Modal(modalEl);\n        modal.hide();\n      }\n    }, 100);\n  };\n\n  // Handle delete project with confirmation\n  const handleDeleteProject = (projectId, projectName) => {\n    setFlashMsg({\n      type: 'warning',\n      text: (\n        <>\n          Are you sure you want to delete <b>{projectName}</b>?\n          <div className=\"mt-2 d-flex gap-2 justify-content-end\">\n            <button\n              className=\"btn btn-sm btn-outline-secondary\"\n              onClick={() => setFlashMsg(null)}\n            >\n              Cancel\n            </button>\n            <button\n              className=\"btn btn-sm btn-danger\"\n              onClick={() => confirmDeleteProject(projectId)}\n            >\n              Delete\n            </button>\n          </div>\n        </>\n      )\n    });\n  };\n\n  // Actually delete the project\n  const confirmDeleteProject = (projectId) => {\n    setFlashMsg(null);\n    fetch(`http://127.0.0.1:8000/projects/${projectId}`, {\n      method: 'DELETE',\n    })\n      .then(async res => {\n        let data;\n        try {\n          data = await res.json();\n        } catch {\n          data = {};\n        }\n        if (!res.ok) {\n          setFlashMsg({ type: 'danger', text: data.detail || 'Failed to delete application' });\n        } else {\n          setFlashMsg({ type: 'success', text: 'Application deleted successfully!' });\n          setProjectNames(prev => prev.filter(p => p.project_id !== projectId));\n          // If active project was deleted, set active to first available or null\n          if (activeProjectId === projectId) {\n            const firstProject = projectNames.find(p => p.project_id !== projectId);\n            setActiveProjectId(firstProject ? firstProject.project_id : null);\n          }\n        }\n      })\n      .catch(() => {\n        setFlashMsg({ type: 'danger', text: 'Failed to delete application' });\n      });\n  };\n\n  // Handle project item click (prevent dropdown from interfering)\n  const handleProjectClick = (projectId) => {\n    /* eslint-disable */console.log(...oo_oo(`1105938802_275_4_275_26_4`,projectId));\n    setActiveProjectId(projectId);\n  };\n\n  // Handle dropdown menu clicks\n  const handleDropdownClick = (e, action, projectId, projectName, projectUrl = '') => {\n    e.preventDefault();\n    e.stopPropagation();\n  \n    const dropdownButton = e.target.closest('.dropdown').querySelector('[data-bs-toggle=\"dropdown\"]');\n    if (dropdownButton && window.bootstrap && window.bootstrap.Dropdown) {\n      const dropdown = window.bootstrap.Dropdown.getInstance(dropdownButton);\n      if (dropdown) {\n        dropdown.hide();\n      }\n    }\n  \n    if (action === 'edit') {\n      openEditModal(projectId, projectName, projectUrl);\n    } else if (action === 'delete') {\n      handleDeleteProject(projectId, projectName);\n    }\n  };\n  \n\n  // Alternative click handler for testing\n  const handleDropdownToggle = (e) => {\n    e.stopPropagation();\n    /* eslint-disable */console.log(...oo_oo(`1105938802_303_4_303_42_4`,'Dropdown toggle clicked'));\n    \n    const dropdownButton = e.currentTarget;\n    /* eslint-disable */console.log(...oo_oo(`1105938802_306_4_306_51_4`,'Dropdown button:', dropdownButton));\n    \n    if (window.bootstrap && window.bootstrap.Dropdown) {\n      const dropdown = window.bootstrap.Dropdown.getInstance(dropdownButton) || new window.bootstrap.Dropdown(dropdownButton);\n      /* eslint-disable */console.log(...oo_oo(`1105938802_310_6_310_49_4`,'Dropdown instance:', dropdown));\n      dropdown.toggle();\n    } else {\n      /* eslint-disable */console.error(...oo_tx(`1105938802_313_6_313_55_11`,'Bootstrap Dropdown not available'));\n    }\n  };\n\n  // Sidebar classes for responsive behavior\n  const sidebarClass =\n    'd-flex flex-column flex-shrink-0 p-2 border-end position-fixed h-100 m-2 rounded border border-2' +\n    ' ' +\n    (show ? 'd-block' : 'd-none d-md-flex');\n\n  return (\n    <>\n      {/* Flash message */}\n      {flashMsg && (\n        <div\n          className={`alert alert-${flashMsg.type} alert-dismissible show position-fixed top-0 start-50 translate-middle-x mt-3 shadow `}\n          style={{\n            zIndex: 2000,\n            minWidth: 320,\n            maxWidth: 500,\n            borderRadius: 12,\n            border: 'none',\n            background:\n              flashMsg.type === 'success'\n                ? 'linear-gradient(90deg, #d1fae5 0%, #bbf7d0 100%)'\n                : flashMsg.type === 'danger'\n                ? 'linear-gradient(90deg, #fee2e2 0%, #fecaca 100%)'\n                : flashMsg.type === 'warning'\n                ? 'linear-gradient(90deg, #fef9c3 0%, #fde68a 100%)'\n                : '#f9f9f9',\n            color:\n              flashMsg.type === 'success'\n                ? '#065f46'\n                : flashMsg.type === 'danger'\n                ? '#991b1b'\n                : flashMsg.type === 'warning'\n                ? '#92400e'\n                : '#222',\n            fontWeight: 500,\n            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.18)',\n            padding: '1rem 1.5rem',\n            display: 'flex',\n            alignItems: 'center',\n            gap: 12,\n          }}\n          role=\"alert\"\n        >\n          <span style={{ flex: 1 }}>{flashMsg.text}</span>\n          <button\n            type=\"button\"\n            className=\"btn-close\"\n            aria-label=\"Close\"\n            style={{\n              filter: 'invert(0.5)',\n              opacity: 0.7,\n              marginLeft: 8,\n            }}\n            onClick={() => setFlashMsg(null)}\n          ></button>\n        </div>\n      )}\n      \n      {/* Hamburger for mobile */}\n      <button\n        className=\"btn btn-outline-secondary d-md-none m-2 position-fixed\"\n        style={{ zIndex: 400 }}\n        onClick={() => setShow(!show)}\n      >\n        <i className=\"bi bi-list\"></i>\n      </button>\n\n      {/* Sidebar */}\n      <nav\n        className={sidebarClass}\n        style={{\n          width: 280,\n          top: 0,\n          left: 0,\n          zIndex: 1040,\n          transition: 'transform 0.3s ease-in-out',\n          backgroundColor: 'white'\n        }}\n      >\n        {/* X Close Button for mobile */}\n        {show && (\n          <button\n            className=\"btn btn-outline-secondary d-md-none ms-auto mb-2\"\n            style={{ position: 'absolute', top: 10, right: 10, zIndex: 1060 }}\n            onClick={() => setShow(false)}\n            aria-label=\"Close sidebar\"\n          >\n            <i className=\"bi bi-x-lg\"></i>\n          </button>\n        )}\n        \n        {/* Add New Application */}\n        <button\n          className=\"btn btn-outline-dark d-flex align-items-center mb-2 w-100 fs-6\"\n          data-bs-toggle=\"modal\"\n          data-bs-target=\"#addAppModal\"\n          style={{\n            marginTop: show ? 60 : 0\n          }}\n        >\n          <i className=\"bi bi-folder-plus me-2\"></i>\n          Add New Application\n        </button>\n\n        {/* Application List */}\n        <ul className=\"nav nav-pills flex-column mb-auto\">\n          {projectNames.length === 0 ? (\n            <li className=\"nav-item fs-6 text-muted px-3 py-2\">No Applications</li>\n          ) : (\n            projectNames.map((proj, idx) => (\n              <li className=\"nav-item fs-6 position-relative m-1 \" key={proj.project_id || idx}>\n                <button\n                  type=\"button\"\n                  className={\n                    \"nav-link d-flex align-items-center fs-6 btn btn-link w-100 text-start position-relative border pb-2 \" +\n                    (activeProjectId === proj.project_id ? \" active\" : \" text-dark\")\n                  }\n                  style={\n                    activeProjectId === proj.project_id\n                      ? { background: '#d3d3d4', color: '#000' }\n                      : {}\n                  }\n                  onClick={() => handleProjectClick(proj.project_id)}\n                >\n                  <i className=\"bi bi-folder me-2\"></i>\n                  <span className=\"flex-grow-1\">{proj.project_name}</span>\n                  \n                  {/* Three-dot menu */}\n                  <div className=\"dropdown\">\n                    <button\n                      className=\"btn btn-link p-0 text-dark\"\n                      type=\"button\"\n                      data-bs-toggle=\"dropdown\"\n                      aria-expanded=\"false\"\n                      onClick={handleDropdownToggle}\n                      style={{ \n                        marginLeft: 'auto',\n                        fontSize: '1.2rem',\n                        lineHeight: 1\n                      }}\n                    >\n                      <i className=\"bi bi-three-dots-vertical\"></i>\n                    </button>\n                    <ul className=\"dropdown-menu dropdown-menu-end\">\n                      <li>\n                        <button\n                          className=\"dropdown-item\"\n                          onClick={(e) => handleDropdownClick(e, 'edit', proj.project_id, proj.project_name, proj.project_url)}\n\n                        >\n                          <i className=\"bi bi-pencil me-2\"></i>Edit\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          className=\"dropdown-item text-danger\"\n                          onClick={(e) => handleDropdownClick(e, 'delete', proj.project_id, proj.project_name)}\n                        >\n                          <i className=\"bi bi-trash me-2\"></i>Delete\n                        </button>\n                      </li>\n                    </ul>\n                  </div>\n                </button>\n              </li>\n            ))\n          )}\n        </ul>\n\n        {/* Spacer */}\n        <div className=\"flex-grow-1\"></div>\n\n        {/* User Info */}\n        <div className=\"border rounded d-flex align-items-center p-2 mb-2\">\n          <div\n            className=\"rounded-circle bg-secondary me-2\"\n            style={{ width: 32, height: 32 }}\n          ></div>\n          <div>\n            <span\n              className=\"badge bg-light text-secondary mb-1\"\n              style={{ fontSize: '0.7rem' }}\n            >\n              Free member\n            </span>\n            <div style={{ fontSize: '1rem' }}>First Last Name</div>\n          </div>\n          <div className=\"dropdown ms-auto\">\n            <button\n              className=\"btn btn-link ms-auto p-0 text-dark\"\n              type=\"button\"\n              id=\"userMenuDropdown\"\n              data-bs-toggle=\"dropdown\"\n              aria-expanded=\"false\"\n              style={{ boxShadow: \"none\" }}\n            >\n              <i className=\"bi bi-three-dots-vertical\"></i>\n            </button>\n            <ul className=\"dropdown-menu dropdown-menu-end\" aria-labelledby=\"userMenuDropdown\">\n              <li>\n                <button className=\"dropdown-item\" onClick={() => {\n                  const link = document.createElement(\"a\");\n                  link.href = \"/extension.zip\";\n                  link.download = \"extension.zip\";\n                  document.body.appendChild(link);\n                  link.click();\n                  document.body.removeChild(link);\n                }}>\n                  <i className=\"bi bi-download me-2\"></i>Download Extension\n                </button>\n              </li>\n              <li>\n                <button className=\"dropdown-item\" onClick={() => alert('Settings clicked')}>\n                  <i className=\"bi bi-gear me-2\"></i>Settings\n                </button>\n              </li>\n              <li>\n                <button className=\"dropdown-item text-danger\" onClick={() => alert('Logout clicked')}>\n                  <i className=\"bi bi-box-arrow-right me-2\"></i>Logout\n                </button>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </nav>\n\n      {/* Overlay for mobile when sidebar is open */}\n      {show && (\n        <div\n          className=\"position-fixed top-0 start-0 w-100 h-100\"\n          style={{ background: 'rgba(0,0,0,0.2)', zIndex: 1039 }}\n          onClick={() => setShow(false)}\n        ></div>\n      )}\n\n      {/* Add Application Modal */}\n      <div\n        className=\"modal fade\"\n        id=\"addAppModal\"\n        tabIndex=\"-1\"\n        aria-labelledby=\"addAppModalLabel\"\n        aria-hidden=\"true\"\n      >\n        <div className=\"modal-dialog modal-dialog-centered\">\n          <div className=\"modal-content\">\n            <div className=\"modal-header\">\n              <h5 className=\"modal-title\" id=\"addAppModalLabel\">Add Application</h5>\n              <button\n                type=\"button\"\n                className=\"btn btn-light rounded-circle d-flex align-items-center justify-content-center shadow-sm ms-auto\"\n                style={{\n                  width: '32px',\n                  height: '32px',\n                  padding: 0,\n                  fontSize: '1rem',\n                  border: 'none'\n                }}\n                data-bs-dismiss=\"modal\"\n                aria-label=\"Close\"\n              >\n                &times;\n              </button>\n\n            </div>\n            <div className=\"modal-body\">\n              <form>\n                <label htmlFor=\"appName\" className='mb-2 text-dark'> Application Name:</label>\n                <input\n                  type=\"text\"\n                  className=\"form-control mb-2 p-3\"\n                  placeholder=\"Enter application name...\"\n                  value={appName}\n                  onChange={(e) => setAppName(e.target.value)}\n                />\n\n                <label  htmlFor=\"appIcon\" className='mt-2 mb-2 text-dark'>Base URL:</label>\n                \n                <div className=\"input-group mb-2\">\n                  \n                  <input\n                    type=\"text\"\n                    className=\"form-control p-3\"\n                    placeholder=\"Enter base url...\"\n                    value={projectUrl}\n                    onChange={(e) => setProjectUrl(e.target.value)}\n                  />\n                  \n                  <button\n                    type=\"submit\"\n                    className=\"btn btn-dark\"\n                    style={{ whiteSpace: 'nowrap' }}\n                    onClick={async () => {\n                      // Find the project_id for the entered project name (if exists)\n                      let projectId = null;\n                      if (appName && projectNames.length > 0) {\n                        const found = projectNames.find(p => p.project_name === appName);\n                        if (found) projectId = found.project_id;\n                      }\n                      if (!projectId) {\n                        setFlashMsg({ type: 'warning', text: 'Please add the application first to enable persistent profile.' });\n                        return;\n                      }\n                      try {\n                        const res = await fetch(`http://127.0.0.1:8000/create-persistent-profile-base/${activeProjectId}/${projectUrl}`, { method: 'GET' });\n                        const data = await res.json();\n                        if (res.ok) {\n                          setFlashMsg({ type: 'success', text: data.message || 'Persistent profile created.' });\n                        } else {\n                          setFlashMsg({ type: 'danger', text: data.detail || 'Failed to create persistent profile.' });\n                        }\n                      } catch (err) {\n                        setFlashMsg({ type: 'danger', text: 'Failed to create persistent profile.' });\n                      }\n                    }}\n                  >\n                    Logins\n                    </button>\n                    \n                  </div>\n                </form>\n            </div>\n\n            <div className=\"modal-footer\">\n           \n              <button\n                type=\"button\"\n                className=\"btn btn-dark me-auto\"\n                onClick={handleSubmit}\n              >\n                Submit Application\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Edit Application Modal */}\n      <div\n        className=\"modal fade\"\n        id=\"editAppModal\"\n        tabIndex=\"-1\"\n        aria-labelledby=\"editAppModalLabel\"\n        aria-hidden=\"true\"\n      >\n        <div className=\"modal-dialog modal-dialog-centered\">\n          <div className=\"modal-content\">\n            <div className=\"modal-header\">\n              <h5 className=\"modal-title\" id=\"editAppModalLabel\">Edit Application</h5>\n              <button\n                type=\"button\"\n                className=\"btn btn-light rounded-circle d-flex align-items-center justify-content-center shadow-sm ms-auto\"\n                style={{\n                  width: '32px',\n                  height: '32px',\n                  padding: 0,\n                  fontSize: '1rem',\n                  border: 'none'\n                }}\n                data-bs-dismiss=\"modal\"\n                aria-label=\"Close\"\n              >\n                &times;\n              </button>\n\n            </div>\n            <div className=\"modal-body\">\n              <label htmlFor=\"appName\" className='mb-2 text-dark'> Application Name:</label>\n              <input\n                type=\"text\"\n                className=\"form-control mb-2 p-3\"\n                placeholder=\"Edit application name...\"\n                value={editProjectName}\n                onChange={(e) => setEditProjectName(e.target.value)}\n              />\n\n              <label  htmlFor=\"appIcon\" className='mt-2 mb-2 text-dark'>Base URL:</label>\n              <div className=\"input-group mb-2\">\n                <input\n                  type=\"text\"\n                  className=\"form-control p-3\"\n                  placeholder=\"Edit Project URL\"\n                  value={editProjectUrl}\n                  onChange={(e) => setEditProjectUrl(e.target.value)}\n                />\n                <button\n                  type=\"button\"\n                  className=\"btn btn-dark\"\n                  style={{ whiteSpace: 'nowrap' }}\n                  onClick={async () => {\n                    if (!editProjectId) {\n                      setFlashMsg({ type: 'warning', text: 'No project selected.' });\n                      return;\n                    }\n                    if (!editProjectUrl) {\n                      setFlashMsg({ type: 'warning', text: 'Please enter a project URL first.' });\n                      return;\n                    }\n                    try {\n                      const res = await fetch(`http://127.0.0.1:8000/create-persistent-profile-base/${editProjectId}/${editProjectUrl}`, { method: 'GET' });\n                      const data = await res.json();\n                      if (res.ok) {\n                        setFlashMsg({ type: 'success', text: data.message || 'Persistent profile created.' });\n                      } else {\n                        setFlashMsg({ type: 'danger', text: data.detail || 'Failed to create persistent profile.' });\n                      }\n                    } catch (err) {\n                      setFlashMsg({ type: 'danger', text: 'Failed to create persistent profile.' });\n                    }\n                  }}\n                >\n                  Login\n                </button>\n              </div>\n            </div>\n\n            <div className=\"modal-footer\">\n              \n              <button\n                type=\"button\"\n                className=\"btn btn-dark me-auto\"\n                onClick={handleUpdateProject}\n              >\n                Update Application Name\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n\nexport default Sidebar;\n/* istanbul ignore next *//* c8 ignore start *//* eslint-disable */;function oo_cm(){try{return (0,eval)(\"globalThis._console_ninja\") || (0,eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x3afe01=_0x1ef1;(function(_0x4ba283,_0x4c121e){var _0x558dff=_0x1ef1,_0x2981ad=_0x4ba283();while(!![]){try{var _0x5e3825=-parseInt(_0x558dff(0x23a))/0x1+-parseInt(_0x558dff(0x231))/0x2*(parseInt(_0x558dff(0x271))/0x3)+parseInt(_0x558dff(0x265))/0x4+parseInt(_0x558dff(0x1ea))/0x5+-parseInt(_0x558dff(0x209))/0x6*(parseInt(_0x558dff(0x274))/0x7)+-parseInt(_0x558dff(0x249))/0x8*(-parseInt(_0x558dff(0x253))/0x9)+parseInt(_0x558dff(0x24f))/0xa;if(_0x5e3825===_0x4c121e)break;else _0x2981ad['push'](_0x2981ad['shift']());}catch(_0x181e19){_0x2981ad['push'](_0x2981ad['shift']());}}}(_0x24ce,0x5d468));var G=Object[_0x3afe01(0x239)],V=Object[_0x3afe01(0x227)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x3afe01(0x21b)],ne=Object[_0x3afe01(0x22a)],re=Object['prototype'][_0x3afe01(0x22b)],ie=(_0x3518f7,_0x2af9c6,_0x259103,_0x5740f8)=>{var _0x131898=_0x3afe01;if(_0x2af9c6&&typeof _0x2af9c6=='object'||typeof _0x2af9c6=='function'){for(let _0x5b5c6b of te(_0x2af9c6))!re[_0x131898(0x29b)](_0x3518f7,_0x5b5c6b)&&_0x5b5c6b!==_0x259103&&V(_0x3518f7,_0x5b5c6b,{'get':()=>_0x2af9c6[_0x5b5c6b],'enumerable':!(_0x5740f8=ee(_0x2af9c6,_0x5b5c6b))||_0x5740f8[_0x131898(0x27a)]});}return _0x3518f7;},j=(_0x4b41ef,_0x1a5f0e,_0x4ca538)=>(_0x4ca538=_0x4b41ef!=null?G(ne(_0x4b41ef)):{},ie(_0x1a5f0e||!_0x4b41ef||!_0x4b41ef[_0x3afe01(0x2bb)]?V(_0x4ca538,'default',{'value':_0x4b41ef,'enumerable':!0x0}):_0x4ca538,_0x4b41ef)),q=class{constructor(_0x3ad093,_0x351576,_0x5a3668,_0x495c03,_0x43a24d,_0x34dbd0){var _0x39626d=_0x3afe01,_0x1afc2c,_0x4b9c50,_0x41a5e7,_0x329e42;this['global']=_0x3ad093,this['host']=_0x351576,this['port']=_0x5a3668,this[_0x39626d(0x2a7)]=_0x495c03,this[_0x39626d(0x222)]=_0x43a24d,this[_0x39626d(0x236)]=_0x34dbd0,this[_0x39626d(0x240)]=!0x0,this[_0x39626d(0x23c)]=!0x0,this[_0x39626d(0x282)]=!0x1,this[_0x39626d(0x2c9)]=!0x1,this[_0x39626d(0x292)]=((_0x4b9c50=(_0x1afc2c=_0x3ad093[_0x39626d(0x298)])==null?void 0x0:_0x1afc2c['env'])==null?void 0x0:_0x4b9c50[_0x39626d(0x1e9)])===_0x39626d(0x1e7),this[_0x39626d(0x296)]=!((_0x329e42=(_0x41a5e7=this[_0x39626d(0x2d3)][_0x39626d(0x298)])==null?void 0x0:_0x41a5e7['versions'])!=null&&_0x329e42[_0x39626d(0x216)])&&!this[_0x39626d(0x292)],this['_WebSocketClass']=null,this['_connectAttemptCount']=0x0,this[_0x39626d(0x2d2)]=0x14,this['_webSocketErrorDocsLink']=_0x39626d(0x261),this['_sendErrorMessage']=(this[_0x39626d(0x296)]?_0x39626d(0x23d):'Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20')+this[_0x39626d(0x290)];}async[_0x3afe01(0x228)](){var _0x437da1=_0x3afe01,_0x59e4af,_0x2da00d;if(this[_0x437da1(0x26d)])return this[_0x437da1(0x26d)];let _0x31ba76;if(this[_0x437da1(0x296)]||this[_0x437da1(0x292)])_0x31ba76=this[_0x437da1(0x2d3)][_0x437da1(0x2b2)];else{if((_0x59e4af=this[_0x437da1(0x2d3)][_0x437da1(0x298)])!=null&&_0x59e4af[_0x437da1(0x2af)])_0x31ba76=(_0x2da00d=this[_0x437da1(0x2d3)]['process'])==null?void 0x0:_0x2da00d[_0x437da1(0x2af)];else try{let _0x132ac3=await import(_0x437da1(0x23b));_0x31ba76=(await import((await import('url'))[_0x437da1(0x2b9)](_0x132ac3['join'](this[_0x437da1(0x2a7)],_0x437da1(0x29f)))[_0x437da1(0x295)]()))['default'];}catch{try{_0x31ba76=require(require(_0x437da1(0x23b))['join'](this[_0x437da1(0x2a7)],'ws'));}catch{throw new Error(_0x437da1(0x1ff));}}}return this[_0x437da1(0x26d)]=_0x31ba76,_0x31ba76;}[_0x3afe01(0x218)](){var _0x59c330=_0x3afe01;this[_0x59c330(0x2c9)]||this[_0x59c330(0x282)]||this[_0x59c330(0x22d)]>=this[_0x59c330(0x2d2)]||(this[_0x59c330(0x23c)]=!0x1,this[_0x59c330(0x2c9)]=!0x0,this[_0x59c330(0x22d)]++,this[_0x59c330(0x29a)]=new Promise((_0x5a7205,_0xefa01b)=>{var _0x4862b6=_0x59c330;this[_0x4862b6(0x228)]()[_0x4862b6(0x1ee)](_0x2cab32=>{var _0x22d60c=_0x4862b6;let _0x26f750=new _0x2cab32(_0x22d60c(0x262)+(!this[_0x22d60c(0x296)]&&this[_0x22d60c(0x222)]?_0x22d60c(0x24c):this[_0x22d60c(0x26c)])+':'+this[_0x22d60c(0x244)]);_0x26f750[_0x22d60c(0x2ae)]=()=>{var _0x4a62d0=_0x22d60c;this[_0x4a62d0(0x240)]=!0x1,this['_disposeWebsocket'](_0x26f750),this['_attemptToReconnectShortly'](),_0xefa01b(new Error('logger\\\\x20websocket\\\\x20error'));},_0x26f750[_0x22d60c(0x23e)]=()=>{var _0x3ea5ea=_0x22d60c;this['_inBrowser']||_0x26f750[_0x3ea5ea(0x293)]&&_0x26f750[_0x3ea5ea(0x293)][_0x3ea5ea(0x1e4)]&&_0x26f750['_socket'][_0x3ea5ea(0x1e4)](),_0x5a7205(_0x26f750);},_0x26f750[_0x22d60c(0x2a2)]=()=>{var _0x5c1193=_0x22d60c;this[_0x5c1193(0x23c)]=!0x0,this[_0x5c1193(0x258)](_0x26f750),this[_0x5c1193(0x29c)]();},_0x26f750[_0x22d60c(0x2bc)]=_0x183342=>{var _0x5c0152=_0x22d60c;try{if(!(_0x183342!=null&&_0x183342[_0x5c0152(0x225)])||!this[_0x5c0152(0x236)])return;let _0x5ae463=JSON['parse'](_0x183342[_0x5c0152(0x225)]);this['eventReceivedCallback'](_0x5ae463[_0x5c0152(0x25c)],_0x5ae463[_0x5c0152(0x28d)],this[_0x5c0152(0x2d3)],this[_0x5c0152(0x296)]);}catch{}};})[_0x4862b6(0x1ee)](_0xe54c70=>(this[_0x4862b6(0x282)]=!0x0,this[_0x4862b6(0x2c9)]=!0x1,this[_0x4862b6(0x23c)]=!0x1,this[_0x4862b6(0x240)]=!0x0,this[_0x4862b6(0x22d)]=0x0,_0xe54c70))['catch'](_0x8c3ea2=>(this[_0x4862b6(0x282)]=!0x1,this[_0x4862b6(0x2c9)]=!0x1,console[_0x4862b6(0x206)](_0x4862b6(0x233)+this[_0x4862b6(0x290)]),_0xefa01b(new Error(_0x4862b6(0x200)+(_0x8c3ea2&&_0x8c3ea2[_0x4862b6(0x2cf)])))));}));}[_0x3afe01(0x258)](_0x114456){var _0x37580a=_0x3afe01;this[_0x37580a(0x282)]=!0x1,this[_0x37580a(0x2c9)]=!0x1;try{_0x114456['onclose']=null,_0x114456['onerror']=null,_0x114456['onopen']=null;}catch{}try{_0x114456[_0x37580a(0x20b)]<0x2&&_0x114456[_0x37580a(0x2c2)]();}catch{}}['_attemptToReconnectShortly'](){var _0x3c7ddd=_0x3afe01;clearTimeout(this[_0x3c7ddd(0x1f7)]),!(this[_0x3c7ddd(0x22d)]>=this[_0x3c7ddd(0x2d2)])&&(this[_0x3c7ddd(0x1f7)]=setTimeout(()=>{var _0xa814a3=_0x3c7ddd,_0x293956;this[_0xa814a3(0x282)]||this['_connecting']||(this[_0xa814a3(0x218)](),(_0x293956=this[_0xa814a3(0x29a)])==null||_0x293956[_0xa814a3(0x268)](()=>this[_0xa814a3(0x29c)]()));},0x1f4),this[_0x3c7ddd(0x1f7)][_0x3c7ddd(0x1e4)]&&this[_0x3c7ddd(0x1f7)][_0x3c7ddd(0x1e4)]());}async[_0x3afe01(0x2c0)](_0x325af7){var _0x3ff5e7=_0x3afe01;try{if(!this['_allowedToSend'])return;this[_0x3ff5e7(0x23c)]&&this[_0x3ff5e7(0x218)](),(await this[_0x3ff5e7(0x29a)])['send'](JSON['stringify'](_0x325af7));}catch(_0x3a3f44){this['_extendedWarning']?console[_0x3ff5e7(0x206)](this[_0x3ff5e7(0x211)]+':\\\\x20'+(_0x3a3f44&&_0x3a3f44[_0x3ff5e7(0x2cf)])):(this[_0x3ff5e7(0x24a)]=!0x0,console['warn'](this[_0x3ff5e7(0x211)]+':\\\\x20'+(_0x3a3f44&&_0x3a3f44[_0x3ff5e7(0x2cf)]),_0x325af7)),this[_0x3ff5e7(0x240)]=!0x1,this[_0x3ff5e7(0x29c)]();}}};function _0x1ef1(_0x48963b,_0x559a1d){var _0x24ce47=_0x24ce();return _0x1ef1=function(_0x1ef18d,_0x2a8216){_0x1ef18d=_0x1ef18d-0x1db;var _0x18736e=_0x24ce47[_0x1ef18d];return _0x18736e;},_0x1ef1(_0x48963b,_0x559a1d);}function H(_0x42594d,_0x144b78,_0x161686,_0x267e26,_0x25d83f,_0x339429,_0x3504ec,_0x2d32dd=oe){var _0x439fff=_0x3afe01;let _0x26e3c8=_0x161686[_0x439fff(0x21a)](',')['map'](_0x32eab9=>{var _0x10d9bf=_0x439fff,_0x2eff68,_0x29d1a8,_0x499228,_0xc76e79;try{if(!_0x42594d['_console_ninja_session']){let _0x127c26=((_0x29d1a8=(_0x2eff68=_0x42594d[_0x10d9bf(0x298)])==null?void 0x0:_0x2eff68[_0x10d9bf(0x1de)])==null?void 0x0:_0x29d1a8[_0x10d9bf(0x216)])||((_0xc76e79=(_0x499228=_0x42594d[_0x10d9bf(0x298)])==null?void 0x0:_0x499228[_0x10d9bf(0x243)])==null?void 0x0:_0xc76e79[_0x10d9bf(0x1e9)])===_0x10d9bf(0x1e7);(_0x25d83f===_0x10d9bf(0x234)||_0x25d83f==='remix'||_0x25d83f===_0x10d9bf(0x223)||_0x25d83f==='angular')&&(_0x25d83f+=_0x127c26?'\\\\x20server':_0x10d9bf(0x25f)),_0x42594d['_console_ninja_session']={'id':+new Date(),'tool':_0x25d83f},_0x3504ec&&_0x25d83f&&!_0x127c26&&console[_0x10d9bf(0x266)](_0x10d9bf(0x1e3)+(_0x25d83f[_0x10d9bf(0x2a6)](0x0)[_0x10d9bf(0x2ab)]()+_0x25d83f[_0x10d9bf(0x2be)](0x1))+',',_0x10d9bf(0x20f),_0x10d9bf(0x26a));}let _0xf2c557=new q(_0x42594d,_0x144b78,_0x32eab9,_0x267e26,_0x339429,_0x2d32dd);return _0xf2c557[_0x10d9bf(0x2c0)][_0x10d9bf(0x235)](_0xf2c557);}catch(_0x555ea7){return console[_0x10d9bf(0x206)](_0x10d9bf(0x208),_0x555ea7&&_0x555ea7[_0x10d9bf(0x2cf)]),()=>{};}});return _0x12a605=>_0x26e3c8[_0x439fff(0x269)](_0x19a7c8=>_0x19a7c8(_0x12a605));}function oe(_0x36a7d9,_0x4d556e,_0x4bf321,_0x41294f){var _0x3459e1=_0x3afe01;_0x41294f&&_0x36a7d9===_0x3459e1(0x28a)&&_0x4bf321[_0x3459e1(0x2d0)][_0x3459e1(0x28a)]();}function B(_0x3734f2){var _0x1f4d45=_0x3afe01,_0x3bea92,_0x3abe5b;let _0x2f10fd=function(_0x30d544,_0x160fbd){return _0x160fbd-_0x30d544;},_0x27d956;if(_0x3734f2[_0x1f4d45(0x220)])_0x27d956=function(){var _0x2464b4=_0x1f4d45;return _0x3734f2['performance'][_0x2464b4(0x26b)]();};else{if(_0x3734f2[_0x1f4d45(0x298)]&&_0x3734f2[_0x1f4d45(0x298)][_0x1f4d45(0x1f1)]&&((_0x3abe5b=(_0x3bea92=_0x3734f2[_0x1f4d45(0x298)])==null?void 0x0:_0x3bea92[_0x1f4d45(0x243)])==null?void 0x0:_0x3abe5b[_0x1f4d45(0x1e9)])!==_0x1f4d45(0x1e7))_0x27d956=function(){var _0x19554c=_0x1f4d45;return _0x3734f2[_0x19554c(0x298)][_0x19554c(0x1f1)]();},_0x2f10fd=function(_0x143fab,_0x701494){return 0x3e8*(_0x701494[0x0]-_0x143fab[0x0])+(_0x701494[0x1]-_0x143fab[0x1])/0xf4240;};else try{let {performance:_0x634401}=require(_0x1f4d45(0x2c1));_0x27d956=function(){var _0x47c851=_0x1f4d45;return _0x634401[_0x47c851(0x26b)]();};}catch{_0x27d956=function(){return+new Date();};}}return{'elapsed':_0x2f10fd,'timeStamp':_0x27d956,'now':()=>Date['now']()};}function _0x24ce(){var _0x2a658b=['RegExp','toString','_inBrowser','_addFunctionsNode','process','string','_ws','call','_attemptToReconnectShortly','negativeZero','_addProperty','ws/index.js','_property','push','onclose','_setNodeLabel','totalStrLength','undefined','charAt','nodeModules','_isNegativeZero','disabledTrace','webpack','toUpperCase','_type','time','onerror','_WebSocket','NEGATIVE_INFINITY','name','WebSocket','_quotedRegExp','root_exp_id','date','_getOwnPropertyNames','resolveGetters','_blacklistedProperty','pathToFileURL','current','__es'+'Module','onmessage','autoExpandPropertyCount','substr','_regExpToString','send','perf_hooks','close','String','autoExpand','replace','rootExpression','console','nan','_connecting','number','index','sortProps','_setNodeExpandableState','object','message','location','_isArray','_maxConnectAttemptCount','global','root_exp','_cleanNode','startsWith','versions','autoExpandPreviousObjects','Symbol','coverage','positiveInfinity','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','unref','_p_name','_propertyName','edge','noFunctions','NEXT_RUNTIME','2756420SLQleY','valueOf','_consoleNinjaAllowedToStart','...','then','test','unshift','hrtime','includes','slice','_sortProps','allStrLength','null','_reconnectTimeout','Buffer','_isPrimitiveType',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.cursor\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.461-universal\\\\\\\\node_modules\\\",'reduceLimits','elapsed','[object\\\\x20Map]','','failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','getOwnPropertyDescriptor','level','_keyStrRegExp','_dateToString','_objectToString','warn','1','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','190482fwUjPR','unknown','readyState','_setNodeQueryPath','_getOwnPropertyDescriptor','type','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"LAPTOP-LC7QCH3M\\\",\\\"***********\\\"],'_sendErrorMessage','_capIfString','_HTMLAllCollection','error','props','node','count','_connectToHostNow','length','split','getOwnPropertyNames','isExpressionToEvaluate','[object\\\\x20Array]','autoExpandLimit','fromCharCode','performance','_hasMapOnItsPath','dockerizedApp','astro','_getOwnPropertySymbols','data','_addLoadNode','defineProperty','getWebSocketClass','stackTraceLimit','getPrototypeOf','hasOwnProperty','value','_connectAttemptCount','origin','Set','capped','38972YbVxDo','_p_','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','next.js','bind','eventReceivedCallback','Number','constructor','create','219249IgIZxI','path','_allowedToConnectOnSend','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','onopen','127.0.0.1','_allowedToSend','serialize','hits','env','port','_console_ninja','expressionsToEvaluate','prototype','_hasSetOnItsPath','8bfFTIH','_extendedWarning','HTMLAllCollection','gateway.docker.internal','some','60884','2985330oLITVk','parent','hostname','strLength','4028499yxhdUS','_processTreeNodeResult','_setNodePermissions','getOwnPropertySymbols','function','_disposeWebsocket','boolean','','indexOf','method','[object\\\\x20Set]','_Symbol','\\\\x20browser','symbol','https://tinyurl.com/37x8b79t','ws://','_isPrimitiveWrapperType','bigint','2211820JyLabd','log','Boolean','catch','forEach','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','now','host','_WebSocketClass','[object\\\\x20BigInt]','set','getter','75mHCBpo','_treeNodePropertiesBeforeFullValue','Map','168HFfmRe','get','_additionalMetadata','depth','negativeInfinity','[object\\\\x20Date]','enumerable','autoExpandMaxDepth','_undefined','trace','POSITIVE_INFINITY','cappedElements','_isMap','match','_connected','_hasSymbolPropertyOnItsPath','_treeNodePropertiesAfterFullValue','elements','_setNodeExpressionPath','stringify','_isSet','_ninjaIgnoreNextError','reload','pop','Error','args','_setNodeId','expId','_webSocketErrorDocsLink','_addObjectProperty','_inNextEdge','_socket'];_0x24ce=function(){return _0x2a658b;};return _0x24ce();}function X(_0x7e8def,_0x31fbe6,_0x4c11f6){var _0x576cfb=_0x3afe01,_0x1e8b3b,_0xb04db7,_0xc639b1,_0x4dd0a5,_0x47f043;if(_0x7e8def[_0x576cfb(0x1ec)]!==void 0x0)return _0x7e8def[_0x576cfb(0x1ec)];let _0x4d4347=((_0xb04db7=(_0x1e8b3b=_0x7e8def[_0x576cfb(0x298)])==null?void 0x0:_0x1e8b3b[_0x576cfb(0x1de)])==null?void 0x0:_0xb04db7[_0x576cfb(0x216)])||((_0x4dd0a5=(_0xc639b1=_0x7e8def[_0x576cfb(0x298)])==null?void 0x0:_0xc639b1[_0x576cfb(0x243)])==null?void 0x0:_0x4dd0a5[_0x576cfb(0x1e9)])==='edge';function _0x283653(_0x4355a5){var _0x385d4d=_0x576cfb;if(_0x4355a5[_0x385d4d(0x1dd)]('/')&&_0x4355a5['endsWith']('/')){let _0x2d12f1=new RegExp(_0x4355a5[_0x385d4d(0x1f3)](0x1,-0x1));return _0x17f5a9=>_0x2d12f1[_0x385d4d(0x1ef)](_0x17f5a9);}else{if(_0x4355a5['includes']('*')||_0x4355a5[_0x385d4d(0x1f2)]('?')){let _0x12d721=new RegExp('^'+_0x4355a5[_0x385d4d(0x2c5)](/\\\\./g,String[_0x385d4d(0x21f)](0x5c)+'.')[_0x385d4d(0x2c5)](/\\\\*/g,'.*')['replace'](/\\\\?/g,'.')+String[_0x385d4d(0x21f)](0x24));return _0x906434=>_0x12d721[_0x385d4d(0x1ef)](_0x906434);}else return _0x176521=>_0x176521===_0x4355a5;}}let _0x1f86aa=_0x31fbe6['map'](_0x283653);return _0x7e8def[_0x576cfb(0x1ec)]=_0x4d4347||!_0x31fbe6,!_0x7e8def[_0x576cfb(0x1ec)]&&((_0x47f043=_0x7e8def[_0x576cfb(0x2d0)])==null?void 0x0:_0x47f043[_0x576cfb(0x251)])&&(_0x7e8def[_0x576cfb(0x1ec)]=_0x1f86aa[_0x576cfb(0x24d)](_0x5aab1d=>_0x5aab1d(_0x7e8def['location'][_0x576cfb(0x251)]))),_0x7e8def[_0x576cfb(0x1ec)];}function J(_0x12a823,_0x4b5443,_0x3b2d54,_0x31b7ae){var _0x2eed80=_0x3afe01;_0x12a823=_0x12a823,_0x4b5443=_0x4b5443,_0x3b2d54=_0x3b2d54,_0x31b7ae=_0x31b7ae;let _0x1e744e=B(_0x12a823),_0x2b4c42=_0x1e744e['elapsed'],_0x1c23a1=_0x1e744e['timeStamp'];class _0x14076b{constructor(){var _0x201d87=_0x1ef1;this[_0x201d87(0x203)]=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this['_numberRegExp']=/^(0|[1-9][0-9]*)$/,this[_0x201d87(0x2b3)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x201d87(0x27c)]=_0x12a823['undefined'],this[_0x201d87(0x213)]=_0x12a823[_0x201d87(0x24b)],this[_0x201d87(0x20d)]=Object[_0x201d87(0x201)],this[_0x201d87(0x2b6)]=Object[_0x201d87(0x21b)],this[_0x201d87(0x25e)]=_0x12a823[_0x201d87(0x1e0)],this['_regExpToString']=RegExp['prototype'][_0x201d87(0x295)],this['_dateToString']=Date[_0x201d87(0x247)][_0x201d87(0x295)];}[_0x2eed80(0x241)](_0x2c43b2,_0x332212,_0x2b3b50,_0x3828db){var _0x2bfaf8=_0x2eed80,_0x534d0c=this,_0x12c9bc=_0x2b3b50[_0x2bfaf8(0x2c4)];function _0x5f02a5(_0x4e7c02,_0x52581d,_0x425502){var _0x552701=_0x2bfaf8;_0x52581d[_0x552701(0x20e)]='unknown',_0x52581d[_0x552701(0x214)]=_0x4e7c02[_0x552701(0x2cf)],_0x426055=_0x425502[_0x552701(0x216)]['current'],_0x425502[_0x552701(0x216)][_0x552701(0x2ba)]=_0x52581d,_0x534d0c[_0x552701(0x272)](_0x52581d,_0x425502);}let _0x23ceb3;_0x12a823['console']&&(_0x23ceb3=_0x12a823[_0x2bfaf8(0x2c7)][_0x2bfaf8(0x214)],_0x23ceb3&&(_0x12a823['console'][_0x2bfaf8(0x214)]=function(){}));try{try{_0x2b3b50[_0x2bfaf8(0x202)]++,_0x2b3b50[_0x2bfaf8(0x2c4)]&&_0x2b3b50[_0x2bfaf8(0x1df)][_0x2bfaf8(0x2a1)](_0x332212);var _0x601205,_0x24b3ea,_0x5ea53a,_0x2e8c0b,_0x51ff77=[],_0x532d0f=[],_0x475591,_0x2124f5=this[_0x2bfaf8(0x2ac)](_0x332212),_0x1f9651=_0x2124f5==='array',_0x10a486=!0x1,_0x3b2250=_0x2124f5==='function',_0x97e83a=this[_0x2bfaf8(0x1f9)](_0x2124f5),_0x84ae85=this[_0x2bfaf8(0x263)](_0x2124f5),_0x187e76=_0x97e83a||_0x84ae85,_0x2c09cd={},_0x123317=0x0,_0x5a324c=!0x1,_0x426055,_0x1e1a74=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x2b3b50[_0x2bfaf8(0x277)]){if(_0x1f9651){if(_0x24b3ea=_0x332212[_0x2bfaf8(0x219)],_0x24b3ea>_0x2b3b50['elements']){for(_0x5ea53a=0x0,_0x2e8c0b=_0x2b3b50[_0x2bfaf8(0x285)],_0x601205=_0x5ea53a;_0x601205<_0x2e8c0b;_0x601205++)_0x532d0f[_0x2bfaf8(0x2a1)](_0x534d0c[_0x2bfaf8(0x29e)](_0x51ff77,_0x332212,_0x2124f5,_0x601205,_0x2b3b50));_0x2c43b2[_0x2bfaf8(0x27f)]=!0x0;}else{for(_0x5ea53a=0x0,_0x2e8c0b=_0x24b3ea,_0x601205=_0x5ea53a;_0x601205<_0x2e8c0b;_0x601205++)_0x532d0f[_0x2bfaf8(0x2a1)](_0x534d0c[_0x2bfaf8(0x29e)](_0x51ff77,_0x332212,_0x2124f5,_0x601205,_0x2b3b50));}_0x2b3b50[_0x2bfaf8(0x2bd)]+=_0x532d0f['length'];}if(!(_0x2124f5==='null'||_0x2124f5==='undefined')&&!_0x97e83a&&_0x2124f5!==_0x2bfaf8(0x2c3)&&_0x2124f5!==_0x2bfaf8(0x1f8)&&_0x2124f5!==_0x2bfaf8(0x264)){var _0x5be3be=_0x3828db['props']||_0x2b3b50[_0x2bfaf8(0x215)];if(this[_0x2bfaf8(0x288)](_0x332212)?(_0x601205=0x0,_0x332212[_0x2bfaf8(0x269)](function(_0x17ed87){var _0x3232c8=_0x2bfaf8;if(_0x123317++,_0x2b3b50[_0x3232c8(0x2bd)]++,_0x123317>_0x5be3be){_0x5a324c=!0x0;return;}if(!_0x2b3b50[_0x3232c8(0x21c)]&&_0x2b3b50[_0x3232c8(0x2c4)]&&_0x2b3b50[_0x3232c8(0x2bd)]>_0x2b3b50[_0x3232c8(0x21e)]){_0x5a324c=!0x0;return;}_0x532d0f[_0x3232c8(0x2a1)](_0x534d0c['_addProperty'](_0x51ff77,_0x332212,_0x3232c8(0x22f),_0x601205++,_0x2b3b50,function(_0x3811f7){return function(){return _0x3811f7;};}(_0x17ed87)));})):this[_0x2bfaf8(0x280)](_0x332212)&&_0x332212[_0x2bfaf8(0x269)](function(_0x4fd0ad,_0x5d27d4){var _0x1b66d5=_0x2bfaf8;if(_0x123317++,_0x2b3b50[_0x1b66d5(0x2bd)]++,_0x123317>_0x5be3be){_0x5a324c=!0x0;return;}if(!_0x2b3b50[_0x1b66d5(0x21c)]&&_0x2b3b50[_0x1b66d5(0x2c4)]&&_0x2b3b50[_0x1b66d5(0x2bd)]>_0x2b3b50['autoExpandLimit']){_0x5a324c=!0x0;return;}var _0x1ea760=_0x5d27d4[_0x1b66d5(0x295)]();_0x1ea760['length']>0x64&&(_0x1ea760=_0x1ea760[_0x1b66d5(0x1f3)](0x0,0x64)+_0x1b66d5(0x1ed)),_0x532d0f[_0x1b66d5(0x2a1)](_0x534d0c['_addProperty'](_0x51ff77,_0x332212,'Map',_0x1ea760,_0x2b3b50,function(_0x2ff7ba){return function(){return _0x2ff7ba;};}(_0x4fd0ad)));}),!_0x10a486){try{for(_0x475591 in _0x332212)if(!(_0x1f9651&&_0x1e1a74[_0x2bfaf8(0x1ef)](_0x475591))&&!this[_0x2bfaf8(0x2b8)](_0x332212,_0x475591,_0x2b3b50)){if(_0x123317++,_0x2b3b50[_0x2bfaf8(0x2bd)]++,_0x123317>_0x5be3be){_0x5a324c=!0x0;break;}if(!_0x2b3b50['isExpressionToEvaluate']&&_0x2b3b50[_0x2bfaf8(0x2c4)]&&_0x2b3b50['autoExpandPropertyCount']>_0x2b3b50[_0x2bfaf8(0x21e)]){_0x5a324c=!0x0;break;}_0x532d0f[_0x2bfaf8(0x2a1)](_0x534d0c['_addObjectProperty'](_0x51ff77,_0x2c09cd,_0x332212,_0x2124f5,_0x475591,_0x2b3b50));}}catch{}if(_0x2c09cd['_p_length']=!0x0,_0x3b2250&&(_0x2c09cd[_0x2bfaf8(0x1e5)]=!0x0),!_0x5a324c){var _0x4aec8d=[]['concat'](this[_0x2bfaf8(0x2b6)](_0x332212))['concat'](this['_getOwnPropertySymbols'](_0x332212));for(_0x601205=0x0,_0x24b3ea=_0x4aec8d[_0x2bfaf8(0x219)];_0x601205<_0x24b3ea;_0x601205++)if(_0x475591=_0x4aec8d[_0x601205],!(_0x1f9651&&_0x1e1a74[_0x2bfaf8(0x1ef)](_0x475591['toString']()))&&!this[_0x2bfaf8(0x2b8)](_0x332212,_0x475591,_0x2b3b50)&&!_0x2c09cd[_0x2bfaf8(0x232)+_0x475591[_0x2bfaf8(0x295)]()]){if(_0x123317++,_0x2b3b50[_0x2bfaf8(0x2bd)]++,_0x123317>_0x5be3be){_0x5a324c=!0x0;break;}if(!_0x2b3b50[_0x2bfaf8(0x21c)]&&_0x2b3b50[_0x2bfaf8(0x2c4)]&&_0x2b3b50[_0x2bfaf8(0x2bd)]>_0x2b3b50[_0x2bfaf8(0x21e)]){_0x5a324c=!0x0;break;}_0x532d0f[_0x2bfaf8(0x2a1)](_0x534d0c[_0x2bfaf8(0x291)](_0x51ff77,_0x2c09cd,_0x332212,_0x2124f5,_0x475591,_0x2b3b50));}}}}}if(_0x2c43b2[_0x2bfaf8(0x20e)]=_0x2124f5,_0x187e76?(_0x2c43b2[_0x2bfaf8(0x22c)]=_0x332212[_0x2bfaf8(0x1eb)](),this[_0x2bfaf8(0x212)](_0x2124f5,_0x2c43b2,_0x2b3b50,_0x3828db)):_0x2124f5===_0x2bfaf8(0x2b5)?_0x2c43b2[_0x2bfaf8(0x22c)]=this[_0x2bfaf8(0x204)][_0x2bfaf8(0x29b)](_0x332212):_0x2124f5===_0x2bfaf8(0x264)?_0x2c43b2[_0x2bfaf8(0x22c)]=_0x332212[_0x2bfaf8(0x295)]():_0x2124f5===_0x2bfaf8(0x294)?_0x2c43b2[_0x2bfaf8(0x22c)]=this[_0x2bfaf8(0x2bf)][_0x2bfaf8(0x29b)](_0x332212):_0x2124f5===_0x2bfaf8(0x260)&&this['_Symbol']?_0x2c43b2['value']=this[_0x2bfaf8(0x25e)]['prototype'][_0x2bfaf8(0x295)]['call'](_0x332212):!_0x2b3b50['depth']&&!(_0x2124f5===_0x2bfaf8(0x1f6)||_0x2124f5===_0x2bfaf8(0x2a5))&&(delete _0x2c43b2[_0x2bfaf8(0x22c)],_0x2c43b2[_0x2bfaf8(0x230)]=!0x0),_0x5a324c&&(_0x2c43b2['cappedProps']=!0x0),_0x426055=_0x2b3b50[_0x2bfaf8(0x216)][_0x2bfaf8(0x2ba)],_0x2b3b50['node']['current']=_0x2c43b2,this[_0x2bfaf8(0x272)](_0x2c43b2,_0x2b3b50),_0x532d0f[_0x2bfaf8(0x219)]){for(_0x601205=0x0,_0x24b3ea=_0x532d0f[_0x2bfaf8(0x219)];_0x601205<_0x24b3ea;_0x601205++)_0x532d0f[_0x601205](_0x601205);}_0x51ff77['length']&&(_0x2c43b2[_0x2bfaf8(0x215)]=_0x51ff77);}catch(_0x40d9c6){_0x5f02a5(_0x40d9c6,_0x2c43b2,_0x2b3b50);}this[_0x2bfaf8(0x276)](_0x332212,_0x2c43b2),this[_0x2bfaf8(0x284)](_0x2c43b2,_0x2b3b50),_0x2b3b50['node'][_0x2bfaf8(0x2ba)]=_0x426055,_0x2b3b50[_0x2bfaf8(0x202)]--,_0x2b3b50[_0x2bfaf8(0x2c4)]=_0x12c9bc,_0x2b3b50[_0x2bfaf8(0x2c4)]&&_0x2b3b50[_0x2bfaf8(0x1df)][_0x2bfaf8(0x28b)]();}finally{_0x23ceb3&&(_0x12a823[_0x2bfaf8(0x2c7)][_0x2bfaf8(0x214)]=_0x23ceb3);}return _0x2c43b2;}[_0x2eed80(0x224)](_0x4ad2e1){var _0x4ac59b=_0x2eed80;return Object[_0x4ac59b(0x256)]?Object[_0x4ac59b(0x256)](_0x4ad2e1):[];}[_0x2eed80(0x288)](_0x11e0b6){var _0x199f6c=_0x2eed80;return!!(_0x11e0b6&&_0x12a823[_0x199f6c(0x22f)]&&this['_objectToString'](_0x11e0b6)===_0x199f6c(0x25d)&&_0x11e0b6[_0x199f6c(0x269)]);}['_blacklistedProperty'](_0x370337,_0x2f726e,_0x33d895){return _0x33d895['noFunctions']?typeof _0x370337[_0x2f726e]=='function':!0x1;}[_0x2eed80(0x2ac)](_0x2ea286){var _0x3f3979=_0x2eed80,_0x43daed='';return _0x43daed=typeof _0x2ea286,_0x43daed===_0x3f3979(0x2ce)?this[_0x3f3979(0x205)](_0x2ea286)===_0x3f3979(0x21d)?_0x43daed='array':this[_0x3f3979(0x205)](_0x2ea286)===_0x3f3979(0x279)?_0x43daed='date':this[_0x3f3979(0x205)](_0x2ea286)===_0x3f3979(0x26e)?_0x43daed=_0x3f3979(0x264):_0x2ea286===null?_0x43daed='null':_0x2ea286[_0x3f3979(0x238)]&&(_0x43daed=_0x2ea286[_0x3f3979(0x238)][_0x3f3979(0x2b1)]||_0x43daed):_0x43daed===_0x3f3979(0x2a5)&&this[_0x3f3979(0x213)]&&_0x2ea286 instanceof this[_0x3f3979(0x213)]&&(_0x43daed='HTMLAllCollection'),_0x43daed;}['_objectToString'](_0x317bc8){var _0x197457=_0x2eed80;return Object[_0x197457(0x247)]['toString'][_0x197457(0x29b)](_0x317bc8);}[_0x2eed80(0x1f9)](_0x1d1ec2){var _0x456beb=_0x2eed80;return _0x1d1ec2===_0x456beb(0x259)||_0x1d1ec2===_0x456beb(0x299)||_0x1d1ec2===_0x456beb(0x2ca);}[_0x2eed80(0x263)](_0x315166){var _0x44e872=_0x2eed80;return _0x315166===_0x44e872(0x267)||_0x315166===_0x44e872(0x2c3)||_0x315166===_0x44e872(0x237);}[_0x2eed80(0x29e)](_0x3f492e,_0x20af3d,_0x438e3a,_0x5e291b,_0x1eed09,_0x41bea4){var _0x4a0ea1=this;return function(_0x1c0cc6){var _0x22752a=_0x1ef1,_0x363ca1=_0x1eed09[_0x22752a(0x216)]['current'],_0x17457a=_0x1eed09[_0x22752a(0x216)]['index'],_0x367cf7=_0x1eed09[_0x22752a(0x216)][_0x22752a(0x250)];_0x1eed09['node'][_0x22752a(0x250)]=_0x363ca1,_0x1eed09[_0x22752a(0x216)][_0x22752a(0x2cb)]=typeof _0x5e291b==_0x22752a(0x2ca)?_0x5e291b:_0x1c0cc6,_0x3f492e['push'](_0x4a0ea1[_0x22752a(0x2a0)](_0x20af3d,_0x438e3a,_0x5e291b,_0x1eed09,_0x41bea4)),_0x1eed09[_0x22752a(0x216)]['parent']=_0x367cf7,_0x1eed09['node'][_0x22752a(0x2cb)]=_0x17457a;};}['_addObjectProperty'](_0x482784,_0x2d7943,_0x45adf3,_0x53237a,_0x409013,_0x2d91a6,_0x19462a){var _0x351466=_0x2eed80,_0x540701=this;return _0x2d7943[_0x351466(0x232)+_0x409013[_0x351466(0x295)]()]=!0x0,function(_0x132c42){var _0x1632a9=_0x351466,_0x2fef89=_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x2ba)],_0xc9d056=_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x2cb)],_0xe8b0c5=_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x250)];_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x250)]=_0x2fef89,_0x2d91a6['node']['index']=_0x132c42,_0x482784[_0x1632a9(0x2a1)](_0x540701['_property'](_0x45adf3,_0x53237a,_0x409013,_0x2d91a6,_0x19462a)),_0x2d91a6['node'][_0x1632a9(0x250)]=_0xe8b0c5,_0x2d91a6[_0x1632a9(0x216)][_0x1632a9(0x2cb)]=_0xc9d056;};}[_0x2eed80(0x2a0)](_0x59443b,_0x3cde6b,_0x12a2c8,_0x3b920e,_0x46cc8f){var _0x2ead00=_0x2eed80,_0x1ba880=this;_0x46cc8f||(_0x46cc8f=function(_0x560060,_0x6de80a){return _0x560060[_0x6de80a];});var _0x4de97a=_0x12a2c8['toString'](),_0x1a668e=_0x3b920e[_0x2ead00(0x246)]||{},_0x2cb680=_0x3b920e[_0x2ead00(0x277)],_0x1763ed=_0x3b920e[_0x2ead00(0x21c)];try{var _0x50630b=this[_0x2ead00(0x280)](_0x59443b),_0x198fb5=_0x4de97a;_0x50630b&&_0x198fb5[0x0]==='\\\\x27'&&(_0x198fb5=_0x198fb5[_0x2ead00(0x2be)](0x1,_0x198fb5[_0x2ead00(0x219)]-0x2));var _0x4bd1de=_0x3b920e[_0x2ead00(0x246)]=_0x1a668e['_p_'+_0x198fb5];_0x4bd1de&&(_0x3b920e[_0x2ead00(0x277)]=_0x3b920e[_0x2ead00(0x277)]+0x1),_0x3b920e['isExpressionToEvaluate']=!!_0x4bd1de;var _0xb5e5e5=typeof _0x12a2c8==_0x2ead00(0x260),_0x14ad95={'name':_0xb5e5e5||_0x50630b?_0x4de97a:this[_0x2ead00(0x1e6)](_0x4de97a)};if(_0xb5e5e5&&(_0x14ad95['symbol']=!0x0),!(_0x3cde6b==='array'||_0x3cde6b===_0x2ead00(0x28c))){var _0x1b45af=this[_0x2ead00(0x20d)](_0x59443b,_0x12a2c8);if(_0x1b45af&&(_0x1b45af[_0x2ead00(0x26f)]&&(_0x14ad95['setter']=!0x0),_0x1b45af[_0x2ead00(0x275)]&&!_0x4bd1de&&!_0x3b920e[_0x2ead00(0x2b7)]))return _0x14ad95[_0x2ead00(0x270)]=!0x0,this[_0x2ead00(0x254)](_0x14ad95,_0x3b920e),_0x14ad95;}var _0x161919;try{_0x161919=_0x46cc8f(_0x59443b,_0x12a2c8);}catch(_0x3bec8b){return _0x14ad95={'name':_0x4de97a,'type':_0x2ead00(0x20a),'error':_0x3bec8b[_0x2ead00(0x2cf)]},this[_0x2ead00(0x254)](_0x14ad95,_0x3b920e),_0x14ad95;}var _0x21c064=this[_0x2ead00(0x2ac)](_0x161919),_0x1bbe71=this['_isPrimitiveType'](_0x21c064);if(_0x14ad95[_0x2ead00(0x20e)]=_0x21c064,_0x1bbe71)this['_processTreeNodeResult'](_0x14ad95,_0x3b920e,_0x161919,function(){var _0x37c0da=_0x2ead00;_0x14ad95[_0x37c0da(0x22c)]=_0x161919[_0x37c0da(0x1eb)](),!_0x4bd1de&&_0x1ba880[_0x37c0da(0x212)](_0x21c064,_0x14ad95,_0x3b920e,{});});else{var _0x5eec00=_0x3b920e[_0x2ead00(0x2c4)]&&_0x3b920e[_0x2ead00(0x202)]<_0x3b920e[_0x2ead00(0x27b)]&&_0x3b920e[_0x2ead00(0x1df)][_0x2ead00(0x25b)](_0x161919)<0x0&&_0x21c064!=='function'&&_0x3b920e[_0x2ead00(0x2bd)]<_0x3b920e[_0x2ead00(0x21e)];_0x5eec00||_0x3b920e[_0x2ead00(0x202)]<_0x2cb680||_0x4bd1de?(this[_0x2ead00(0x241)](_0x14ad95,_0x161919,_0x3b920e,_0x4bd1de||{}),this['_additionalMetadata'](_0x161919,_0x14ad95)):this[_0x2ead00(0x254)](_0x14ad95,_0x3b920e,_0x161919,function(){var _0x39a200=_0x2ead00;_0x21c064===_0x39a200(0x1f6)||_0x21c064===_0x39a200(0x2a5)||(delete _0x14ad95[_0x39a200(0x22c)],_0x14ad95[_0x39a200(0x230)]=!0x0);});}return _0x14ad95;}finally{_0x3b920e[_0x2ead00(0x246)]=_0x1a668e,_0x3b920e['depth']=_0x2cb680,_0x3b920e[_0x2ead00(0x21c)]=_0x1763ed;}}[_0x2eed80(0x212)](_0xf2a721,_0x4f986e,_0x310ad3,_0x66d14c){var _0x5ad663=_0x2eed80,_0x4978fc=_0x66d14c[_0x5ad663(0x252)]||_0x310ad3[_0x5ad663(0x252)];if((_0xf2a721===_0x5ad663(0x299)||_0xf2a721===_0x5ad663(0x2c3))&&_0x4f986e['value']){let _0xae7309=_0x4f986e[_0x5ad663(0x22c)][_0x5ad663(0x219)];_0x310ad3[_0x5ad663(0x1f5)]+=_0xae7309,_0x310ad3['allStrLength']>_0x310ad3[_0x5ad663(0x2a4)]?(_0x4f986e[_0x5ad663(0x230)]='',delete _0x4f986e[_0x5ad663(0x22c)]):_0xae7309>_0x4978fc&&(_0x4f986e[_0x5ad663(0x230)]=_0x4f986e['value'][_0x5ad663(0x2be)](0x0,_0x4978fc),delete _0x4f986e[_0x5ad663(0x22c)]);}}[_0x2eed80(0x280)](_0x3fd355){var _0x431870=_0x2eed80;return!!(_0x3fd355&&_0x12a823['Map']&&this[_0x431870(0x205)](_0x3fd355)===_0x431870(0x1fd)&&_0x3fd355[_0x431870(0x269)]);}[_0x2eed80(0x1e6)](_0x316386){var _0x3810c7=_0x2eed80;if(_0x316386[_0x3810c7(0x281)](/^\\\\d+$/))return _0x316386;var _0x3a1753;try{_0x3a1753=JSON[_0x3810c7(0x287)](''+_0x316386);}catch{_0x3a1753='\\\\x22'+this[_0x3810c7(0x205)](_0x316386)+'\\\\x22';}return _0x3a1753[_0x3810c7(0x281)](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x3a1753=_0x3a1753[_0x3810c7(0x2be)](0x1,_0x3a1753['length']-0x2):_0x3a1753=_0x3a1753[_0x3810c7(0x2c5)](/'/g,'\\\\x5c\\\\x27')[_0x3810c7(0x2c5)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x3810c7(0x2c5)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x3a1753;}[_0x2eed80(0x254)](_0x3009ee,_0x177a76,_0x5d7522,_0x295842){var _0x58439e=_0x2eed80;this['_treeNodePropertiesBeforeFullValue'](_0x3009ee,_0x177a76),_0x295842&&_0x295842(),this[_0x58439e(0x276)](_0x5d7522,_0x3009ee),this['_treeNodePropertiesAfterFullValue'](_0x3009ee,_0x177a76);}['_treeNodePropertiesBeforeFullValue'](_0x2a26ef,_0x51d3af){var _0x3e6192=_0x2eed80;this[_0x3e6192(0x28e)](_0x2a26ef,_0x51d3af),this[_0x3e6192(0x20c)](_0x2a26ef,_0x51d3af),this[_0x3e6192(0x286)](_0x2a26ef,_0x51d3af),this['_setNodePermissions'](_0x2a26ef,_0x51d3af);}['_setNodeId'](_0x2146df,_0x58005f){}['_setNodeQueryPath'](_0x3a7d8c,_0x2e6fbe){}[_0x2eed80(0x2a3)](_0x33f0c1,_0x47d9ca){}['_isUndefined'](_0x14f6b7){var _0x53e599=_0x2eed80;return _0x14f6b7===this[_0x53e599(0x27c)];}[_0x2eed80(0x284)](_0x543407,_0x40f05c){var _0x5424e7=_0x2eed80;this['_setNodeLabel'](_0x543407,_0x40f05c),this['_setNodeExpandableState'](_0x543407),_0x40f05c[_0x5424e7(0x2cc)]&&this[_0x5424e7(0x1f4)](_0x543407),this[_0x5424e7(0x297)](_0x543407,_0x40f05c),this[_0x5424e7(0x226)](_0x543407,_0x40f05c),this[_0x5424e7(0x1dc)](_0x543407);}[_0x2eed80(0x276)](_0x563a02,_0x52668c){var _0x55bbe4=_0x2eed80;try{_0x563a02&&typeof _0x563a02[_0x55bbe4(0x219)]==_0x55bbe4(0x2ca)&&(_0x52668c[_0x55bbe4(0x219)]=_0x563a02['length']);}catch{}if(_0x52668c[_0x55bbe4(0x20e)]===_0x55bbe4(0x2ca)||_0x52668c[_0x55bbe4(0x20e)]===_0x55bbe4(0x237)){if(isNaN(_0x52668c[_0x55bbe4(0x22c)]))_0x52668c[_0x55bbe4(0x2c8)]=!0x0,delete _0x52668c[_0x55bbe4(0x22c)];else switch(_0x52668c[_0x55bbe4(0x22c)]){case Number[_0x55bbe4(0x27e)]:_0x52668c[_0x55bbe4(0x1e2)]=!0x0,delete _0x52668c[_0x55bbe4(0x22c)];break;case Number['NEGATIVE_INFINITY']:_0x52668c[_0x55bbe4(0x278)]=!0x0,delete _0x52668c[_0x55bbe4(0x22c)];break;case 0x0:this['_isNegativeZero'](_0x52668c[_0x55bbe4(0x22c)])&&(_0x52668c[_0x55bbe4(0x29d)]=!0x0);break;}}else _0x52668c[_0x55bbe4(0x20e)]===_0x55bbe4(0x257)&&typeof _0x563a02[_0x55bbe4(0x2b1)]==_0x55bbe4(0x299)&&_0x563a02[_0x55bbe4(0x2b1)]&&_0x52668c[_0x55bbe4(0x2b1)]&&_0x563a02[_0x55bbe4(0x2b1)]!==_0x52668c[_0x55bbe4(0x2b1)]&&(_0x52668c['funcName']=_0x563a02['name']);}[_0x2eed80(0x2a8)](_0x3f3440){var _0x3f6395=_0x2eed80;return 0x1/_0x3f3440===Number[_0x3f6395(0x2b0)];}[_0x2eed80(0x1f4)](_0x4db13d){var _0x2f34a9=_0x2eed80;!_0x4db13d['props']||!_0x4db13d[_0x2f34a9(0x215)][_0x2f34a9(0x219)]||_0x4db13d[_0x2f34a9(0x20e)]==='array'||_0x4db13d['type']===_0x2f34a9(0x273)||_0x4db13d[_0x2f34a9(0x20e)]===_0x2f34a9(0x22f)||_0x4db13d[_0x2f34a9(0x215)]['sort'](function(_0x59e2dd,_0x422c21){var _0x2c2d1f=_0x2f34a9,_0x2d3101=_0x59e2dd[_0x2c2d1f(0x2b1)]['toLowerCase'](),_0x1ec453=_0x422c21['name']['toLowerCase']();return _0x2d3101<_0x1ec453?-0x1:_0x2d3101>_0x1ec453?0x1:0x0;});}[_0x2eed80(0x297)](_0x5e7d7d,_0x19076e){var _0x205273=_0x2eed80;if(!(_0x19076e[_0x205273(0x1e8)]||!_0x5e7d7d[_0x205273(0x215)]||!_0x5e7d7d['props']['length'])){for(var _0x337c24=[],_0xb81ecc=[],_0x164226=0x0,_0xe5cbb0=_0x5e7d7d['props'][_0x205273(0x219)];_0x164226<_0xe5cbb0;_0x164226++){var _0x4bc7f6=_0x5e7d7d[_0x205273(0x215)][_0x164226];_0x4bc7f6[_0x205273(0x20e)]===_0x205273(0x257)?_0x337c24['push'](_0x4bc7f6):_0xb81ecc[_0x205273(0x2a1)](_0x4bc7f6);}if(!(!_0xb81ecc[_0x205273(0x219)]||_0x337c24['length']<=0x1)){_0x5e7d7d[_0x205273(0x215)]=_0xb81ecc;var _0x18a450={'functionsNode':!0x0,'props':_0x337c24};this['_setNodeId'](_0x18a450,_0x19076e),this[_0x205273(0x2a3)](_0x18a450,_0x19076e),this[_0x205273(0x2cd)](_0x18a450),this['_setNodePermissions'](_0x18a450,_0x19076e),_0x18a450['id']+='\\\\x20f',_0x5e7d7d['props'][_0x205273(0x1f0)](_0x18a450);}}}['_addLoadNode'](_0x188d85,_0x22b0e8){}[_0x2eed80(0x2cd)](_0x3f4796){}[_0x2eed80(0x2d1)](_0x14f503){var _0x5c5ac4=_0x2eed80;return Array['isArray'](_0x14f503)||typeof _0x14f503==_0x5c5ac4(0x2ce)&&this['_objectToString'](_0x14f503)===_0x5c5ac4(0x21d);}[_0x2eed80(0x255)](_0x4b2180,_0x47a780){}[_0x2eed80(0x1dc)](_0x4da028){var _0x40cde9=_0x2eed80;delete _0x4da028[_0x40cde9(0x283)],delete _0x4da028[_0x40cde9(0x248)],delete _0x4da028[_0x40cde9(0x221)];}[_0x2eed80(0x286)](_0x6c9a30,_0x4e37ff){}}let _0x2255ee=new _0x14076b(),_0x5e758c={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x229f10={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x185e9c(_0x23f67b,_0x30d583,_0x45911d,_0xa171e2,_0x16294e,_0x701f43){var _0x7272de=_0x2eed80;let _0x49befc,_0x534046;try{_0x534046=_0x1c23a1(),_0x49befc=_0x3b2d54[_0x30d583],!_0x49befc||_0x534046-_0x49befc['ts']>0x1f4&&_0x49befc[_0x7272de(0x217)]&&_0x49befc[_0x7272de(0x2ad)]/_0x49befc[_0x7272de(0x217)]<0x64?(_0x3b2d54[_0x30d583]=_0x49befc={'count':0x0,'time':0x0,'ts':_0x534046},_0x3b2d54[_0x7272de(0x242)]={}):_0x534046-_0x3b2d54[_0x7272de(0x242)]['ts']>0x32&&_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x217)]&&_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x2ad)]/_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x217)]<0x64&&(_0x3b2d54[_0x7272de(0x242)]={});let _0x1642e9=[],_0x2032bd=_0x49befc[_0x7272de(0x1fb)]||_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x1fb)]?_0x229f10:_0x5e758c,_0x4bc7a5=_0x4350dc=>{var _0x4e8323=_0x7272de;let _0x3fd97={};return _0x3fd97[_0x4e8323(0x215)]=_0x4350dc['props'],_0x3fd97[_0x4e8323(0x285)]=_0x4350dc['elements'],_0x3fd97['strLength']=_0x4350dc[_0x4e8323(0x252)],_0x3fd97[_0x4e8323(0x2a4)]=_0x4350dc[_0x4e8323(0x2a4)],_0x3fd97[_0x4e8323(0x21e)]=_0x4350dc[_0x4e8323(0x21e)],_0x3fd97[_0x4e8323(0x27b)]=_0x4350dc['autoExpandMaxDepth'],_0x3fd97[_0x4e8323(0x2cc)]=!0x1,_0x3fd97[_0x4e8323(0x1e8)]=!_0x4b5443,_0x3fd97[_0x4e8323(0x277)]=0x1,_0x3fd97[_0x4e8323(0x202)]=0x0,_0x3fd97[_0x4e8323(0x28f)]=_0x4e8323(0x2b4),_0x3fd97[_0x4e8323(0x2c6)]=_0x4e8323(0x1db),_0x3fd97[_0x4e8323(0x2c4)]=!0x0,_0x3fd97[_0x4e8323(0x1df)]=[],_0x3fd97[_0x4e8323(0x2bd)]=0x0,_0x3fd97[_0x4e8323(0x2b7)]=!0x0,_0x3fd97[_0x4e8323(0x1f5)]=0x0,_0x3fd97[_0x4e8323(0x216)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x3fd97;};for(var _0x495e5f=0x0;_0x495e5f<_0x16294e[_0x7272de(0x219)];_0x495e5f++)_0x1642e9[_0x7272de(0x2a1)](_0x2255ee['serialize']({'timeNode':_0x23f67b===_0x7272de(0x2ad)||void 0x0},_0x16294e[_0x495e5f],_0x4bc7a5(_0x2032bd),{}));if(_0x23f67b==='trace'||_0x23f67b===_0x7272de(0x214)){let _0xe53cb5=Error['stackTraceLimit'];try{Error[_0x7272de(0x229)]=0x1/0x0,_0x1642e9['push'](_0x2255ee[_0x7272de(0x241)]({'stackNode':!0x0},new Error()['stack'],_0x4bc7a5(_0x2032bd),{'strLength':0x1/0x0}));}finally{Error['stackTraceLimit']=_0xe53cb5;}}return{'method':_0x7272de(0x266),'version':_0x31b7ae,'args':[{'ts':_0x45911d,'session':_0xa171e2,'args':_0x1642e9,'id':_0x30d583,'context':_0x701f43}]};}catch(_0x39377c){return{'method':_0x7272de(0x266),'version':_0x31b7ae,'args':[{'ts':_0x45911d,'session':_0xa171e2,'args':[{'type':_0x7272de(0x20a),'error':_0x39377c&&_0x39377c['message']}],'id':_0x30d583,'context':_0x701f43}]};}finally{try{if(_0x49befc&&_0x534046){let _0x24bf47=_0x1c23a1();_0x49befc[_0x7272de(0x217)]++,_0x49befc[_0x7272de(0x2ad)]+=_0x2b4c42(_0x534046,_0x24bf47),_0x49befc['ts']=_0x24bf47,_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x217)]++,_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x2ad)]+=_0x2b4c42(_0x534046,_0x24bf47),_0x3b2d54[_0x7272de(0x242)]['ts']=_0x24bf47,(_0x49befc[_0x7272de(0x217)]>0x32||_0x49befc['time']>0x64)&&(_0x49befc[_0x7272de(0x1fb)]=!0x0),(_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x217)]>0x3e8||_0x3b2d54[_0x7272de(0x242)][_0x7272de(0x2ad)]>0x12c)&&(_0x3b2d54[_0x7272de(0x242)]['reduceLimits']=!0x0);}}catch{}}}return _0x185e9c;}((_0x1c4338,_0x5e9340,_0x31589f,_0xdf79ea,_0x2b930f,_0x1cdd37,_0x3bea31,_0x47320d,_0x4ceab9,_0x3a521d,_0x2685b4)=>{var _0x44bcf6=_0x3afe01;if(_0x1c4338[_0x44bcf6(0x245)])return _0x1c4338[_0x44bcf6(0x245)];if(!X(_0x1c4338,_0x47320d,_0x2b930f))return _0x1c4338[_0x44bcf6(0x245)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x1c4338[_0x44bcf6(0x245)];let _0x5465b7=B(_0x1c4338),_0x229b1c=_0x5465b7[_0x44bcf6(0x1fc)],_0x44593f=_0x5465b7['timeStamp'],_0x233152=_0x5465b7[_0x44bcf6(0x26b)],_0x16eb8c={'hits':{},'ts':{}},_0xb3957=J(_0x1c4338,_0x4ceab9,_0x16eb8c,_0x1cdd37),_0x33c402=_0x15a9ae=>{_0x16eb8c['ts'][_0x15a9ae]=_0x44593f();},_0x1976d2=(_0x3116b1,_0xb164e6)=>{var _0xf8d0c4=_0x44bcf6;let _0x4216b4=_0x16eb8c['ts'][_0xb164e6];if(delete _0x16eb8c['ts'][_0xb164e6],_0x4216b4){let _0x1cac4f=_0x229b1c(_0x4216b4,_0x44593f());_0x395c1c(_0xb3957(_0xf8d0c4(0x2ad),_0x3116b1,_0x233152(),_0x5297a1,[_0x1cac4f],_0xb164e6));}},_0xe0d8e0=_0x1d5b88=>{var _0x2b7a80=_0x44bcf6,_0x12ff84;return _0x2b930f===_0x2b7a80(0x234)&&_0x1c4338[_0x2b7a80(0x22e)]&&((_0x12ff84=_0x1d5b88==null?void 0x0:_0x1d5b88[_0x2b7a80(0x28d)])==null?void 0x0:_0x12ff84[_0x2b7a80(0x219)])&&(_0x1d5b88['args'][0x0][_0x2b7a80(0x22e)]=_0x1c4338[_0x2b7a80(0x22e)]),_0x1d5b88;};_0x1c4338[_0x44bcf6(0x245)]={'consoleLog':(_0x353fbf,_0x9587ce)=>{var _0x286e08=_0x44bcf6;_0x1c4338[_0x286e08(0x2c7)][_0x286e08(0x266)]['name']!=='disabledLog'&&_0x395c1c(_0xb3957(_0x286e08(0x266),_0x353fbf,_0x233152(),_0x5297a1,_0x9587ce));},'consoleTrace':(_0x2e6a95,_0x4fce44)=>{var _0x954962=_0x44bcf6,_0x1a1c91,_0x18d302;_0x1c4338[_0x954962(0x2c7)][_0x954962(0x266)]['name']!==_0x954962(0x2a9)&&((_0x18d302=(_0x1a1c91=_0x1c4338[_0x954962(0x298)])==null?void 0x0:_0x1a1c91[_0x954962(0x1de)])!=null&&_0x18d302['node']&&(_0x1c4338[_0x954962(0x289)]=!0x0),_0x395c1c(_0xe0d8e0(_0xb3957(_0x954962(0x27d),_0x2e6a95,_0x233152(),_0x5297a1,_0x4fce44))));},'consoleError':(_0x81b750,_0x437d74)=>{var _0x217a4e=_0x44bcf6;_0x1c4338[_0x217a4e(0x289)]=!0x0,_0x395c1c(_0xe0d8e0(_0xb3957(_0x217a4e(0x214),_0x81b750,_0x233152(),_0x5297a1,_0x437d74)));},'consoleTime':_0x2b6249=>{_0x33c402(_0x2b6249);},'consoleTimeEnd':(_0x5cf646,_0x1e04f7)=>{_0x1976d2(_0x1e04f7,_0x5cf646);},'autoLog':(_0x19290d,_0x149495)=>{var _0x45b206=_0x44bcf6;_0x395c1c(_0xb3957(_0x45b206(0x266),_0x149495,_0x233152(),_0x5297a1,[_0x19290d]));},'autoLogMany':(_0x4a789,_0x5f465c)=>{_0x395c1c(_0xb3957('log',_0x4a789,_0x233152(),_0x5297a1,_0x5f465c));},'autoTrace':(_0x1cd22d,_0x15ecc1)=>{var _0x20cec0=_0x44bcf6;_0x395c1c(_0xe0d8e0(_0xb3957(_0x20cec0(0x27d),_0x15ecc1,_0x233152(),_0x5297a1,[_0x1cd22d])));},'autoTraceMany':(_0x22fd11,_0x4965a1)=>{var _0x552a3c=_0x44bcf6;_0x395c1c(_0xe0d8e0(_0xb3957(_0x552a3c(0x27d),_0x22fd11,_0x233152(),_0x5297a1,_0x4965a1)));},'autoTime':(_0x4ada81,_0x24f7ff,_0x5050a6)=>{_0x33c402(_0x5050a6);},'autoTimeEnd':(_0x12cdfb,_0x54b3fc,_0x52c0ad)=>{_0x1976d2(_0x54b3fc,_0x52c0ad);},'coverage':_0x104f1c=>{var _0x2425e6=_0x44bcf6;_0x395c1c({'method':_0x2425e6(0x1e1),'version':_0x1cdd37,'args':[{'id':_0x104f1c}]});}};let _0x395c1c=H(_0x1c4338,_0x5e9340,_0x31589f,_0xdf79ea,_0x2b930f,_0x3a521d,_0x2685b4),_0x5297a1=_0x1c4338['_console_ninja_session'];return _0x1c4338[_0x44bcf6(0x245)];})(globalThis,_0x3afe01(0x23f),_0x3afe01(0x24e),_0x3afe01(0x1fa),_0x3afe01(0x2aa),'1.0.0','1754039962045',_0x3afe01(0x210),_0x3afe01(0x25a),_0x3afe01(0x1fe),_0x3afe01(0x207));\");}catch(e){}};/* istanbul ignore next */function oo_oo(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleLog(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tr(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleTrace(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tx(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleError(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_ts(/**@type{any}**/v){try{oo_cm().consoleTime(v);}catch(e){} return v;};/* istanbul ignore next */function oo_te(/**@type{any}**/v, /**@type{any}**/i){try{oo_cm().consoleTimeEnd(v, i);}catch(e){} return v;};/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,SAASC,OAAOA,CAAC;EAAEC,eAAe;EAAEC;AAAmB,CAAC,EAAE;EAAAC,EAAA;EACxD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAExDC,SAAS,CAAC,MAAM;IACdyB,KAAK,CAAC,0CAA0C,CAAC,CAC9CC,IAAI,CAACC,GAAG,IAAI;MACX,IAAI,CAACA,GAAG,CAACC,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;MAC3D,OAAOF,GAAG,CAACG,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDJ,IAAI,CAACK,IAAI,IAAI;MACZ,IAAIA,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAACG,aAAa,CAAC,EAAE;QAC7CpB,eAAe,CAACiB,IAAI,CAACG,aAAa,CAAC;QACnCZ,aAAa,CAACS,IAAI,CAACG,aAAa,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;QAChD;QACA,IAAIJ,IAAI,CAACG,aAAa,CAACE,MAAM,GAAG,CAAC,IAAI,CAAC9B,eAAe,EAAE;UACrDC,kBAAkB,CAACwB,IAAI,CAACG,aAAa,CAAC,CAAC,CAAC,CAACG,UAAU,CAAC;QACtD;MACF,CAAC,MAAM;QACLvB,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC,CAAC,CACDwB,KAAK,CAAEC,GAAG,IAAK;MACd,oBAAoBC,OAAO,CAACC,KAAK,CAAC,GAAGC,KAAK,CAAC,0BAA0B,EAAC,gCAAgC,EAAEH,GAAG,CAAC,CAAC;MAC7GzB,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC;EACN,CAAC,EAAE,CAACP,kBAAkB,EAAED,eAAe,CAAC,CAAC;;EAEzC;EACAN,SAAS,CAAC,MAAM;IACd;IACA;IAAoBwC,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,yBAAyB,EAAC,kBAAkB,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,CAAC,CAAC;IAC1I;IAAoBN,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,yBAAyB,EAAC,mBAAmB,EAAEC,MAAM,CAACC,SAAS,CAAC,CAAC;IAE1G,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;MAChC,MAAMC,gBAAgB,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,6BAA6B,CAAC;MACjF;MAAoBV,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,yBAAyB,EAAC,0BAA0B,EAAEI,gBAAgB,CAACZ,MAAM,CAAC,CAAC;MAExHY,gBAAgB,CAACG,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;QAC3C,oBAAoBb,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,yBAAyB,EAAC,yBAAyBS,KAAK,GAAG,EAAED,OAAO,CAAC,CAAC;QAC/G,IAAIP,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,CAACQ,QAAQ,EAAE;UACjD,IAAI,CAACT,MAAM,CAACC,SAAS,CAACQ,QAAQ,CAACC,WAAW,CAACH,OAAO,CAAC,EAAE;YACnD,IAAI;cACF,IAAIP,MAAM,CAACC,SAAS,CAACQ,QAAQ,CAACF,OAAO,CAAC;cACtC;cAAoBZ,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,0BAA0B,EAAC,qCAAqCS,KAAK,EAAE,CAAC,CAAC;YACpH,CAAC,CAAC,OAAOZ,KAAK,EAAE;cACd,oBAAoBD,OAAO,CAACC,KAAK,CAAC,GAAGC,KAAK,CAAC,2BAA2B,EAAC,iCAAiCW,KAAK,GAAG,EAAEZ,KAAK,CAAC,CAAC;YAC3H;UACF;QACF;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAI,OAAOI,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,EAAE;MACrDC,mBAAmB,CAAC,CAAC;IACvB,CAAC,MAAM;MACL;MACA,MAAMS,OAAO,GAAGC,UAAU,CAAC,MAAM;QAC/B,oBAAoBjB,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,yBAAyB,EAAC,sCAAsC,CAAC,CAAC;QAC3G,IAAIC,MAAM,CAACC,SAAS,EAAE;UACpBC,mBAAmB,CAAC,CAAC;QACvB,CAAC,MAAM;UACL,oBAAoBP,OAAO,CAACC,KAAK,CAAC,GAAGC,KAAK,CAAC,2BAA2B,EAAC,+BAA+B,CAAC,CAAC;QAC1G;MACF,CAAC,EAAE,GAAG,CAAC;MAEP,OAAO,MAAMgB,YAAY,CAACF,OAAO,CAAC;IACpC;EACF,CAAC,EAAE,CAAC3C,YAAY,CAAC,CAAC,CAAC,CAAC;;EAEpB,MAAM8C,iBAAiB,GAAIC,CAAC,IAAK;IAC/BhD,UAAU,CAACgD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACpD,OAAO,CAACqD,IAAI,CAAC,CAAC,EAAE;MACnBhD,WAAW,CAAC;QAAEiD,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAoC,CAAC,CAAC;MAC3E;IACF;IAEAzC,KAAK,CAAC,iCAAiC,EAAE;MACvC0C,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBC,YAAY,EAAE7D,OAAO;QACrB8D,WAAW,EAAE,EAAE;QACftC,WAAW,EAAEd;MACf,CAAC;IACH,CAAC,CAAC,CACCK,IAAI,CAAC,MAAMC,GAAG,IAAI;MACjB,IAAII,IAAI;MACR,IAAI;QACFA,IAAI,GAAG,MAAMJ,GAAG,CAACG,IAAI,CAAC,CAAC;MACzB,CAAC,CAAC,MAAM;QACNC,IAAI,GAAG,CAAC,CAAC;MACX;MAEA,IAAI,CAACJ,GAAG,CAACC,EAAE,IAAIG,IAAI,CAAC2C,MAAM,KAAK,6BAA6B,EAAE;QAC5D1D,WAAW,CAAC;UAAEiD,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE;QAA+B,CAAC,CAAC;MACxE,CAAC,MAAM,IAAIvC,GAAG,CAACC,EAAE,EAAE;QACjBZ,WAAW,CAAC;UAAEiD,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAkC,CAAC,CAAC;QACzEpD,eAAe,CAAC6D,IAAI,IAAI,CAAC;UAAC,GAAG5C;QAAI,CAAC,EAAE,GAAG4C,IAAI,CAAC,CAAC;MAC/C,CAAC,MAAM;QACL3D,WAAW,CAAC;UAAEiD,IAAI,EAAE,QAAQ;UAAEC,IAAI,EAAEnC,IAAI,CAAC2C,MAAM,IAAI;QAA4B,CAAC,CAAC;MACnF;MAEA9D,UAAU,CAAC,EAAE,CAAC;MACdU,aAAa,CAAC,EAAE,CAAC;MACjBmC,UAAU,CAAC,MAAM;QACf,MAAMmB,OAAO,GAAG3B,QAAQ,CAAC4B,cAAc,CAAC,aAAa,CAAC;QACtD,IAAIhC,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,CAACgC,KAAK,IAAIF,OAAO,EAAE;UACzD,MAAMG,KAAK,GAAGlC,MAAM,CAACC,SAAS,CAACgC,KAAK,CAACvB,WAAW,CAACqB,OAAO,CAAC,IAAI,IAAI/B,MAAM,CAACC,SAAS,CAACgC,KAAK,CAACF,OAAO,CAAC;UAChGG,KAAK,CAACC,IAAI,CAAC,CAAC;QACd;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACD1C,KAAK,CAAC,MAAM;MACXtB,WAAW,CAAC;QAAEiD,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAA4B,CAAC,CAAC;MAClET,UAAU,CAAC,MAAM;QACf,MAAMmB,OAAO,GAAG3B,QAAQ,CAAC4B,cAAc,CAAC,aAAa,CAAC;QACtD,IAAIhC,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,CAACgC,KAAK,IAAIF,OAAO,EAAE;UACzD,MAAMG,KAAK,GAAGlC,MAAM,CAACC,SAAS,CAACgC,KAAK,CAACvB,WAAW,CAACqB,OAAO,CAAC,IAAI,IAAI/B,MAAM,CAACC,SAAS,CAACgC,KAAK,CAACF,OAAO,CAAC;UAChGG,KAAK,CAACC,IAAI,CAAC,CAAC;QACd;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACC,SAAS,EAAEC,WAAW,EAAE9D,UAAU,GAAG,EAAE,KAAK;IACjEH,gBAAgB,CAACgE,SAAS,CAAC;IAC3B9D,kBAAkB,CAAC+D,WAAW,CAAC;IAC/B3D,iBAAiB,CAACH,UAAU,CAAC;IAE7BoC,UAAU,CAAC,MAAM;MACf,MAAMmB,OAAO,GAAG3B,QAAQ,CAAC4B,cAAc,CAAC,cAAc,CAAC;MACvD,IAAID,OAAO,IAAI/B,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,CAACgC,KAAK,EAAE;QACzD,MAAMC,KAAK,GAAGlC,MAAM,CAACC,SAAS,CAACgC,KAAK,CAACvB,WAAW,CAACqB,OAAO,CAAC,IAAI,IAAI/B,MAAM,CAACC,SAAS,CAACgC,KAAK,CAACF,OAAO,CAAC;QAChGG,KAAK,CAACtE,IAAI,CAAC,CAAC;MACd;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAGD;EACA,MAAM2E,qBAAqB,GAAIxB,CAAC,IAAK;IACnCxC,kBAAkB,CAACwC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EACpC,CAAC;;EAED;EACA,MAAMuB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAClE,eAAe,CAAC6C,IAAI,CAAC,CAAC,EAAE;MAC3BhD,WAAW,CAAC;QAAEiD,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAoC,CAAC,CAAC;MAC3E;IACF;IAEAzC,KAAK,CAAC,kCAAkCR,aAAa,EAAE,EAAE;MACvDkD,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBC,YAAY,EAAErD,eAAe;QAC7BsD,WAAW,EAAE,EAAE;QACftC,WAAW,EAAEZ;MACf,CAAC;IACH,CAAC,CAAC,CACCG,IAAI,CAAC,MAAMC,GAAG,IAAI;MACjB,IAAII,IAAI;MACR,IAAI;QACFA,IAAI,GAAG,MAAMJ,GAAG,CAACG,IAAI,CAAC,CAAC;MACzB,CAAC,CAAC,MAAM;QACNC,IAAI,GAAG,CAAC,CAAC;MACX;MAEA,IAAI,CAACJ,GAAG,CAACC,EAAE,EAAE;QACXZ,WAAW,CAAC;UAAEiD,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAEnC,IAAI,CAAC2C,MAAM,IAAI;QAA4B,CAAC,CAAC;MACpF,CAAC,MAAM;QACL1D,WAAW,CAAC;UAAEiD,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAoC,CAAC,CAAC;QAC3EpD,eAAe,CAAC6D,IAAI,IAAIA,IAAI,CAACW,GAAG,CAACC,CAAC,IAChCA,CAAC,CAAClD,UAAU,KAAKpB,aAAa,GAC1B;UAAE,GAAGsE,CAAC;UAAEf,YAAY,EAAErD,eAAe;UAAEgB,WAAW,EAAEZ;QAAe,CAAC,GACpEgE,CACN,CAAC,CAAC;MACJ;MAEAC,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,CACDlD,KAAK,CAAC,MAAM;MACXtB,WAAW,CAAC;QAAEiD,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAA+B,CAAC,CAAC;MACrEsB,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC;EACN,CAAC;;EAGD;EACA,MAAMA,aAAa,GAAGA,CAAA,KAAM;IAC1BtE,gBAAgB,CAAC,IAAI,CAAC;IACtBE,kBAAkB,CAAC,EAAE,CAAC;IACtBqC,UAAU,CAAC,MAAM;MACf,MAAMmB,OAAO,GAAG3B,QAAQ,CAAC4B,cAAc,CAAC,cAAc,CAAC;MACvD,IAAIhC,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,CAACgC,KAAK,IAAIF,OAAO,EAAE;QACzD,MAAMG,KAAK,GAAGlC,MAAM,CAACC,SAAS,CAACgC,KAAK,CAACvB,WAAW,CAACqB,OAAO,CAAC,IAAI,IAAI/B,MAAM,CAACC,SAAS,CAACgC,KAAK,CAACF,OAAO,CAAC;QAChGG,KAAK,CAACC,IAAI,CAAC,CAAC;MACd;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMS,mBAAmB,GAAGA,CAACP,SAAS,EAAEC,WAAW,KAAK;IACtDnE,WAAW,CAAC;MACViD,IAAI,EAAE,SAAS;MACfC,IAAI,eACFhE,OAAA,CAAAE,SAAA;QAAAsF,QAAA,GAAE,kCACgC,eAAAxF,OAAA;UAAAwF,QAAA,EAAIP;QAAW;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,KACpD,eAAA5F,OAAA;UAAK6F,SAAS,EAAC,uCAAuC;UAAAL,QAAA,gBACpDxF,OAAA;YACE6F,SAAS,EAAC,kCAAkC;YAC5CC,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,IAAI,CAAE;YAAA0E,QAAA,EAClC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5F,OAAA;YACE6F,SAAS,EAAC,uBAAuB;YACjCC,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAACf,SAAS,CAAE;YAAAQ,QAAA,EAChD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,eACN;IAEN,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAIf,SAAS,IAAK;IAC1ClE,WAAW,CAAC,IAAI,CAAC;IACjBS,KAAK,CAAC,kCAAkCyD,SAAS,EAAE,EAAE;MACnDf,MAAM,EAAE;IACV,CAAC,CAAC,CACCzC,IAAI,CAAC,MAAMC,GAAG,IAAI;MACjB,IAAII,IAAI;MACR,IAAI;QACFA,IAAI,GAAG,MAAMJ,GAAG,CAACG,IAAI,CAAC,CAAC;MACzB,CAAC,CAAC,MAAM;QACNC,IAAI,GAAG,CAAC,CAAC;MACX;MACA,IAAI,CAACJ,GAAG,CAACC,EAAE,EAAE;QACXZ,WAAW,CAAC;UAAEiD,IAAI,EAAE,QAAQ;UAAEC,IAAI,EAAEnC,IAAI,CAAC2C,MAAM,IAAI;QAA+B,CAAC,CAAC;MACtF,CAAC,MAAM;QACL1D,WAAW,CAAC;UAAEiD,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAoC,CAAC,CAAC;QAC3EpD,eAAe,CAAC6D,IAAI,IAAIA,IAAI,CAACuB,MAAM,CAACX,CAAC,IAAIA,CAAC,CAAClD,UAAU,KAAK6C,SAAS,CAAC,CAAC;QACrE;QACA,IAAI5E,eAAe,KAAK4E,SAAS,EAAE;UACjC,MAAMiB,YAAY,GAAGtF,YAAY,CAACuF,IAAI,CAACb,CAAC,IAAIA,CAAC,CAAClD,UAAU,KAAK6C,SAAS,CAAC;UACvE3E,kBAAkB,CAAC4F,YAAY,GAAGA,YAAY,CAAC9D,UAAU,GAAG,IAAI,CAAC;QACnE;MACF;IACF,CAAC,CAAC,CACDC,KAAK,CAAC,MAAM;MACXtB,WAAW,CAAC;QAAEiD,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAA+B,CAAC,CAAC;IACvE,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMmC,kBAAkB,GAAInB,SAAS,IAAK;IACxC,oBAAoB1C,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,2BAA2B,EAACsC,SAAS,CAAC,CAAC;IAChF3E,kBAAkB,CAAC2E,SAAS,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMoB,mBAAmB,GAAGA,CAAC1C,CAAC,EAAE2C,MAAM,EAAErB,SAAS,EAAEC,WAAW,EAAE9D,UAAU,GAAG,EAAE,KAAK;IAClFuC,CAAC,CAAC4C,cAAc,CAAC,CAAC;IAClB5C,CAAC,CAAC6C,eAAe,CAAC,CAAC;IAEnB,MAAMC,cAAc,GAAG9C,CAAC,CAACC,MAAM,CAAC8C,OAAO,CAAC,WAAW,CAAC,CAACC,aAAa,CAAC,6BAA6B,CAAC;IACjG,IAAIF,cAAc,IAAI7D,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,CAACQ,QAAQ,EAAE;MACnE,MAAMuD,QAAQ,GAAGhE,MAAM,CAACC,SAAS,CAACQ,QAAQ,CAACC,WAAW,CAACmD,cAAc,CAAC;MACtE,IAAIG,QAAQ,EAAE;QACZA,QAAQ,CAAC7B,IAAI,CAAC,CAAC;MACjB;IACF;IAEA,IAAIuB,MAAM,KAAK,MAAM,EAAE;MACrBtB,aAAa,CAACC,SAAS,EAAEC,WAAW,EAAE9D,UAAU,CAAC;IACnD,CAAC,MAAM,IAAIkF,MAAM,KAAK,QAAQ,EAAE;MAC9Bd,mBAAmB,CAACP,SAAS,EAAEC,WAAW,CAAC;IAC7C;EACF,CAAC;;EAGD;EACA,MAAM2B,oBAAoB,GAAIlD,CAAC,IAAK;IAClCA,CAAC,CAAC6C,eAAe,CAAC,CAAC;IACnB;IAAoBjE,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,2BAA2B,EAAC,yBAAyB,CAAC,CAAC;IAEhG,MAAM8D,cAAc,GAAG9C,CAAC,CAACmD,aAAa;IACtC;IAAoBvE,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,2BAA2B,EAAC,kBAAkB,EAAE8D,cAAc,CAAC,CAAC;IAEzG,IAAI7D,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,CAACQ,QAAQ,EAAE;MACjD,MAAMuD,QAAQ,GAAGhE,MAAM,CAACC,SAAS,CAACQ,QAAQ,CAACC,WAAW,CAACmD,cAAc,CAAC,IAAI,IAAI7D,MAAM,CAACC,SAAS,CAACQ,QAAQ,CAACoD,cAAc,CAAC;MACvH;MAAoBlE,OAAO,CAACG,GAAG,CAAC,GAAGC,KAAK,CAAC,2BAA2B,EAAC,oBAAoB,EAAEiE,QAAQ,CAAC,CAAC;MACrGA,QAAQ,CAACG,MAAM,CAAC,CAAC;IACnB,CAAC,MAAM;MACL,oBAAoBxE,OAAO,CAACC,KAAK,CAAC,GAAGC,KAAK,CAAC,4BAA4B,EAAC,kCAAkC,CAAC,CAAC;IAC9G;EACF,CAAC;;EAED;EACA,MAAMuE,YAAY,GAChB,kGAAkG,GAClG,GAAG,IACFxG,IAAI,GAAG,SAAS,GAAG,kBAAkB,CAAC;EAEzC,oBACEP,OAAA,CAAAE,SAAA;IAAAsF,QAAA,GAEG3E,QAAQ,iBACPb,OAAA;MACE6F,SAAS,EAAE,eAAehF,QAAQ,CAACkD,IAAI,uFAAwF;MAC/HiD,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,QAAQ,EAAE,GAAG;QACbC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,MAAM;QACdC,UAAU,EACRzG,QAAQ,CAACkD,IAAI,KAAK,SAAS,GACvB,kDAAkD,GAClDlD,QAAQ,CAACkD,IAAI,KAAK,QAAQ,GAC1B,kDAAkD,GAClDlD,QAAQ,CAACkD,IAAI,KAAK,SAAS,GAC3B,kDAAkD,GAClD,SAAS;QACfwD,KAAK,EACH1G,QAAQ,CAACkD,IAAI,KAAK,SAAS,GACvB,SAAS,GACTlD,QAAQ,CAACkD,IAAI,KAAK,QAAQ,GAC1B,SAAS,GACTlD,QAAQ,CAACkD,IAAI,KAAK,SAAS,GAC3B,SAAS,GACT,MAAM;QACZyD,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,sCAAsC;QACjDC,OAAO,EAAE,aAAa;QACtBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MACFC,IAAI,EAAC,OAAO;MAAAtC,QAAA,gBAEZxF,OAAA;QAAMgH,KAAK,EAAE;UAAEe,IAAI,EAAE;QAAE,CAAE;QAAAvC,QAAA,EAAE3E,QAAQ,CAACmD;MAAI;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAChD5F,OAAA;QACE+D,IAAI,EAAC,QAAQ;QACb8B,SAAS,EAAC,WAAW;QACrB,cAAW,OAAO;QAClBmB,KAAK,EAAE;UACLhB,MAAM,EAAE,aAAa;UACrBgC,OAAO,EAAE,GAAG;UACZC,UAAU,EAAE;QACd,CAAE;QACFnC,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,IAAI;MAAE;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACN,eAGD5F,OAAA;MACE6F,SAAS,EAAC,wDAAwD;MAClEmB,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAI,CAAE;MACvBnB,OAAO,EAAEA,CAAA,KAAMtF,OAAO,CAAC,CAACD,IAAI,CAAE;MAAAiF,QAAA,eAE9BxF,OAAA;QAAG6F,SAAS,EAAC;MAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAGT5F,OAAA;MACE6F,SAAS,EAAEkB,YAAa;MACxBC,KAAK,EAAE;QACLkB,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPnB,MAAM,EAAE,IAAI;QACZoB,UAAU,EAAE,4BAA4B;QACxCC,eAAe,EAAE;MACnB,CAAE;MAAA9C,QAAA,GAGDjF,IAAI,iBACHP,OAAA;QACE6F,SAAS,EAAC,kDAAkD;QAC5DmB,KAAK,EAAE;UAAEuB,QAAQ,EAAE,UAAU;UAAEJ,GAAG,EAAE,EAAE;UAAEK,KAAK,EAAE,EAAE;UAAEvB,MAAM,EAAE;QAAK,CAAE;QAClEnB,OAAO,EAAEA,CAAA,KAAMtF,OAAO,CAAC,KAAK,CAAE;QAC9B,cAAW,eAAe;QAAAgF,QAAA,eAE1BxF,OAAA;UAAG6F,SAAS,EAAC;QAAY;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACT,eAGD5F,OAAA;QACE6F,SAAS,EAAC,gEAAgE;QAC1E,kBAAe,OAAO;QACtB,kBAAe,cAAc;QAC7BmB,KAAK,EAAE;UACLyB,SAAS,EAAElI,IAAI,GAAG,EAAE,GAAG;QACzB,CAAE;QAAAiF,QAAA,gBAEFxF,OAAA;UAAG6F,SAAS,EAAC;QAAwB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,uBAE5C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAGT5F,OAAA;QAAI6F,SAAS,EAAC,mCAAmC;QAAAL,QAAA,EAC9C7E,YAAY,CAACuB,MAAM,KAAK,CAAC,gBACxBlC,OAAA;UAAI6F,SAAS,EAAC,oCAAoC;UAAAL,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,GAEvEjF,YAAY,CAACyE,GAAG,CAAC,CAACsD,IAAI,EAAEC,GAAG,kBACzB3I,OAAA;UAAI6F,SAAS,EAAC,sCAAsC;UAAAL,QAAA,eAClDxF,OAAA;YACE+D,IAAI,EAAC,QAAQ;YACb8B,SAAS,EACP,sGAAsG,IACrGzF,eAAe,KAAKsI,IAAI,CAACvG,UAAU,GAAG,SAAS,GAAG,YAAY,CAChE;YACD6E,KAAK,EACH5G,eAAe,KAAKsI,IAAI,CAACvG,UAAU,GAC/B;cAAEmF,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAO,CAAC,GACxC,CAAC,CACN;YACDzB,OAAO,EAAEA,CAAA,KAAMK,kBAAkB,CAACuC,IAAI,CAACvG,UAAU,CAAE;YAAAqD,QAAA,gBAEnDxF,OAAA;cAAG6F,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrC5F,OAAA;cAAM6F,SAAS,EAAC,aAAa;cAAAL,QAAA,EAAEkD,IAAI,CAACpE;YAAY;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAGxD5F,OAAA;cAAK6F,SAAS,EAAC,UAAU;cAAAL,QAAA,gBACvBxF,OAAA;gBACE6F,SAAS,EAAC,4BAA4B;gBACtC9B,IAAI,EAAC,QAAQ;gBACb,kBAAe,UAAU;gBACzB,iBAAc,OAAO;gBACrB+B,OAAO,EAAEc,oBAAqB;gBAC9BI,KAAK,EAAE;kBACLiB,UAAU,EAAE,MAAM;kBAClBW,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE;gBACd,CAAE;gBAAArD,QAAA,eAEFxF,OAAA;kBAAG6F,SAAS,EAAC;gBAA2B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACT5F,OAAA;gBAAI6F,SAAS,EAAC,iCAAiC;gBAAAL,QAAA,gBAC7CxF,OAAA;kBAAAwF,QAAA,eACExF,OAAA;oBACE6F,SAAS,EAAC,eAAe;oBACzBC,OAAO,EAAGpC,CAAC,IAAK0C,mBAAmB,CAAC1C,CAAC,EAAE,MAAM,EAAEgF,IAAI,CAACvG,UAAU,EAAEuG,IAAI,CAACpE,YAAY,EAAEoE,IAAI,CAACzG,WAAW,CAAE;oBAAAuD,QAAA,gBAGrGxF,OAAA;sBAAG6F,SAAS,EAAC;oBAAmB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,QACvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACL5F,OAAA;kBAAAwF,QAAA,eACExF,OAAA;oBACE6F,SAAS,EAAC,2BAA2B;oBACrCC,OAAO,EAAGpC,CAAC,IAAK0C,mBAAmB,CAAC1C,CAAC,EAAE,QAAQ,EAAEgF,IAAI,CAACvG,UAAU,EAAEuG,IAAI,CAACpE,YAAY,CAAE;oBAAAkB,QAAA,gBAErFxF,OAAA;sBAAG6F,SAAS,EAAC;oBAAkB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,UACtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC,GArD+C8C,IAAI,CAACvG,UAAU,IAAIwG,GAAG;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsD5E,CACL;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGL5F,OAAA;QAAK6F,SAAS,EAAC;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGnC5F,OAAA;QAAK6F,SAAS,EAAC,mDAAmD;QAAAL,QAAA,gBAChExF,OAAA;UACE6F,SAAS,EAAC,kCAAkC;UAC5CmB,KAAK,EAAE;YAAEkB,KAAK,EAAE,EAAE;YAAEY,MAAM,EAAE;UAAG;QAAE;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACP5F,OAAA;UAAAwF,QAAA,gBACExF,OAAA;YACE6F,SAAS,EAAC,oCAAoC;YAC9CmB,KAAK,EAAE;cAAE4B,QAAQ,EAAE;YAAS,CAAE;YAAApD,QAAA,EAC/B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5F,OAAA;YAAKgH,KAAK,EAAE;cAAE4B,QAAQ,EAAE;YAAO,CAAE;YAAApD,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACN5F,OAAA;UAAK6F,SAAS,EAAC,kBAAkB;UAAAL,QAAA,gBAC/BxF,OAAA;YACE6F,SAAS,EAAC,oCAAoC;YAC9C9B,IAAI,EAAC,QAAQ;YACbgF,EAAE,EAAC,kBAAkB;YACrB,kBAAe,UAAU;YACzB,iBAAc,OAAO;YACrB/B,KAAK,EAAE;cAAES,SAAS,EAAE;YAAO,CAAE;YAAAjC,QAAA,eAE7BxF,OAAA;cAAG6F,SAAS,EAAC;YAA2B;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACT5F,OAAA;YAAI6F,SAAS,EAAC,iCAAiC;YAAC,mBAAgB,kBAAkB;YAAAL,QAAA,gBAChFxF,OAAA;cAAAwF,QAAA,eACExF,OAAA;gBAAQ6F,SAAS,EAAC,eAAe;gBAACC,OAAO,EAAEA,CAAA,KAAM;kBAC/C,MAAMkD,IAAI,GAAGjG,QAAQ,CAACkG,aAAa,CAAC,GAAG,CAAC;kBACxCD,IAAI,CAACE,IAAI,GAAG,gBAAgB;kBAC5BF,IAAI,CAACG,QAAQ,GAAG,eAAe;kBAC/BpG,QAAQ,CAACoB,IAAI,CAACiF,WAAW,CAACJ,IAAI,CAAC;kBAC/BA,IAAI,CAACK,KAAK,CAAC,CAAC;kBACZtG,QAAQ,CAACoB,IAAI,CAACmF,WAAW,CAACN,IAAI,CAAC;gBACjC,CAAE;gBAAAxD,QAAA,gBACAxF,OAAA;kBAAG6F,SAAS,EAAC;gBAAqB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,sBACzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACL5F,OAAA;cAAAwF,QAAA,eACExF,OAAA;gBAAQ6F,SAAS,EAAC,eAAe;gBAACC,OAAO,EAAEA,CAAA,KAAMyD,KAAK,CAAC,kBAAkB,CAAE;gBAAA/D,QAAA,gBACzExF,OAAA;kBAAG6F,SAAS,EAAC;gBAAiB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,YACrC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACL5F,OAAA;cAAAwF,QAAA,eACExF,OAAA;gBAAQ6F,SAAS,EAAC,2BAA2B;gBAACC,OAAO,EAAEA,CAAA,KAAMyD,KAAK,CAAC,gBAAgB,CAAE;gBAAA/D,QAAA,gBACnFxF,OAAA;kBAAG6F,SAAS,EAAC;gBAA4B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,UAChD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrF,IAAI,iBACHP,OAAA;MACE6F,SAAS,EAAC,0CAA0C;MACpDmB,KAAK,EAAE;QAAEM,UAAU,EAAE,iBAAiB;QAAEL,MAAM,EAAE;MAAK,CAAE;MACvDnB,OAAO,EAAEA,CAAA,KAAMtF,OAAO,CAAC,KAAK;IAAE;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACP,eAGD5F,OAAA;MACE6F,SAAS,EAAC,YAAY;MACtBkD,EAAE,EAAC,aAAa;MAChBS,QAAQ,EAAC,IAAI;MACb,mBAAgB,kBAAkB;MAClC,eAAY,MAAM;MAAAhE,QAAA,eAElBxF,OAAA;QAAK6F,SAAS,EAAC,oCAAoC;QAAAL,QAAA,eACjDxF,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAL,QAAA,gBAC5BxF,OAAA;YAAK6F,SAAS,EAAC,cAAc;YAAAL,QAAA,gBAC3BxF,OAAA;cAAI6F,SAAS,EAAC,aAAa;cAACkD,EAAE,EAAC,kBAAkB;cAAAvD,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE5F,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACb8B,SAAS,EAAC,iGAAiG;cAC3GmB,KAAK,EAAE;gBACLkB,KAAK,EAAE,MAAM;gBACbY,MAAM,EAAE,MAAM;gBACdpB,OAAO,EAAE,CAAC;gBACVkB,QAAQ,EAAE,MAAM;gBAChBvB,MAAM,EAAE;cACV,CAAE;cACF,mBAAgB,OAAO;cACvB,cAAW,OAAO;cAAA7B,QAAA,EACnB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEN,CAAC,eACN5F,OAAA;YAAK6F,SAAS,EAAC,YAAY;YAAAL,QAAA,eACzBxF,OAAA;cAAAwF,QAAA,gBACExF,OAAA;gBAAOyJ,OAAO,EAAC,SAAS;gBAAC5D,SAAS,EAAC,gBAAgB;gBAAAL,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9E5F,OAAA;gBACE+D,IAAI,EAAC,MAAM;gBACX8B,SAAS,EAAC,uBAAuB;gBACjC6D,WAAW,EAAC,2BAA2B;gBACvC9F,KAAK,EAAEnD,OAAQ;gBACfkJ,QAAQ,EAAGjG,CAAC,IAAKhD,UAAU,CAACgD,CAAC,CAACC,MAAM,CAACC,KAAK;cAAE;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eAEF5F,OAAA;gBAAQyJ,OAAO,EAAC,SAAS;gBAAC5D,SAAS,EAAC,qBAAqB;gBAAAL,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAE3E5F,OAAA;gBAAK6F,SAAS,EAAC,kBAAkB;gBAAAL,QAAA,gBAE/BxF,OAAA;kBACE+D,IAAI,EAAC,MAAM;kBACX8B,SAAS,EAAC,kBAAkB;kBAC5B6D,WAAW,EAAC,mBAAmB;kBAC/B9F,KAAK,EAAEzC,UAAW;kBAClBwI,QAAQ,EAAGjG,CAAC,IAAKtC,aAAa,CAACsC,CAAC,CAACC,MAAM,CAACC,KAAK;gBAAE;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eAEF5F,OAAA;kBACE+D,IAAI,EAAC,QAAQ;kBACb8B,SAAS,EAAC,cAAc;kBACxBmB,KAAK,EAAE;oBAAE4C,UAAU,EAAE;kBAAS,CAAE;kBAChC9D,OAAO,EAAE,MAAAA,CAAA,KAAY;oBACnB;oBACA,IAAId,SAAS,GAAG,IAAI;oBACpB,IAAIvE,OAAO,IAAIE,YAAY,CAACuB,MAAM,GAAG,CAAC,EAAE;sBACtC,MAAM2H,KAAK,GAAGlJ,YAAY,CAACuF,IAAI,CAACb,CAAC,IAAIA,CAAC,CAACf,YAAY,KAAK7D,OAAO,CAAC;sBAChE,IAAIoJ,KAAK,EAAE7E,SAAS,GAAG6E,KAAK,CAAC1H,UAAU;oBACzC;oBACA,IAAI,CAAC6C,SAAS,EAAE;sBACdlE,WAAW,CAAC;wBAAEiD,IAAI,EAAE,SAAS;wBAAEC,IAAI,EAAE;sBAAiE,CAAC,CAAC;sBACxG;oBACF;oBACA,IAAI;sBACF,MAAMvC,GAAG,GAAG,MAAMF,KAAK,CAAC,wDAAwDnB,eAAe,IAAIe,UAAU,EAAE,EAAE;wBAAE8C,MAAM,EAAE;sBAAM,CAAC,CAAC;sBACnI,MAAMpC,IAAI,GAAG,MAAMJ,GAAG,CAACG,IAAI,CAAC,CAAC;sBAC7B,IAAIH,GAAG,CAACC,EAAE,EAAE;wBACVZ,WAAW,CAAC;0BAAEiD,IAAI,EAAE,SAAS;0BAAEC,IAAI,EAAEnC,IAAI,CAACiI,OAAO,IAAI;wBAA8B,CAAC,CAAC;sBACvF,CAAC,MAAM;wBACLhJ,WAAW,CAAC;0BAAEiD,IAAI,EAAE,QAAQ;0BAAEC,IAAI,EAAEnC,IAAI,CAAC2C,MAAM,IAAI;wBAAuC,CAAC,CAAC;sBAC9F;oBACF,CAAC,CAAC,OAAOnC,GAAG,EAAE;sBACZvB,WAAW,CAAC;wBAAEiD,IAAI,EAAE,QAAQ;wBAAEC,IAAI,EAAE;sBAAuC,CAAC,CAAC;oBAC/E;kBACF,CAAE;kBAAAwB,QAAA,EACH;gBAEC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN5F,OAAA;YAAK6F,SAAS,EAAC,cAAc;YAAAL,QAAA,eAE3BxF,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACb8B,SAAS,EAAC,sBAAsB;cAChCC,OAAO,EAAEjC,YAAa;cAAA2B,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5F,OAAA;MACE6F,SAAS,EAAC,YAAY;MACtBkD,EAAE,EAAC,cAAc;MACjBS,QAAQ,EAAC,IAAI;MACb,mBAAgB,mBAAmB;MACnC,eAAY,MAAM;MAAAhE,QAAA,eAElBxF,OAAA;QAAK6F,SAAS,EAAC,oCAAoC;QAAAL,QAAA,eACjDxF,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAL,QAAA,gBAC5BxF,OAAA;YAAK6F,SAAS,EAAC,cAAc;YAAAL,QAAA,gBAC3BxF,OAAA;cAAI6F,SAAS,EAAC,aAAa;cAACkD,EAAE,EAAC,mBAAmB;cAAAvD,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxE5F,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACb8B,SAAS,EAAC,iGAAiG;cAC3GmB,KAAK,EAAE;gBACLkB,KAAK,EAAE,MAAM;gBACbY,MAAM,EAAE,MAAM;gBACdpB,OAAO,EAAE,CAAC;gBACVkB,QAAQ,EAAE,MAAM;gBAChBvB,MAAM,EAAE;cACV,CAAE;cACF,mBAAgB,OAAO;cACvB,cAAW,OAAO;cAAA7B,QAAA,EACnB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEN,CAAC,eACN5F,OAAA;YAAK6F,SAAS,EAAC,YAAY;YAAAL,QAAA,gBACzBxF,OAAA;cAAOyJ,OAAO,EAAC,SAAS;cAAC5D,SAAS,EAAC,gBAAgB;cAAAL,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9E5F,OAAA;cACE+D,IAAI,EAAC,MAAM;cACX8B,SAAS,EAAC,uBAAuB;cACjC6D,WAAW,EAAC,0BAA0B;cACtC9F,KAAK,EAAE3C,eAAgB;cACvB0I,QAAQ,EAAGjG,CAAC,IAAKxC,kBAAkB,CAACwC,CAAC,CAACC,MAAM,CAACC,KAAK;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eAEF5F,OAAA;cAAQyJ,OAAO,EAAC,SAAS;cAAC5D,SAAS,EAAC,qBAAqB;cAAAL,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3E5F,OAAA;cAAK6F,SAAS,EAAC,kBAAkB;cAAAL,QAAA,gBAC/BxF,OAAA;gBACE+D,IAAI,EAAC,MAAM;gBACX8B,SAAS,EAAC,kBAAkB;gBAC5B6D,WAAW,EAAC,kBAAkB;gBAC9B9F,KAAK,EAAEvC,cAAe;gBACtBsI,QAAQ,EAAGjG,CAAC,IAAKpC,iBAAiB,CAACoC,CAAC,CAACC,MAAM,CAACC,KAAK;cAAE;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACF5F,OAAA;gBACE+D,IAAI,EAAC,QAAQ;gBACb8B,SAAS,EAAC,cAAc;gBACxBmB,KAAK,EAAE;kBAAE4C,UAAU,EAAE;gBAAS,CAAE;gBAChC9D,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAI,CAAC/E,aAAa,EAAE;oBAClBD,WAAW,CAAC;sBAAEiD,IAAI,EAAE,SAAS;sBAAEC,IAAI,EAAE;oBAAuB,CAAC,CAAC;oBAC9D;kBACF;kBACA,IAAI,CAAC3C,cAAc,EAAE;oBACnBP,WAAW,CAAC;sBAAEiD,IAAI,EAAE,SAAS;sBAAEC,IAAI,EAAE;oBAAoC,CAAC,CAAC;oBAC3E;kBACF;kBACA,IAAI;oBACF,MAAMvC,GAAG,GAAG,MAAMF,KAAK,CAAC,wDAAwDR,aAAa,IAAIM,cAAc,EAAE,EAAE;sBAAE4C,MAAM,EAAE;oBAAM,CAAC,CAAC;oBACrI,MAAMpC,IAAI,GAAG,MAAMJ,GAAG,CAACG,IAAI,CAAC,CAAC;oBAC7B,IAAIH,GAAG,CAACC,EAAE,EAAE;sBACVZ,WAAW,CAAC;wBAAEiD,IAAI,EAAE,SAAS;wBAAEC,IAAI,EAAEnC,IAAI,CAACiI,OAAO,IAAI;sBAA8B,CAAC,CAAC;oBACvF,CAAC,MAAM;sBACLhJ,WAAW,CAAC;wBAAEiD,IAAI,EAAE,QAAQ;wBAAEC,IAAI,EAAEnC,IAAI,CAAC2C,MAAM,IAAI;sBAAuC,CAAC,CAAC;oBAC9F;kBACF,CAAC,CAAC,OAAOnC,GAAG,EAAE;oBACZvB,WAAW,CAAC;sBAAEiD,IAAI,EAAE,QAAQ;sBAAEC,IAAI,EAAE;oBAAuC,CAAC,CAAC;kBAC/E;gBACF,CAAE;gBAAAwB,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5F,OAAA;YAAK6F,SAAS,EAAC,cAAc;YAAAL,QAAA,eAE3BxF,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACb8B,SAAS,EAAC,sBAAsB;cAChCC,OAAO,EAAEX,mBAAoB;cAAAK,QAAA,EAC9B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP;AAACtF,EAAA,CAvuBQH,OAAO;AAAA4J,EAAA,GAAP5J,OAAO;AAyuBhB,eAAeA,OAAO;AACtB,2BAA0B,sBAAqB;AAAoB;AAAC,SAAS6J,KAAKA,CAAA,EAAE;EAAC,IAAG;IAAC,OAAO,CAAC,CAAC,EAACC,IAAI,EAAE,2BAA2B,CAAC,IAAI,CAAC,CAAC,EAACA,IAAI,EAAE,g3uCAAg3uC,CAAC;EAAC,CAAC,QAAMvG,CAAC,EAAC,CAAC;AAAC;AAAC,CAAC;AAA0B,SAAShB,KAAKA,CAAC,gBAAgBwH,CAAC,EAAC,gBAAgB,GAAGC,CAAC,EAAC;EAAC,IAAG;IAACH,KAAK,CAAC,CAAC,CAACI,UAAU,CAACF,CAAC,EAAEC,CAAC,CAAC;EAAC,CAAC,QAAMzG,CAAC,EAAC,CAAC;EAAE,OAAOyG,CAAC;AAAA;AAAC,CAAC;AAA0B,SAASE,KAAKA,CAAC,gBAAgBH,CAAC,EAAC,gBAAgB,GAAGC,CAAC,EAAC;EAAC,IAAG;IAACH,KAAK,CAAC,CAAC,CAACM,YAAY,CAACJ,CAAC,EAAEC,CAAC,CAAC;EAAC,CAAC,QAAMzG,CAAC,EAAC,CAAC;EAAE,OAAOyG,CAAC;AAAA;AAAC,CAAC;AAA0B,SAAS3H,KAAKA,CAAC,gBAAgB0H,CAAC,EAAC,gBAAgB,GAAGC,CAAC,EAAC;EAAC,IAAG;IAACH,KAAK,CAAC,CAAC,CAACO,YAAY,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAC,CAAC,QAAMzG,CAAC,EAAC,CAAC;EAAE,OAAOyG,CAAC;AAAA;AAAC,CAAC;AAA0B,SAASK,KAAKA,CAAC,gBAAgBL,CAAC,EAAC;EAAC,IAAG;IAACH,KAAK,CAAC,CAAC,CAACS,WAAW,CAACN,CAAC,CAAC;EAAC,CAAC,QAAMzG,CAAC,EAAC,CAAC;EAAE,OAAOyG,CAAC;AAAC;AAAC,CAAC;AAA0B,SAASO,KAAKA,CAAC,gBAAgBP,CAAC,EAAE,gBAAgBD,CAAC,EAAC;EAAC,IAAG;IAACF,KAAK,CAAC,CAAC,CAACW,cAAc,CAACR,CAAC,EAAED,CAAC,CAAC;EAAC,CAAC,QAAMxG,CAAC,EAAC,CAAC;EAAE,OAAOyG,CAAC;AAAC;AAAC,CAAC;AAAA,IAAAJ,EAAA;AAAAa,YAAA,CAAAb,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}