import asyncio
import sys
from pathlib import Path
from playwright.async_api import async_playwright
# DEST1 = Path("persistent/profile")

# DEST2 = Path("UI_agent/persistent/profile")
def get_target_info():
    """Get target URL and profile path from command line arguments"""
    if len(sys.argv) >= 2:
        target_url = sys.argv[1]
    else:
        target_url = "https://smt-serco-web-qa.azurewebsites.net/"
    
    if len(sys.argv) >= 3:
        profile_path = Path(sys.argv[2])
    else:
        # Default to profile directory in the same location as script
        profile_path = Path(__file__).parent / "profile"
    
    return target_url, profile_path

async def create_browser_profile(target_url, profile_dir):
    """Create a new browser profile and navigate to target URL"""
    print(f"🚀 Creating browser profile at: {profile_dir}")
    print(f"🌐 Target URL: {target_url}")
    
    playwright = await async_playwright().start()
    
    try:
        # Create the profile directory if it doesn't exist
        profile_dir.mkdir(parents=True, exist_ok=True)
        
        browser = await playwright.chromium.launch_persistent_context(
            user_data_dir=str(profile_dir),
            headless=False,
            slow_mo=100,
            # Additional options for better compatibility
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled'
            ]
        )
        
        page = await browser.new_page()
        
        print("📄 Navigating to target URL...")
        await page.goto(target_url, wait_until='networkidle', timeout=30000)
        
        print("⏳ Waiting for page to stabilize...")
        await asyncio.sleep(120)  # Reduced from 120 seconds for faster testing
        
        # Take a screenshot to verify the page loaded
        screenshot_path = profile_dir / "initial_page.png"
        await page.screenshot(path=str(screenshot_path))
        print(f"📸 Screenshot saved: {screenshot_path}")
        
        print("✅ Profile created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating profile: {str(e)}")
        raise
    finally:
        await browser.close()
        await playwright.stop()

async def ensure_browser_profile():
    """Ensure browser profile exists, create if necessary"""
    target_url, profile_dir = get_target_info()
    
    print(f"🔍 Checking profile directory: {profile_dir}")
    
    # Check if profile exists and has content
    if profile_dir.exists() and any(profile_dir.iterdir()):
        print("✅ Browser profile already exists.")
        
        # List some contents to verify
        contents = list(profile_dir.iterdir())[:5]  # First 5 items
        print(f"📁 Profile contents sample: {[item.name for item in contents]}")
        
        # Check if it's a valid Chromium profile
        if (profile_dir / "Default").exists() or (profile_dir / "Local State").exists():
            print("✅ Valid Chromium profile detected.")
        else:
            print("⚠️ Profile exists but may not be valid, recreating...")
            await create_browser_profile(target_url, profile_dir)
    else:
        print("🚀 Browser profile does not exist. Creating a new one.")
        await create_browser_profile(target_url, profile_dir)

def ensure_profile_sync():
    """Synchronous wrapper for profile creation"""
    
    # folders = [

    #     PROFILE_DIR,

    #     DEST1,

    #     DEST2,

    # ]
    
    # for folder in folders:

    #     try:

    #         shutil.rmtree(folder)

    #         print(f"Deleted {folder}")

    #     except FileNotFoundError:

    #         print(f"{folder} does not exist")

    #     except Exception as e:

    #         print(f"Error deleting {folder}: {e}")

    asyncio.run(ensure_browser_profile())


# if __name__ == "__main__":
#     ensure_profile_sync()