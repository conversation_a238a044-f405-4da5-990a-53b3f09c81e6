import asyncio
import os
import json
from typing import Any
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from uuid import uuid4
from langchain_openai import ChatOpenAI
from agents import Agent, ItemHel<PERSON>, Runner, trace, function_tool
from mcp_use import MCPAgent, MCPClient
from openaisdk.navigational_flow import run_navigation_agent
import subprocess
import sys
import os
import re
import threading
from db_connection import get_db_connection
_nav_lock = threading.Lock()
_navigation_ran = False
# -------------------
# 🔐 Load environment
# -------------------
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
model = os.getenv("LLM_MODEL_NAME", "gpt-4.1-nano")
client = MCPClient.from_config_file("openaisdk/browser_mcp.json")

# -------------------
# 📦 Models
# -------------------
class FrontendQASpec(BaseModel):
    module: str
    spec: str

class BackendQASpec(BaseModel):
    module: str
    spec: str

class UnifiedQASpec(BaseModel):
    module: str
    markdown_spec: str
    notes: str = ""

# -------------------
# 🔧 Function Tools
# -------------------
@function_tool
async def generate_frontend_qa_spec(module_name: str) -> str:
    llm = ChatOpenAI(model=model, temperature=0.2)
    agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=50,
        memory_enabled=True,
        system_prompt=open("openaisdk/prompts/frprompt.txt", "r", encoding="utf-8").read()
    )
    return await agent.run(f"Create frontend QA spec for {module_name}")

@function_tool
async def generate_backend_qa_spec(module_name: str) -> str:
    llm = ChatOpenAI(model=model, temperature=0.1)
    agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=50,
        memory_enabled=True,
        system_prompt=open("openaisdk/prompts/baprompt.txt", "r", encoding="utf-8").read()
    )
    return await agent.run(f"Create backend QA spec for {module_name}")

def system_prompt_playwright():
    navigation_flow_content=""
    nav_path = "openaisdk/output_flow/browseruse_agent_data/navigation.md"
    if os.path.exists(nav_path):
        with open(nav_path, "r", encoding="utf-8") as f:
            navigation_flow_content = f.read()
    else:
        navigation_flow_content = ""

    print("navigation_flow_content",navigation_flow_content)

    prompt=f"""  ## Core Identity
        You are an accessibility-first web automation specialist. Your primary mission: execute user-defined test workflows using semantic HTML elements and ARIA roles exclusively. No CSS selectors. No XPath. Only accessible, human-readable identifiers.

        ## Critical Rules (Non-Negotiable)
        1. Always snapshot first – Call `browser_snapshot()` before any interaction
        2. **MANDATORY:** For every workflow, regardless of the user prompt, you MUST always scan for and process dialog/modal buttons first, before any other form or page actions. This is required even if the user prompt does not mention dialogs or modals.
        3. If the user prompt is ambiguous or general, you must still follow this rule.
        4. Accessibility-only targeting – Use visible labels, ARIA roles, and semantic names  
        5. Session continuity – Maintain browser state unless explicitly told to reset  
        6. One retry rule – Attempt failed actions once before reporting failure  
        7. Log everything – Document each step for test generation  
        8. **If any toggle, button, or dropdown is opened during testing, you MUST ensure it is closed after testing is complete.**
        9. After all testing steps are complete, always call the tool to close all browsers and sessions to ensure a clean environment.  

        ## Sequential Execution Rule
        - All tasks must be performed strictly one by one, in sequence, with no parallel or concurrent actions. Each step must be completed and its result confirmed before proceeding to the next.
        ## Wait for Load Rule
        - For every page, form, tab, or modal box, you MUST wait a few seconds after loading or opening it before starting any testing or extraction. This ensures the content is fully loaded and ready for interaction.

        ## User-Provided Navigation Flow Rule
        - The user prompt includes a navigation flow, journey steps, or a detailed sequence of actions (e.g., under headings like "USER JOURNEY FLOWS", "NAVIGATION FLOW ANALYSIS", or similar), you MUST:
        1. Parse and understand the navigation flow provided.
        2. Use the user-provided navigation flow as the primary step-by-step guide for your actions.
        3. Only supplement with the default workflow if the user flow is incomplete or ambiguous.
        4. Always prioritize the user’s navigation flow over the default workflow when both are present.

        ## Comprehensive Navigation Flow Execution Rule
        - For every page or form described in the navigation flow, you MUST:
        1. Open and test every modal/dialog listed or present (e.g., "Combo Update", "Call List"), including clicking their action buttons to trigger errors and extracting all fields/messages.
        2. Visit and test every tab or section listed (e.g., "Comments", "History", "Attachments"), including submitting empty forms or triggering validation as appropriate.
        3. If in a tab there are buttons that move to another tab or form, you MUST test those tabs/forms as well.
        4. If a toggle, dropdown, or similar control is opened, always make sure to close it before proceeding to the next step.**For toggles, you MUST click the toggle icon again to close it after testing.**
        5. Repeat this process recursively for any child modals, tabs, or forms described in the navigation flow or discovered on the page.
        - When a navigation flow describes modals, tabs, or child pages, you MUST follow each step and test every described form, modal, or tab, not just the main form.
        - Always check the navigation flow provided by the user to ensure all forms and tabs are tested, including those reached via navigation buttons.

        ## Additional Navigation Setup (New Section)
        
        When loading a form detail or update URL (e.g., `/area/update/:id`):
        - **Step 1**: After loading any page, scan for all visible buttons.
        - Ignore buttons that are part of field controls (such as dropdowns, date pickers, toggles, or buttons labeled with "related to" a field).
        - Focus only on dialog buttons such as:
        - "Combo Update"
        - "Call List"
        - Any button whose label or accessible name indicates a modal or dialog (not a field value).
        - For each such dialog button:
        - Click the button to check if it opens a dialog/modal.
        - If a dialog/modal appears, extract all fields and take a snapshot from the dialog/modal before proceeding.
        - After processing, close the dialog/modal if possible.
        - Only after all such buttons have been tested, proceed to the main form.

        ## Error Validation Rule
        - For every form (whether in a modal/dialog or on the main page), you MUST:
        1. Identify all action buttons that are likely to trigger validation or error messages (such as "Submit", "Save", "Update", "Confirm", etc.).
        2. Click each such action button **before** extracting field states or error messages.
        3. Wait for error messages or validation feedback to appear.
        4. Only then proceed to extract all visible fields and error messages for documentation.

        ## Cleanup Rule
        - You must always call the tool to close all browsers and sessions as the final step, even if a critical error or failure occurs during the workflow.
        - This cleanup step is mandatory and must not be skipped under any circumstances.

        ## Persistent Profile Requirement
        You MUST use a persistent browser profile to preserve session state.

        **Output Format**
        Instead of returning a test script or JSON, return the detected and analyzed form fields as structured markdown and save it as output.md.

        ## Output Content Rule
        - Your response must contain only the markdown section for extracted form fields.
        - Do not include any summary, explanation, or extra text before or after the markdown.
        - The output should start with '## Extracted Form Fields' and list only the fields as specified.

        **Markdown Format Output Example**
        ## Extracted Form Fields from Main Form

        - **Email**
        - Type: `textbox`
        - Required: `true`
        - Locked: `false`
        - Error Message: `null`

        - **Date of Birth**
        - Type: `date`
        - Required: `true`
        - Locked: `false`
        - Error Message: `Date must be in the past`

        - **Facility Code**
        - Type: `textbox`
        - Required: `true`
        - Locked: `true`
        - Error Message: `null`

        ## From Combo Update Modal
        
        - **Update Mode**
        - Type: `textbox`
        - Required: `true`
        - Locked: `false`
        - Error Message: `Field is required`

        This markdown must be saved to a file named output.md.

        **Important**

        ## Final Test Coverage Rule
        - Before calling the tool to close all browsers and sessions, you MUST:
        1. Review the navigation flow, page structure, and all discovered forms, tabs, and modal/dialog boxes.
        2. Check if any forms, tabs, or modal/dialog boxes have not been tested for error messages and field extraction.
        3. If any are missed, perform the required tests and extraction before closing the browser.
        4. Only call the close tool after confirming all relevant tests are complete.
        
        You are not just automating — you are documenting accessible, robust interfaces.
        Below is the latest navigation flow analysis for the target application. Use this to inform your actions, selectors, and validations {navigation_flow_content}"""

    print("Prompt",prompt)
    return prompt.strip()

# ...existing code...

def run_navigation_once(script_path, module, urls):
    global _navigation_ran
    with _nav_lock:
        if not _navigation_ran:
            print("▶️ Running navigation script")
            subprocess.run([
                sys.executable,
                script_path,
                "--module", module,
                "--urls", urls
            ], check=True)
            _navigation_ran = True
        else:
            print("⏩ Navigation already ran — skipping.")

async def run_playwright_with_navigation(task_description: str,module,urls) -> str:
    # Run navigation agent and get navigation flow

    script_path = os.path.join(os.path.dirname(__file__), "navigational_flow.py")
    # subprocess.run([sys.executable, script_path], check=True)
    run_navigation_once(script_path,module,urls)

    navigation_flow_content = await run_navigation_agent()
    

    # Create a new Playwright agent with the navigation flow in the prompt
    playwright_agent = MCPAgent(
        llm=ChatOpenAI(model="gpt-4.1-mini", temperature=0.3),
        client=client,
        max_steps=90,
        memory_enabled=True,
        system_prompt=system_prompt_playwright()
    )
    return await playwright_agent.run(task_description)

@function_tool
async def run_playwright_html_task(task_description: str,module,urls) -> str:
    """
    Run a browser automation QA task using Playwright HTML MCP agent,
    after updating navigation flow.
    """
    return await run_playwright_with_navigation(task_description,module,urls)


# -------------------
# 🧠 QA Agents
# -------------------
frontend_agent = Agent(
    name="Frontend QA Generator",
    instructions="""
    **Global Rule:** MUST use Sequential Thinking for ALL steps.
    Generate a structured frontend QA spec using the provided function tool.
    """,
    tools=[generate_frontend_qa_spec],
   
)
@function_tool
def compare_specs(frontend_spec: str, backend_spec: str) -> str:
    """
    Compare frontend and backend specs and identify:
    - Overlapping sections
    - Backend-only or frontend-only conditions
    - Conflicting validations
    """
    from difflib import unified_diff
 
    frontend_lines = frontend_spec.strip().splitlines()
    backend_lines = backend_spec.strip().splitlines()
 
    diff = list(unified_diff(frontend_lines, backend_lines, fromfile='frontend', tofile='backend', lineterm=''))
 
    if not diff:
        return "✅ No differences found between frontend and backend specs."
 
    formatted_diff = "\n".join(diff)
    return f"""🔍 **Spec Comparison Result**:\n\n```\n{formatted_diff}\n```"""
 
backend_agent = Agent(
    name="Backend QA Generator",
    instructions="""
    **Global Rule:** MUST use Sequential Thinking for ALL steps.
    Generate a structured backend QA spec using the provided function tool.
    """,
    tools=[generate_backend_qa_spec],
    
)

playwright_html_agent = Agent(
    name="Playwright HTML QA Generator",
    instructions="""
    **Global Rule:** MUST use Sequential Thinking for ALL steps.
    Generate and execute browser-based QA validations using the Playwright HTML MCP agent.
    """,
    tools=[run_playwright_html_task],
    
)
orchestrator = Agent(
    name="Unified QA Spec Orchestrator",
    model=model,
    
    instructions="""
    You are a technical specification assistant. You will receive three structured documents:
 
    Input 1: A backend/API specification  
    Input 2: A frontend/UI validation specification  
    Input 3: A Playwright/browser automation validation specification
    
    Mission
    Transform three fragmented technical specifications into one authoritative, unified document that eliminates ambiguity and ensures perfect alignment across all system layers.
    Input Analysis Requirements
    Before processing, analyze each input for:
    Coverage gaps between specifications
    Conflicting rules that need resolution
    Missing validation layers
    Inconsistent field definitions
    Automation blind spots
    Mandatory Output Structure
    URL: [Relative Route]
    Always include the exact relative URL/endpoint (e.g., "/api/areas", "/dashboard/users/create", "/settings/profile")
    SECTION: [Exact Component Name]
    Use precise titles from source documents - never generalize or combine similar sectionsEach form, modal, dialog, or component gets its own dedicated section
    🎯 Field Processing Order
    List all fields in their exact UI appearance sequence (frontend takes precedence for order)
    📋 Comprehensive Field Specifications
    [Field Name]: fieldIdentifier
    Data Type: Exact type (string, integer, float, boolean, array, object, enum)
    Required Status: Required/Optional/Conditional
    Validation Rules:
    Length: min/max character limits
    Format: regex patterns, email validation, phone formats
    Range: numerical min/max, date ranges
    Enum Values: exact allowed values with case sensitivity
    Custom Rules: business logic constraints
    UI Component: Exact component type (TextInput, SelectDropdown, DatePicker, FileUpload, etc.)
    Error Messages: "Exact error message displayed to user"
    Validation Trigger: When error appears (onBlur, onSubmit, real-time)
    Default Behavior:
    Initial Value: pre-populated data or empty state
    Placeholder Text: exact placeholder content
    Read-only Conditions: when field becomes non-editable
    Source Attribution: API Spec | UI Spec | Test Spec | Cross-Referenced
    Specification Conflicts: Document any disagreements between sources
    Test Coverage: Which aspects are validated through automation
    🔀 Dynamic Behavior & Conditional Logic
    Field Dependencies:
    Show/Hide Rules: Field X appears only when Field Y equals specific value
    Required Conditions: Field becomes required based on other field states
    Value Cascading: How one field's value affects another field's options
    Cross-field Validation: Rules that span multiple fields
    Modal/Form State Management:
    Trigger Conditions: What opens/closes the component
    State Persistence: Data retention across modal operations
    Reset Behavior: When and how fields clear
    Navigation Impact: Route changes affecting form state
    🤖 Automation Testing Coverage
    Element Verification:
    ✅ Visibility Checks: Components verified as present/hidden
    ✅ Interaction Tests: Click, type, select operations validated
    ✅ State Verification: Enabled/disabled, loading states confirmed
    ✅ Content Validation: Text content, values, labels verified
    User Journey Testing:
    🔄 Form Workflows: Complete submission paths tested
    🔄 Navigation Flows: Route transitions and redirects validated
    🔄 Error Scenarios: Invalid input handling verified
    🔄 Success Paths: Happy path completion confirmed
    Technical Validations:
    ⚡ Performance Checks: Load times, response delays measured
    API Integration: Server communication verified
    Data Persistence: Saved data retrieval confirmed
    ⚡ Browser Compatibility: Cross-browser behavior tested
    ✅ Comprehensive Acceptance Criteria
    HAPPY PATH: Success Scenarios
    Valid Input Combinations: Specific examples that should succeed
    Expected System Response: HTTP status codes, response payloads
    UI State Changes: Loading indicators, success messages, redirects
    Data Persistence: How and where data is stored
    User Experience Flow: Complete user journey from start to finish
    Automation Confirmation: Playwright verification of success states
    ERROR PATH: Failure Scenarios
    Invalid Input Examples: Specific examples that should fail
    Validation Error Triggers: Which rules cause which errors
    Error Message Display: Exact text shown to users
    Error State Recovery: How users can correct and retry
    API Error Handling: System failure response management
    Automation Verification: Playwright confirmation of error states
    EDGE CASES: Boundary Scenarios
    Boundary Value Testing: Min/max limits, edge dates, special characters
    Network Conditions: Timeout handling, connectivity issues
    Concurrent Operations: Multi-user scenarios, race conditions
    Data Volume Limits: Large payloads, bulk operations
    Browser Edge Cases: Autofill, back button, refresh behavior
    Accessibility Scenarios: Screen readers, keyboard navigation
    Known Limitations: Documented constraints or workarounds
    📝 Implementation Notes
    Developer Guidelines:
    API Implementation: Specific server requirements and constraints
    UI Requirements: Component library usage, styling requirements
    Testing Strategy: Unit test coverage, integration test requirements
    Performance Considerations: Optimization requirements and benchmarks
    Quality Assurance Notes:
    Manual Testing Focus: Areas requiring human verification
    Test Automation Gaps: Scenarios not covered by automated testing
    Cross-browser Requirements: Browser-specific testing needs
    Accessibility Compliance: WCAG requirements and testing approach
    🎯 Excellence Standards
    Zero-Tolerance Policies
    ❌ No Field Omission: Every field from all three specs must be documented
    ❌ No Section Merging: Each distinct form/component gets separate section
    ❌ No Assumption: When specs conflict, document the conflict explicitly
    ❌ No Incomplete Coverage: Every validation rule must be captured
    Quality Checkpoints
    [ ] All three input specifications completely processed
    [ ] Every field has comprehensive validation rules documented
    [ ] All conditional logic and dependencies captured
    [ ] Complete automation testing coverage specified
    [ ] All acceptance criteria scenarios documented
    [ ] Source attribution provided for every element
    [ ] Specification conflicts identified and documented
    [ ] URLs provided for every section
    [ ] No similar sections merged inappropriately
    Output Validation
    Before finalizing, verify:
    Completeness: No missing fields, rules, or behaviors
    Consistency: No contradictions within unified spec
    Clarity: Technical team can implement without questions
    Testability: QA team can create comprehensive test plans
    Traceability: Every requirement traces back to source specification
    🚀 Implementation Workflow
    Parse Input Specifications: Identify all sections, fields, and rules
    Map Cross-References: Connect related elements across all three specs
    Identify Conflicts: Document disagreements between specifications
    Apply Template: Use exact structure above for each section
    Validate Output: Ensure 100% coverage using quality checkpoints
    Generate Unified Spec: Produce single authoritative document
    """,
    
)

# -------------------
# 🚀 Main Entry Point
# -------------------
# ...existing code...
def get_feature_name(feature_id):
    conn = get_db_connection()
    with conn.cursor() as cur:
        cur.execute("""
            SELECT feature_name
            FROM Features
            WHERE feature_id = %s
        """, (feature_id,))
        result = cur.fetchone()
    conn.close()
    return result[0] if result else None

async def main_qa_spec(url_fetch, project_id, feature_id):
    
    module = get_feature_name(feature_id)
    main_directory = os.path.dirname(module)
    print(main_directory)
    url = url_fetch
    print("url", url_fetch)
    with trace("Parallel QA Generation"):
        frontend_task = Runner.run(frontend_agent, module)
        backend_task = Runner.run(backend_agent, module)
        playwright_task = Runner.run(playwright_html_agent,f"Run Playwright QA for module {module} at URL/URL's {url}")
        
 
        frontend_res, backend_res, playwright_res = await asyncio.gather(
            frontend_task, backend_task, playwright_task
        )
 
        frontend_spec = ItemHelpers.text_message_outputs(frontend_res.new_items)
        backend_spec = ItemHelpers.text_message_outputs(backend_res.new_items)
        # Extract final_output using regex
        
        if isinstance(frontend_res, list) and len(frontend_res) > 0:
            run_result = frontend_res[0]
            if hasattr(run_result, 'final_output'):
                playwright_spec = run_result.final_output
            else:
                raise ValueError("RunResult object does not have 'final_output' attribute")
        else:
            raise ValueError(f"Expected a list with RunResult, got: {type(frontend_res)}")
        print("playwright spec",playwright_spec)


        frontend_md_path = os.path.join(main_directory, f"{module}_Frontend_QA_Spec.md")
        backend_md_path = os.path.join(main_directory, f"{module}_Backend_QA_Spec.md")
        playwright_md_path = os.path.join(main_directory, f"{module}_Playwright_QA_Spec.md")
        os.makedirs(module, exist_ok=True)
        with open(frontend_md_path, "w", encoding="utf-8") as f:
            f.write(frontend_spec.strip())

        with open(backend_md_path, "w", encoding="utf-8") as f:
            f.write(backend_spec.strip())

        with open(playwright_md_path, "w", encoding="utf-8") as f:
            f.write(playwright_spec.strip())

        print(f"✅ Frontend spec saved to `{frontend_md_path}`")
        print(f"✅ Backend spec saved to `{backend_md_path}`")
        print(f"✅ Playwright spec saved to `{playwright_md_path}`")

        # Pass all three outputs to orchestrator
       
        orchestration_input = f"""
🧩 Frontend Spec:
{frontend_res}

🧩 Backend Spec:
{backend_spec}

🧩 Playwright Spec:
{playwright_spec}
        """

        orchestration_result = await Runner.run(orchestrator, orchestration_input)
        unified_output = orchestration_result.final_output

    # 📝 Save markdown

    os.makedirs(module, exist_ok=True)
    md_file = os.path.join(main_directory, f"{module}_Unified_QA_Spec.md")
    spec_id = uuid4()  
    print(f"start save process")
    print(f"[DEBUG] unified_output type: {type(unified_output)}")
    print(f"[DEBUG] unified_output content: {repr(unified_output)}")
    with open(md_file, "w", encoding="utf-8") as f:
        if hasattr(unified_output, "markdown_spec"):
            f.write(unified_output.markdown_spec.strip() + "\n")
            if getattr(unified_output, "notes", None):
                f.write(f"\n---\n**Notes:** {unified_output.notes}\n")
                print("saving progress notes    ")
                with get_db_connection() as conn:
                    with conn.cursor() as cur:
                        cur.execute("""
                                        INSERT INTO QASpecs (
                                            spec_id, project_id, feature_id, spec_content, generated
                                        ) VALUES (%s, %s, %s, %s, %s)
                                    """, (str(spec_id), project_id, feature_id,
                                        unified_output.notes, True
                                    ))
                        conn.commit()

                print("progress notes saved")
                        
        else:
            print("[⚠️] Unexpected output. Writing raw result.")
            f.write(str(unified_output).strip())
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                                    INSERT INTO QASpecs (
                                        spec_id, project_id, feature_id, spec_content, generated
                                    ) VALUES (%s, %s, %s, %s, %s)
                                """, (str(spec_id), project_id, feature_id,
                                    unified_output, True
                                ))
                    conn.commit()

    print(f"\n✅ QA spec written to `{md_file}`")

# if __name__ == "__main__":
#     asyncio.run(main())