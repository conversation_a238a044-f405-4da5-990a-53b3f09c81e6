{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\MCP-Project-FullStack-local\\\\MCP-Project-FullStack\\\\mcp-react\\\\src\\\\Components\\\\ProjectDetails\\\\ProjectDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './ProjectDetails.css';\nimport Links from './Links';\nimport TestSteps from './TestSteps';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProjectDetails({\n  activeProjectId,\n  projectLi,\n  setProjectLi\n}) {\n  _s();\n  // Shared state for flash messages\n  const [flashMsg, setFlashMsg] = useState(null);\n  const selectedLinkId = localStorage.getItem('selectedLinkId');\n  // console.log(\"selectedLinkId prchat\", selectedLinkId);\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [flashMsg && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `alert alert-${flashMsg.type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`,\n      style: {\n        zIndex: 2000,\n        minWidth: 300,\n        maxWidth: 500\n      },\n      role: \"alert\",\n      children: [flashMsg.text, /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        className: \"btn-close\",\n        \"aria-label\": \"Close\",\n        onClick: () => {\n          setFlashMsg(null);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row min-vh-100 bg-white ms-1 me-1 border-start border-end border-bottom border-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-4\",\n        style: {\n          maxHeight: 'calc(100vh - 180px)',\n          overflowY: 'auto',\n          overflowX: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(Links, {\n          activeProjectId: activeProjectId,\n          projectLi: projectLi,\n          setProjectLi: setProjectLi,\n          flashMsg: flashMsg,\n          setFlashMsg: setFlashMsg\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        style: {\n          maxHeight: 'calc(100vh - 180px)',\n          overflowY: 'auto',\n          overflowX: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(TestSteps, {\n          activeProjectId: activeProjectId,\n          projectLi: projectLi,\n          flashMsg: flashMsg,\n          setFlashMsg: setFlashMsg\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(ProjectDetails, \"tRw/0kqlHVqNKpYd6HYTVKneDFw=\");\n_c = ProjectDetails;\nexport default ProjectDetails;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetails\");", "map": {"version": 3, "names": ["React", "useState", "Links", "TestSteps", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProjectDetails", "activeProjectId", "projectLi", "setProjectLi", "_s", "flashMsg", "setFlashMsg", "selectedLinkId", "localStorage", "getItem", "children", "className", "type", "style", "zIndex", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "role", "text", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "maxHeight", "overflowY", "overflowX", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/MCP-Project-FullStack-local/MCP-Project-FullStack/mcp-react/src/Components/ProjectDetails/ProjectDetails.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport './ProjectDetails.css';\r\nimport Links from './Links';\r\nimport TestSteps from './TestSteps';\r\n\r\nfunction ProjectDetails({ activeProjectId, projectLi, setProjectLi }) {\r\n  // Shared state for flash messages\r\n  const [flashMsg, setFlashMsg] = useState(null);\r\n  const selectedLinkId = localStorage.getItem('selectedLinkId');\r\n  // console.log(\"selectedLinkId prchat\", selectedLinkId);\r\n\r\n  return (\r\n    <>\r\n      {/* Flash message */}\r\n      {flashMsg && (\r\n        <div className={`alert alert-${flashMsg.type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`} style={{zIndex: 2000, minWidth: 300, maxWidth: 500}} role=\"alert\">\r\n          {flashMsg.text}\r\n          <button type=\"button\" className=\"btn-close\" aria-label=\"Close\" onClick={() => { setFlashMsg(null); }}></button>\r\n        </div>\r\n      )}\r\n      <div className=\"row min-vh-100 bg-white ms-1 me-1 border-start border-end border-bottom border-2\">\r\n        {/* Links */}\r\n        <div className=\"col-md-4 mb-4\" style={{ maxHeight: 'calc(100vh - 180px)', overflowY: 'auto', overflowX: 'hidden' }}>\r\n          <Links\r\n            activeProjectId={activeProjectId}\r\n            projectLi={projectLi}\r\n            setProjectLi={setProjectLi}\r\n            flashMsg={flashMsg}\r\n            setFlashMsg={setFlashMsg}\r\n          />\r\n        </div>\r\n        {/* Test Cases/Steps */}\r\n        <div className=\"col-md-8\" style={{ maxHeight: 'calc(100vh - 180px)', overflowY: 'auto', overflowX: 'hidden' }}>\r\n          <TestSteps\r\n            activeProjectId={activeProjectId}\r\n            projectLi={projectLi}\r\n            flashMsg={flashMsg}\r\n            setFlashMsg={setFlashMsg}\r\n          />\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default ProjectDetails;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,sBAAsB;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,SAASC,cAAcA,CAAC;EAAEC,eAAe;EAAEC,SAAS;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACpE;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAMc,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EAC7D;;EAEA,oBACEZ,OAAA,CAAAE,SAAA;IAAAW,QAAA,GAEGL,QAAQ,iBACPR,OAAA;MAAKc,SAAS,EAAE,eAAeN,QAAQ,CAACO,IAAI,oFAAqF;MAACC,KAAK,EAAE;QAACC,MAAM,EAAE,IAAI;QAAEC,QAAQ,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAG,CAAE;MAACC,IAAI,EAAC,OAAO;MAAAP,QAAA,GAChML,QAAQ,CAACa,IAAI,eACdrB,OAAA;QAAQe,IAAI,EAAC,QAAQ;QAACD,SAAS,EAAC,WAAW;QAAC,cAAW,OAAO;QAACQ,OAAO,EAAEA,CAAA,KAAM;UAAEb,WAAW,CAAC,IAAI,CAAC;QAAE;MAAE;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5G,CACN,eACD1B,OAAA;MAAKc,SAAS,EAAC,kFAAkF;MAAAD,QAAA,gBAE/Fb,OAAA;QAAKc,SAAS,EAAC,eAAe;QAACE,KAAK,EAAE;UAAEW,SAAS,EAAE,qBAAqB;UAAEC,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAhB,QAAA,eACjHb,OAAA,CAACH,KAAK;UACJO,eAAe,EAAEA,eAAgB;UACjCC,SAAS,EAAEA,SAAU;UACrBC,YAAY,EAAEA,YAAa;UAC3BE,QAAQ,EAAEA,QAAS;UACnBC,WAAW,EAAEA;QAAY;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1B,OAAA;QAAKc,SAAS,EAAC,UAAU;QAACE,KAAK,EAAE;UAAEW,SAAS,EAAE,qBAAqB;UAAEC,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAhB,QAAA,eAC5Gb,OAAA,CAACF,SAAS;UACRM,eAAe,EAAEA,eAAgB;UACjCC,SAAS,EAAEA,SAAU;UACrBG,QAAQ,EAAEA,QAAS;UACnBC,WAAW,EAAEA;QAAY;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP;AAACnB,EAAA,CAtCQJ,cAAc;AAAA2B,EAAA,GAAd3B,cAAc;AAwCvB,eAAeA,cAAc;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}