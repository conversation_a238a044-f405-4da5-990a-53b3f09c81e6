-- 1. Projects Table

CREATE TABLE Projects (

    project_id UUID PRIMARY KEY,

    project_name TEXT NOT NULL UNIQUE,

    project_url TEXT,

    description TEXT,

    created_at TIMESTAMP DEFAULT NOW()

);
 
-- 2. ProjectFiles Table

CREATE TABLE ProjectFiles (

    file_id UUID PRIMARY KEY,

    project_id UUID NOT NULL,

    filename TEXT NOT NULL,

    s3_link TEXT NOT NULL,

    uploaded_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (project_id) REFERENCES Projects(project_id)

);
 
-- 3. Features Table (group-level, now with file_id)

CREATE TABLE Features (

    feature_id UUID PRIMARY KEY,

    project_id UUID NOT NULL,

    feature_name TEXT NOT NULL,

    description TEXT,

    file_id UUID,  -- ✅ New: optional link to a file

    created_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (project_id) REFERENCES Projects(project_id),

    FOREI<PERSON><PERSON> KEY (file_id) REFERENCES ProjectFiles(file_id),

    UNIQUE (project_id, feature_name)

);
 
-- 4. ProjectLinks Table

CREATE TABLE ProjectLinks (

    link_id UUID PRIMARY KEY,

    project_id UUID NOT NULL,

    feature_id UUID,

    file_id JSONB,

    url TEXT NOT NULL,

    page_title TEXT,

    scraped_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (project_id) REFERENCES Projects(project_id),

    FOREIGN KEY (feature_id) REFERENCES Features(feature_id)

);
 
-- 5. QASpecs Table (feature-level only)

CREATE TABLE QASpecs (

    spec_id UUID PRIMARY KEY,

    project_id UUID NOT NULL,

    feature_id UUID NOT NULL,

    spec_content TEXT NOT NULL,

    generated BOOLEAN DEFAULT TRUE,

    approved BOOLEAN DEFAULT FALSE,

    generated_at TIMESTAMP DEFAULT NOW(),

    approved_at TIMESTAMP,

    updated_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (project_id) REFERENCES Projects(project_id),

    FOREIGN KEY (feature_id) REFERENCES Features(feature_id)

);
 
-- 6. TestCases Table (feature-level only)

CREATE TABLE TestCases (

    testcase_id UUID PRIMARY KEY,

    project_id UUID NOT NULL,

    feature_id UUID NOT NULL,

    spec_id UUID,  -- Optional link to QA spec

    name TEXT NOT NULL,

    description TEXT,

    created_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (project_id) REFERENCES Projects(project_id),

    FOREIGN KEY (feature_id) REFERENCES Features(feature_id),

    FOREIGN KEY (spec_id) REFERENCES QASpecs(spec_id)

);
 
-- 7. TestSteps Table

CREATE TABLE TestSteps (

    step_id UUID PRIMARY KEY,

    testcase_id UUID NOT NULL UNIQUE,

    steps TEXT NOT NULL,

    created_at TIMESTAMP DEFAULT NOW(),

    approved BOOLEAN DEFAULT FALSE,

    FOREIGN KEY (testcase_id) REFERENCES TestCases(testcase_id)

);
 
-- 8. AutomationScripts Table

CREATE TABLE AutomationScripts (

    script_id UUID PRIMARY KEY,

    step_id UUID NOT NULL,

    language TEXT,

    code TEXT NOT NULL,

    folder_path TEXT,

    created_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (step_id) REFERENCES TestSteps(step_id)

);
 
-- 9. TestRuns Table

CREATE TABLE TestRuns (

    run_id UUID PRIMARY KEY,

    project_id UUID NOT NULL,

    run_name TEXT,

    run_started_at TIMESTAMP DEFAULT NOW(),

    run_ended_at TIMESTAMP,

    FOREIGN KEY (project_id) REFERENCES Projects(project_id)

);
 
-- 10. TestResults Table

CREATE TABLE TestResults (

    result_id UUID PRIMARY KEY,

    run_id UUID NOT NULL,

    script_ids UUID[] NOT NULL,

    result_file TEXT,

    executed_at TIMESTAMP DEFAULT NOW(),

    duration_seconds INT,

    remarks TEXT,

    results_json JSONB,

    FOREIGN KEY (run_id) REFERENCES TestRuns(run_id)

);
 
-- 11. DetailedTestResults Table

CREATE TABLE DetailedTestResults (

    id UUID PRIMARY KEY,

    run_id UUID NOT NULL,

    report_id UUID,

    test_name TEXT,

    file_name TEXT,

    status TEXT,

    error_message TEXT,

    stack_trace TEXT,

    start_time TIMESTAMP,

    duration_ms INTEGER,

    project_name TEXT,

    retry INTEGER,

    screenshot_paths JSONB,

    created_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (run_id) REFERENCES TestRuns(run_id),

    FOREIGN KEY (report_id) REFERENCES TestResults(result_id)

);
 
-- 12. ChatHistory Table

CREATE TABLE ChatHistory (

    chat_id UUID NOT NULL,

    project_id UUID NOT NULL,

    user_message TEXT NOT NULL,

    url TEXT NOT NULL,

    response TEXT,

    created_at TIMESTAMP DEFAULT NOW(),

    PRIMARY KEY (chat_id, created_at),

    FOREIGN KEY (project_id) REFERENCES Projects(project_id)

);

 