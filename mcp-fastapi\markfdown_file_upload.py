import boto3
import os
from dotenv import load_dotenv

def _init_s3_client():
    load_dotenv()
    AWS_ACCESS_KEY = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
    AWS_BUCKET_NAME = os.getenv("AWS_BUCKET_NAME", "mcp-qa-engineer")
    AWS_REGION = "ap-south-1"

    if not AWS_ACCESS_KEY or not AWS_SECRET_KEY or not AWS_BUCKET_NAME:
        raise RuntimeError("Missing AWS credentials or bucket name.")

    return boto3.client(
        "s3",
        region_name=AWS_REGION,
        aws_access_key_id=AWS_ACCESS_KEY,
        aws_secret_access_key=AWS_SECRET_KEY,
    ), AWS_BUCKET_NAME, AWS_REGION


def _upload_markdown(local_file_path, s3_prefix):
    if not os.path.exists(local_file_path):
        raise FileNotFoundError(f"File not found: {local_file_path}")

    filename = os.path.basename(local_file_path)
    s3_key = f"{s3_prefix.rstrip('/')}/{filename}"

    s3_client, bucket, region = _init_s3_client()
    s3_client.upload_file(
        local_file_path,
        bucket,
        s3_key,
        ExtraArgs={"ContentType": "text/markdown"}
    )

    return f"https://{bucket}.s3.{region}.amazonaws.com/{s3_key}"


def upload_nav_file_to_s3(local_file_path):
    """Upload a navigation markdown file to S3 (prefix: navs/)"""
    return _upload_markdown(local_file_path, "navs")


def upload_qa_spec_to_s3(local_file_path):
    """Upload a QA spec markdown file to S3 (prefix: qaspecs/)"""
    return _upload_markdown(local_file_path, "qaspecs")