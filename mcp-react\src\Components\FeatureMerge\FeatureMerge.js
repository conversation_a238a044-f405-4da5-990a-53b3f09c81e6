import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';


// Modal Component
function Modal({ show, onClose, title, children }) {
  
  if (!show) return null;
  return (
    <div
      className="modal fade show"
      style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }}
      tabIndex="-1"
      aria-labelledby="modalLabel"
      
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="modalLabel">{title}</h5>
            <button
              type="button"
              className="btn btn-light rounded-circle d-flex align-items-center justify-content-center shadow-sm ms-auto"
              style={{
                width: '32px',
                height: '32px',
                padding: 0,
                fontSize: '1rem',
                border: 'none'
              }}
              onClick={onClose}
              aria-label="Close"
            >
              &times;
            </button>
          </div>
          <div className="modal-body">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

// Flash Message Component with Live Timer
function FlashMessage({ flashMsg, isGrabbing, elapsed, onClose }) {
  if (!flashMsg) return null;

  return (
    <div className={`alert alert-${flashMsg.type} py-2 position-relative`} style={{ whiteSpace: 'pre-line' }}>
      <div className="d-flex align-items-start justify-content-between">
        <div className="flex-grow-1">
          {flashMsg.text}
          {isGrabbing && (
            <div className="mt-2">
              <div className="d-flex align-items-center gap-2">
                <div className="spinner-border spinner-border-sm" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                <span className="fw-bold">Processing... {elapsed}s elapsed</span>
              </div>
            </div>
          )}
        </div>
        {onClose && (
          <button
            type="button"
            className="btn-close btn-close-white ms-2"
            aria-label="Close"
            onClick={onClose}
            style={{ fontSize: '0.8rem' }}
          ></button>
        )}
      </div>
    </div>
  );
}

function DragDropLinks({ activeProjectId, projectLinks, setProjectLinks }) {
  const navigate = useNavigate();
  const [links, setLinks] = useState([]);
  const [draggedItem, setDraggedItem] = useState(null);
  const [dragOverGroup, setDragOverGroup] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  const [newFeatureName, setNewFeatureName] = useState('');
  const [newLinkUrl, setNewLinkUrl] = useState('');
  const [newLinkLabel, setNewLinkLabel] = useState('');
  const [flashMsg, setFlashMsg] = useState(null);
  const [showGrabModal, setShowGrabModal] = useState(false);
  const [grabUrl, setGrabUrl] = useState('');
  const [grabLoading, setGrabLoading] = useState(false);
  const [grabError, setGrabError] = useState(null);
  const [elapsed, setElapsed] = useState(0);
  const [timerId, setTimerId] = useState(null);
  const [linksError, setLinksError] = useState(null);
  const [isGrabbing, setIsGrabbing] = useState(false);
  const [groups, setGroups] = useState([]);
  const [mainUrl, setMainUrl] = useState(null);
  const [baseUrl, setBaseUrl] = useState(null);
  

  useEffect(() => {
    if (!activeProjectId) return;
    setLinksError(null);
    fetch(`http://127.0.0.1:8000/get-project-base-url/${activeProjectId}`)
      .then(res => {
        if (!res.ok) throw new Error('Failed to fetch links');
        return res.json();
      })
      .then(data => {
        setBaseUrl(data.main_url || null);
        setGrabUrl(data.main_url)
      })
      .catch(() => {
        setBaseUrl([]);
        setLinksError('Could not load links. Please try again.');
       ;
      });
  }, [activeProjectId]);

  

  // Groups with their assigned link IDs
  // Fetch groups when activeProjectId changes
  useEffect(() => {
    if (activeProjectId) {
      fetchGroupsFromBackend();
    }
  }, [activeProjectId]);
  

  // Mock data for demonstration
  useEffect(() => {
    fetchProjectLinks();
  }, [activeProjectId]);

  function getFirstPathSegment(url) {
    try {
      const { pathname } = new URL(url);         // e.g., "/dashboard/page"
      // const segments = pathname.split("/").filter(Boolean); // ["dashboard", "page"]
      return pathname || null;                // just "dashboard"
    } catch {
      return null; // return null on invalid URL
    }
  }

  const fetchProjectLinks = async () => {
    if (!activeProjectId) {
      setLinks([]);
      return;
    }
  
    try {
      const response = await fetch(`http://localhost:8000/api/projects/${activeProjectId}/links`);
      if (!response.ok) throw new Error("Failed to fetch links");
  
      const data = await response.json();
  
      const formattedLinks = data.map(link => ({
        id: link.id,
        label: link.url ? getFirstPathSegment(link.url) : link.url,
        url: link.url,
        type: 'link',
        feature_id: link.feature_id || null,
      }));
  
      // Assign ungrouped links to `links` state
      const ungroupedLinks = formattedLinks.filter(link => !link.feature_id);
      setLinks(ungroupedLinks);
  
      // Group by feature_id
      const groupedByFeature = {};
      formattedLinks.forEach(link => {
        if (link.feature_id) {
          if (!groupedByFeature[link.feature_id]) {
            groupedByFeature[link.feature_id] = [];
          }
          groupedByFeature[link.feature_id].push(link);
        }
      });
  
      // Merge grouped links into groups
      setGroups(prev =>
        prev.map(group => ({
          ...group,
          links: groupedByFeature[group.feature_id] || []
        }))
      );
  
    } catch (error) {
      console.error("Error fetching project links:", error);
      setLinks([]);
    }
  };
  

  // Get link object by ID
  const getLinkById = (id) => {
    // First check in unassigned links
    const unassignedLink = links.find(link => link.id === id);
    if (unassignedLink) return unassignedLink;
    
    // Then check in assigned links from all groups
    for (let group of groups) {
      const assignedLink = group.links.find(link => link.id === id);
      if (assignedLink) return assignedLink;
    }
    return null;
  };

  // Get unassigned links (not in any group)
  const getUnassignedLinks = () => {
    const assignedLinkIds = groups.flatMap(group => group.links.map(link => link.id));
    return links.filter(link => !assignedLinkIds.includes(link.id));
  };

  // Drag handlers
  const handleDragStart = (e, link) => {
    setDraggedItem(link);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', e.target);
  };

  const handleDragOver = (e, groupId) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverGroup(groupId);
  };

  const handleDragLeave = (e, groupId) => {
    // Only remove drag over state if we're actually leaving the group container
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragOverGroup(null);
    }
  };

  const handleDrop = (e, groupId) => {
    e.preventDefault();
    setDragOverGroup(null);
    
    if (!draggedItem) return;

    // Check if item is already in this group
    const targetGroup = groups.find(g => g.id === groupId);
    if (targetGroup.links.some(link => link.id === draggedItem.id)) {
      setDraggedItem(null);
      return;
    }

    // Remove from other groups first
    setGroups(prev => prev.map(group => ({
      ...group,
      links: group.links.filter(link => link.id !== draggedItem.id)
    })));

    // Add to target group
    setGroups(prev => prev.map(group => 
      group.id === groupId 
        ? { ...group, links: [...group.links, draggedItem] }
        : group
    ));

    setDraggedItem(null);
  };

  const handleDragEnd = () => {
    setDraggedItem(null);
    setDragOverGroup(null);
  };

  // Remove item from group
  const confirmDeleteLink = async (groupId, linkId) => {
    setFlashMsg(null);
    try {
      const response = await fetch(`http://localhost:8000/api/groups/${linkId}/links.delete`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok) {
        setGroups(prev =>
          prev.map(group =>
            group.id === groupId
              ? { ...group, links: group.links.filter(link => link.id !== linkId) }
              : group
          )
        );
        setFlashMsg({ type: "success", text: result.message || "Link deleted successfully." });
      } else {
        throw new Error(result.detail || "Failed to delete the link.");
      }
    } catch (error) {
      setFlashMsg({ type: "error", text: error.message });
    }
  };

  const removeFromGroup = (groupId, linkId) => {
    const link = getLinkById(linkId);
    if (!link) return;

    setFlashMsg({
      type: 'warning',
      text: (
        <>
          Are you sure you want to delete <b>{link.label}</b>?
          <div className="mt-2 d-flex gap-2 justify-content-end">
            <button
              className="btn btn-sm btn-outline-secondary"
              onClick={() => setFlashMsg(null)}
            >
              Cancel
            </button>
            <button
              className="btn btn-sm btn-danger"
              onClick={() => confirmDeleteLink(groupId, linkId)}
            >
              Delete
            </button>
          </div>
        </>
      )
    });
  };
  

  // Move item between groups
  const moveToGroup = (fromGroupId, toGroupId, linkId) => {
    if (fromGroupId === toGroupId) return;
    
    const linkToMove = groups.find(g => g.id === fromGroupId)?.links.find(l => l.id === linkId);
    if (!linkToMove) return;

    // Remove from source group and add to target group
    setGroups(prev => prev.map(group => {
      if (group.id === fromGroupId) {
        return { ...group, links: group.links.filter(link => link.id !== linkId) };
      } else if (group.id === toGroupId) {
        return { ...group, links: [...group.links, linkToMove] };
      }
      return group;
    }));
  };

  // Handle dropdown selection for unassigned links
  const handleAssignToGroup = (linkId, selectedGroupId) => {
    if (!selectedGroupId) return;
   
    const linkToMove = getLinkById(linkId);
    if (!linkToMove) return;
 
    // Add to selected group
    setGroups(prev => prev.map(group =>
      group.id === selectedGroupId
        ? { ...group, links: [...group.links, linkToMove] }
        : group
    ));
  };

  const unassignedLinks = getUnassignedLinks();

  const createFeatureInBackend = async (featureName) => {
    try {
      const response = await fetch("http://localhost:8000/api/features", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          project_id: activeProjectId,
          feature_name: featureName,
          description: ""
        })
      });

      if (!response.ok) {
        const error = await response.json();
        alert("Failed to add feature: " + error.detail);
        return null;
      }

      const data = await response.json();
      return {
        id: data.feature_id, // Use feature_id as the main id
        feature_id: data.feature_id,
        name: data.feature_name,
        color: 'secondary',
        links: []
      };
    } catch (error) {
      console.error("Error creating feature:", error);
      return null;
    }
  };
  // Add New Feature handler
  const handleAddFeature = async () => {
    if (!newFeatureName.trim()) return;

    const newFeature = await createFeatureInBackend(newFeatureName);
    if (newFeature) {
      setGroups(prev => [...prev, newFeature]);
      setNewFeatureName('');
      setShowModal(false);
    }
  };


  const startTimer = () => {
    const id = setInterval(() => {
      setElapsed(prev => prev + 1);
    }, 1000);
    setTimerId(id);
  };
  
  const stopTimer = () => {
    if (timerId) {
      clearInterval(timerId);
      setTimerId(null);
    }
  };

  // Add Link handler (backend integration)
  const handleAddLink = async () => {
    if (!newLinkUrl.trim()) {
      setFlashMsg({ type: 'warning', text: 'Link URL is required.' });
      return;
    }
    setFlashMsg(null);
    try {
      const response = await fetch('http://127.0.0.1:8000/projectlinks/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          project_id: activeProjectId,
          url: newLinkUrl.trim(),
          page_title: newLinkLabel || 'Link Name/Page Name'
        })
      });
  
      if (response.status === 200) {
        const data = await response.json();
        setFlashMsg({ type: 'warning', text: data.detail?.message || 'Link already exists.' });
        return;
      }
  
      if (!response.ok) {
        const data = await response.json();
        setFlashMsg({ type: 'danger', text: data.detail || 'Failed to add link.' });
        return;
      }
  
      const data = await response.json();
      setFlashMsg({ type: 'success', text: 'Link added successfully!' });
      setShowModal(false);                 // ✅ Close modal
      setNewLinkUrl('');                   // ✅ Clear input
      setNewLinkLabel('');
      fetchProjectLinks();                // ✅ Refresh links, triggers UI update
    } catch (error) {
      setFlashMsg({ type: 'danger', text: 'Error adding link.' });
      console.error(error);
    }
  };

  const handleGrabSubmit = async () => {
    console.log("Starting grab process...");
    
    if (!grabUrl.trim()) {
      setFlashMsg({ type: 'warning', text: 'Please enter a URL.' });
      return;
    }

    // Initialize grab process
    setGrabLoading(true);
    setIsGrabbing(true);
    setGrabError(null);
    setElapsed(0);
    
    // Show initial flash message
    setFlashMsg({ 
      type: 'info', 
      text: `🔄 Starting to grab content from:\n${grabUrl}`
    });
    
    // Start timer
    startTimer();

    try {
      const res = await fetch(`http://127.0.0.1:8000/create_profile/${activeProjectId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: grabUrl })
      });
      
      // Stop timer and grabbing state
      stopTimer();
      setIsGrabbing(false);
      setGrabLoading(false);
      
      if (res.ok) {
        setFlashMsg({
          type: 'success',
          text: `🎉 Success! Content grabbed from:\n${grabUrl}\nCompleted in ${elapsed}s.`
        });
        setShowModal(false);
        setGrabUrl('');
        
        // Refetch links for the current project
        fetch(`http://127.0.0.1:8000/api/links/${activeProjectId}/urls`)
          .then(res => {
            if (!res.ok) throw new Error('Failed to fetch links');
            return res.json();
          })
          .then(data => {
            if (Array.isArray(data.urls)) {
              const reversed = [...data.urls].reverse();
              setLinks(
                reversed.map((item, idx) => ({
                  label: `Link ${idx + 1}`,
                  url: item.url,
                  id: item.link_id
                }))
              );
              setLinksError(null);
              if (reversed.length > 0) {
                setProjectLinks(reversed[0].link_id);
              }
            } else {
              setLinks([]);
              setLinksError('No links found.');
            }
          })
          .catch(() => {
            setLinks([]);
            setLinksError('Could not load links. Please try again.');
          });
      } else {
        const data = await res.json();
        setFlashMsg({ 
          type: 'danger', 
          text: `❌ Failed to grab content after ${elapsed}s:\n${data.detail || 'Unknown error occurred'}`
        });
      }
    } catch (err) {
      stopTimer();
      setIsGrabbing(false);
      setGrabLoading(false);
      setFlashMsg({ 
        type: 'danger', 
        text: `❌ Network error after ${elapsed}s:\n${err.message || 'Connection failed'}`
      });
    }
  };

  const fetchGroupsFromBackend = async () => {
    try {
      const response = await fetch(`http://localhost:8000/api/projects/${activeProjectId}/features`);
      if (!response.ok) throw new Error("Failed to fetch groups");

      const data = await response.json();
      console.log("Fetched features from backend:", data); // Debug log
      
      const formattedGroups = data.map((feature, index) => ({
        id: feature.feature_id,
        feature_id: feature.feature_id,
        name: feature.feature_name,
        color: getColorByIndex(index),
        links: [],
      }));

      console.log("Formatted groups:", formattedGroups); // Debug log
      setGroups(formattedGroups);
    } catch (error) {
      console.error("Error fetching groups:", error);
    }
  };

  const getColorByIndex = (i) => {
    const colors = ['primary', 'warning', 'success', 'info', 'danger'];
    return colors[i % colors.length];
  };
  
  // Clear flashMsg when modal closes or opens for a new link
  useEffect(() => {
    if (!showModal) {
      setFlashMsg(null);
      // Clean up timer if modal is closed during grabbing
      if (isGrabbing) {
        stopTimer();
        setIsGrabbing(false);
        setGrabLoading(false);
      }
    }
  }, [showModal]);

  // Cleanup timer on component unmount
  useEffect(() => {
    return () => {
      if (timerId) {
        clearInterval(timerId);
      }
    };
  }, [timerId]);

  // Save assignments to backend
  const saveAssignmentsToBackend = async () => {
    try {
      // Collect all assignments from groups
      const assignments = [];
      groups.forEach(group => {
        group.links.forEach(link => {
          const linkId = link.id;
          const featureId = group.feature_id || group.id;
       
          if (linkId && featureId) {
            assignments.push({ link_id: linkId, feature_id: featureId });
          } else {
            console.warn("Skipping invalid assignment:", { linkId, featureId });
          }
        });
      });

      if (assignments.length === 0) {
        console.log("No assignments to save");
        return true; // No assignments, but not an error
      }

      console.log("Saving assignments:", assignments);

      const response = await fetch('http://127.0.0.1:8000/api/links/assign-features', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          assignments: assignments
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to save assignments');
      }

      const result = await response.json();
      console.log("Assignments saved successfully:", result);
      return true;
    } catch (error) {
      console.error("Error saving assignments:", error);
      setFlashMsg({ 
        type: 'danger', 
        text: `❌ Failed to save assignments: ${error.message}` 
      });
      return false;
    }
  };

  // Navigate to QA Spec page
  const handleContinueToQASpec = async () => {
    // Show loading state
    setFlashMsg({ 
      type: 'info', 
      text: '💾 Saving assignments...' 
    });

    const saveSuccess = await saveAssignmentsToBackend();
    
    if (saveSuccess) {
      setFlashMsg({ 
        type: 'success', 
        text: '✅ Assignments saved successfully!' 
      });
      
      // Navigate after a brief delay to show success message
      setTimeout(() => {
        navigate('/qa-spec');
      }, 1500);
    }
    // If save failed, flash message is already set in saveAssignmentsToBackend
  };
  
  // Close flash message handler
  const handleCloseFlashMsg = () => {
    setFlashMsg(null);
    if (isGrabbing) {
      stopTimer();
      setIsGrabbing(false);
      setGrabLoading(false);
    }
  };

  // Modal content
  const renderModalContent = () => {
    if (modalType === 'feature') {
      return (
        <form>
          <label htmlFor="featureName" className='mb-2 text-dark'>Feature Name:</label>
          <input
            type="text"
            className="form-control mb-3 p-3"
            placeholder="Enter feature name..."
            value={newFeatureName}
            onChange={e => setNewFeatureName(e.target.value)}
          />
          <div className="modal-footer">
            <button type="button" className="btn btn-dark me-auto" onClick={handleAddFeature}>
              Add Feature
            </button>
          </div>
        </form>
      );
    }
    if (modalType === 'link') {
      return (
        <form>
          <FlashMessage 
            flashMsg={flashMsg} 
            isGrabbing={isGrabbing}
            elapsed={elapsed}
            onClose={handleCloseFlashMsg}
          />
          <label htmlFor="linkUrl" className='mt-2 mb-2 text-dark'>Link URL:</label>
          <input
            type="url"
            className="form-control mb-3 p-3"
            placeholder="Enter link URL..."
            value={newLinkUrl}
            onChange={e => setNewLinkUrl(e.target.value)}
          />
          <div className="modal-footer">
            <button type="button" className="btn btn-dark me-auto" onClick={handleAddLink}>
              Add Link
            </button>
          </div>
        </form>
      );
    }
    if (modalType === 'grab') {
      return (
        <form>
          <FlashMessage 
            flashMsg={flashMsg} 
            isGrabbing={isGrabbing}
            elapsed={elapsed}
            onClose={handleCloseFlashMsg}
          />
          <label htmlFor="grabUrl" className='mb-2 text-dark'>URL to grab:</label>
          <input
            type="text"
            className="form-control mb-2 p-3"
            placeholder="Enter URL"
            value={grabUrl}
            onChange={e => setGrabUrl(e.target.value)}
            disabled={grabLoading}
          />
          <div className="modal-footer">
            <button 
              type="button" 
              className="btn btn-dark me-auto" 
              onClick={handleGrabSubmit}
              disabled={grabLoading}
            >
              {grabLoading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Grabbing...
                </>
              ) : (
                'Grab Links'
              )}
            </button>
          </div>
        </form>
      );
    }
    return null;
  };

  

  return (
    <div className="container-fluid min-vh-100 bg-white border-start border-end border-bottom border-2">
      {/* Global Flash Message - Outside Modal */}
      {flashMsg && !showModal && (
        <div className="position-fixed top-0 start-50 translate-middle-x" style={{ zIndex: 9999, marginTop: '20px', width: '90%', maxWidth: '500px' }}>
          <FlashMessage 
            flashMsg={flashMsg} 
            isGrabbing={isGrabbing}
            elapsed={elapsed}
            onClose={handleCloseFlashMsg}
          />
        </div>
      )}

      {/* Modal */}
      <Modal
        show={showModal}
        onClose={() => setShowModal(false)}
        title={
          modalType === 'feature' ? 'Add New Feature' :
          modalType === 'link' ? 'Add Link' :
          modalType === 'grab' ? 'Grab Links' : ''
        }
      >
        {renderModalContent()}
      </Modal>

      {/* Header */}
      <div className="row">
        <div className="col-12 col-lg-4 d-flex align-items-center gap-3 mb-3 mb-lg-0 border-end border-bottom border-2 p-2">
          <h4 className="mb-0 fw-bold">Links ({unassignedLinks.length})</h4>
          <button className="btn btn-outline-secondary btn-sm px-3"
            onClick={() => { setModalType('grab'); setShowModal(true); }}>
            ⚡ Grab
          </button>
          <button className="btn btn-dark btn-sm px-3"
            onClick={() => { setModalType('link'); setShowModal(true); }}>
            + Add Link
          </button>
        </div>
        <div className="col-12 col-lg-8 d-flex justify-content-lg-end gap-3 border-end border-bottom border-2 p-2">
          <h4 className="mb-0 fw-bold d-lg-none">Groups</h4>
          <button className="btn btn-outline-secondary btn-sm px-3"
            onClick={() => { setModalType('feature'); setShowModal(true); }}>
            + Add New Feature
          </button>
          <button className="btn btn-dark btn-sm px-3" onClick={handleContinueToQASpec}>
            ✓ Continue to QA Spec
          </button>
        </div>
      </div>

      <div className="row">
        {/* Links Panel */}
        <div className="col-12 col-lg-4 mb-4 mb-lg-0 border-end border-bottom border-2 pt-2">
          <div className="card border-1 shadow-sm h-100">
            <div className="card-body p-0">
              <div className="p-3 border-bottom bg-light">
                <h6 className="mb-0 fw-bold">{baseUrl}</h6>
              </div>
              <div className="p-3" style={{ maxHeight: '600px', overflowY: 'auto' }}>
                {unassignedLinks.map((link, index) => (
                  <div
                    key={link.id}
                    className={`mb-3 p-3 border rounded-3 cursor-move ${
                      draggedItem?.id === link.id ? 'opacity-50' : 'bg-light'
                    }`}
                    draggable
                    onDragStart={(e) => handleDragStart(e, link)}
                    onDragEnd={handleDragEnd}
                    style={{ cursor: 'grab' }}
                  >
                    <div className="d-flex align-items-start justify-content-between mb-2">
                      <div className="flex-grow-1">
                        <div className="fw-semibold text-dark mb-2">{link.label}</div>
                        <input 
                          type="text" 
                          className="form-control form-control-sm mb-2" 
                          value={link.url} 
                          readOnly 
                        />
                      </div>
                      <div className="ms-2" style={{ fontSize: '18px', color: '#ccc' }}>
                        ⋮⋮
                      </div>
                    </div>
                    
                    <div className="d-flex align-items-center gap-2">
                      <select 
                        className="form-control form-select form-select-sm" 
                        style={{ maxWidth: '280px' }}
                        onChange={(e) => handleAssignToGroup(link.id, parseInt(e.target.value))}
                        value=""
                      >
                        <option value="">Assign to group</option>
                        {groups.map(group => (
                          <option key={group.id} value={group.id}>{group.name}</option>
                        ))}
                      </select>
                      
                    </div>
                  </div>
                ))}
                
                {unassignedLinks.length === 0 && (
                  <div className="text-center text-muted py-5">
                    <div className="mb-2">📁</div>
                    <div>All links have been assigned to groups</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Groups Panel */}
        <div className="col-12 col-lg-8 pt-2 border-bottom border-2 pb-2">
          <div className="card border-1 shadow-sm h-100">
            <div className="card-body p-0">
              <div className="p-3 border-bottom  d-none d-lg-block bg-light as">
                <h6 className="mb-0 fw-bold">Groups</h6>
              </div>
              <div className="p-3" style={{ maxHeight: '600px', overflowY: 'auto' }}>
                {groups.map(group => (
                  <fieldset 
                    key={group.id}
                    className="border rounded-3 p-3 mb-4"
                    style={{ 
                      borderColor: '#dee2e6',
                      borderWidth: '2px',
                      
                    }}
                  >
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <div 
                        className={`badge px-3 py-2 rounded-pill fs-6 ${
                          group.color === 'primary' ? 'bg-primary' :
                          group.color === 'warning' ? 'bg-warning text-dark' :
                          group.color === 'success' ? 'bg-success' : 'bg-secondary'
                        }`}
                        style={{ 
                          border: 'none',
                          marginBottom: '0',
                          fontSize: '0.875rem'
                        }}
                      >
                        {group.name}
                      </div>
                      <span className="text-muted small">
                        {group.links.length} links
                      </span>
                    </div>

                    <div
                      className={`p-2 border-2 rounded-4 transition-all ${
                        dragOverGroup === group.id 
                          ? 'border-primary bg-primary bg-opacity-10' 
                          : 'border-light'
                      }`}
                      onDragOver={(e) => handleDragOver(e, group.id)}
                      onDragLeave={(e) => handleDragLeave(e, group.id)}
                      onDrop={(e) => handleDrop(e, group.id)}
                      style={{ minHeight: '120px' }}
                    >
                      {/* Group Content */}
                      {group.links.length === 0 ? (
                        <div 
                          className={`text-center py-4 border-2 border-dashed rounded-3 ${
                            dragOverGroup === group.id 
                              ? 'border-primary text-primary' 
                              : 'border-light text-muted'
                          }`}
                        >
                          <div className="mb-2" style={{ fontSize: '24px' }}>
                            {dragOverGroup === group.id ? '⬇️' : '📥'}
                          </div>
                          <div>
                            {dragOverGroup === group.id 
                              ? 'Drop link here' 
                              : 'Drag links here to group them'
                            }
                          </div>
                        </div>
                      ) : (
                        <>
                          {group.links.map((link, index) => (
                            <div key={link.id} className="mb-3 p-3 bg-light border rounded-3 shadow-sm">
                              <div className="fw-semibold text-dark mb-2">{link.label}</div>
                              <div className="d-flex align-items-center gap-2">
                                <input 
                                  type="text" 
                                  className="form-control form-control-sm"
                                  value={link.url}
                                  readOnly
                                  style={{ flex: 1 }}
                                />
                                <select 
                                  className="form-select form-select-sm" 
                                  style={{ maxWidth: '140px' }}
                                  value={group.id}
                                  onChange={(e) => {
                                    const newGroupId = parseInt(e.target.value);
                                    if (newGroupId !== group.id) {
                                      moveToGroup(group.id, newGroupId, link.id);
                                    }
                                  }}
                                >
                                  <option value="">Move to group</option>
                                  {groups.map(g => (
                                    <option key={g.id} value={g.id}>
                                      {g.name}
                                    </option>
                                  ))}
                                </select>
                                <button 
                                  className="btn btn-outline-danger btn-sm"
                                  onClick={() => removeFromGroup(group.id, link.id)}
                                >
                                  <i className="bi bi-trash"></i>
                                </button>
                              </div>
                            </div>
                          ))}
                          
                          {/* Drop zone for additional items */}
                          <div 
                            className={`text-center py-3 border-2 border-dashed rounded-3 mt-3 ${
                              dragOverGroup === group.id 
                                ? 'border-primary text-primary bg-primary bg-opacity-5' 
                                : 'border-light text-muted'
                            }`}
                          >
                            <div style={{ fontSize: '16px' }}>
                              {dragOverGroup === group.id ? '⬇️ Drop here' : '+ Drop more links here'}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </fieldset>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DragDropLinks;