import asyncio
from dotenv import load_dotenv
load_dotenv()
from browser_use import Agent,BrowserSession
from browser_use.llm import ChatOpenAI
import sys
import os
import subprocess
import re
import argparse
browser_session = BrowserSession(
    headless=False,
    viewport={'width': 964, 'height': 647},
    user_data_dir='profile',
)
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, ".."))  # one folder up
qa_spec_path = os.path.join(parent_dir, "QASPEC", "qa_spec.txt")



def get_url(testcase,url):
    base_url = url
    # testcase = """1. Navigate to /csa/add. 2. Enter minimum and maximum boundary values: Symbol: 5 chars "abcde"; Location: 3 chars "abc"; Address: 100 chars "A1234567890..." (100 chars total); Phone Number: 7 chars "1234567"; Email: "<EMAIL>"; Area of Responsibility: "Area1"; Region: select "Central"; FOC Name: 100 chars "FOCNameMaxLen100Chars_1234567890..." (100 chars total); FOC Email: "<EMAIL>"; FOC Phone: 20 chars "12345678901234567890". 3. All fields are mandatory. 4. Submit the form. 5. Verify successful submission with toast message and redirect to /csa."""

    # i want to extract the url from the testcase
    # Match full URLs (http/https) and relative URLs (starting with slash)
    url_pattern = r'(https?://[^\s,")]+|(?<!\w)/[^\s,")]+)'
    urls = re.findall(url_pattern, testcase)
    print("url:", urls)


    NAVIGATION_FLOW_PROMPT = f"""
    You are a Website Navigation Agent.

    Your mission is to:
    - Start from this base URL: {base_url}
    - Use the test case below to figure out where to navigate and what the goal is
    - Simulate how a user would complete the journey starting from the base URL
    - Document every intermediate step, interaction, modal, form, and action

    --- Targeted URL ---
    {urls}

    --- YOUR TASK ---
    1. Determine the destination page and path based on the test case.
    2. Simulate how a user would navigate to that page from the base URL.
      - If the link is direct, explain how such links are normally triggered from the main page.
      - If buttons/menus need to be clicked, document the full step-by-step path.
    3. Scroll each page fully.
    4. Open all modals, dialogs, and tabbed sections involved in the test case flow.
    5. List every visible field name in tab order — no summaries or grouping.
    6. Submit the form if required by the test case and observe:
      - Confirmation message or toast
      - Redirects
    7. Save everything to navigation.md inside `navigation_url/browseruse_agent_data/`.

    --- OUTPUT FORMAT ---
    === NAVIGATION FLOW ANALYSIS ===  
    - **Navigation Path Taken**:  
      - [Step-by-step path from base to form page]

    - **Form Fields** (listed in order):  
      - [Field 1]  
      - [Field 2]  
      - ...
    """
    return NAVIGATION_FLOW_PROMPT


#gpt-4.1-nano
#gpt-4.1-mini
#o4-mini-2025-04-16

llm= ChatOpenAI(model="o4-mini-2025-04-16", temperature=1)

def main(testcase,url):
    agent1 = Agent(
        name="NavigationAgent",
        task=get_url(testcase,url),
        llm=llm,
        browser_session=browser_session,
        file_system_path="navigation_url",
        message_context="""This module analyzes user journeys required to fulfill a test case scenario.
        It determines how to reach the target form, what to fill, and how to submit. never ido any testing just capture the flow of navigation""",
        extend_system_message="""From the base page, follow any visible paths or interactions needed to reach the form described in the test case.
        Document every modal, field, and outcome as a structured navigation log, saved to navigation.md. 
        Never skip steps or assume direct links unless explicitly present on UI.""",
    )
    
    # Convert async call to sync by running in event loop
    response = asyncio.run(agent1.run(max_steps=15))
    
    print("Total Duration")
    print(response.total_duration_seconds())
    if( not response.is_successful()):
      history = response.final_result()
      stripped_history = history.split("navigation.md:")[0].strip()
      print("stripped_history",stripped_history)
      return stripped_history
    else:
      return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--testcase", type=str, required=True)
    parser.add_argument("--url", type=str, required=True)

    args = parser.parse_args()
    
    result = main(args.testcase, args.url)
    if result:
        print(result)  # Send to stdout

    # if response.is_successful():
    #   pattern = r'\*\*Navigation Path Taken\*\*:(.*?)(?=- \*\*|\Z)'
    #   history = response.final_result()
    #   match = re.search(pattern, history, re.DOTALL)
    #   if match:
    #       navigation_section = match.group(1).strip()
    #       print("asdadsadsadsadddddddddddddddddddddddddddddddddddddd",navigation_section)
    #       return f"- **Navigation Path Taken**:\n{navigation_section}"
    #   return None
    # else:
    #   return None


# Call the sync main function directly
# main()