[{"D:\\React\\MCP REACT\\mcp-react\\src\\index.js": "1", "D:\\React\\MCP REACT\\mcp-react\\src\\App.js": "2", "D:\\React\\MCP REACT\\mcp-react\\src\\Components\\Navbar\\Sidebar\\Sidebar.js": "3", "D:\\React\\MCP REACT\\mcp-react\\src\\Components\\Navbar\\MainTopNav\\MainTopNav.js": "4", "D:\\React\\MCP REACT\\mcp-react\\src\\Components\\ProjectDetails\\ProjectDetails.js": "5", "D:\\React\\MCP REACT\\mcp-react\\src\\Components\\Navbar\\ProjactTabsNav\\ProjectTabsNav.js": "6", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\index.js": "7", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\App.js": "8", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectDetails\\ProjectDetails.js": "9", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\MainTopNav\\MainTopNav.js": "10", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\ProjactTabsNav\\ProjectTabsNav.js": "11", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\Sidebar\\Sidebar.js": "12", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\History\\ChatHistory.js": "13", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectChat\\ProjectChat.js": "14", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\index.js": "15", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\App.js": "16", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\History\\ChatHistory.js": "17", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectChat\\ProjectChat.js": "18", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectDetails\\ProjectDetails.js": "19", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\Sidebar\\Sidebar.js": "20", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\MainTopNav\\MainTopNav.js": "21", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\ProjactTabsNav\\ProjectTabsNav.js": "22", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\AddFiles\\AddFiles.js": "23", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectDetails\\TestSteps.js": "24", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectDetails\\Links.js": "25", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\FeatureMerge\\FeatureMerge.js": "26", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\AddFiles\\MarkdownEditor.js": "27", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\AddFiles\\EditMarkdownFile.js": "28", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\JsonReports\\JsonReports.js": "29", "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\FeatureMerge\\QaSpec\\QaSpec.js": "30"}, {"size": 673, "mtime": 1749026817543, "results": "31", "hashOfConfig": "32"}, {"size": 1879, "mtime": 1749037400997, "results": "33", "hashOfConfig": "32"}, {"size": 5364, "mtime": 1749553899315, "results": "34", "hashOfConfig": "32"}, {"size": 1883, "mtime": 1749553274078, "results": "35", "hashOfConfig": "32"}, {"size": 11318, "mtime": 1749551228654, "results": "36", "hashOfConfig": "32"}, {"size": 1416, "mtime": 1749039841413, "results": "37", "hashOfConfig": "32"}, {"size": 673, "mtime": 1749026817543, "results": "38", "hashOfConfig": "39"}, {"size": 2450, "mtime": 1750327644707, "results": "40", "hashOfConfig": "39"}, {"size": 37512, "mtime": 1750671574296, "results": "41", "hashOfConfig": "39"}, {"size": 2905, "mtime": 1750171357817, "results": "42", "hashOfConfig": "39"}, {"size": 1444, "mtime": 1749739761924, "results": "43", "hashOfConfig": "39"}, {"size": 21603, "mtime": 1750671574296, "results": "44", "hashOfConfig": "39"}, {"size": 4632, "mtime": 1750143211879, "results": "45", "hashOfConfig": "39"}, {"size": 17439, "mtime": 1750402669365, "results": "46", "hashOfConfig": "39"}, {"size": 692, "mtime": 1751018993938, "results": "47", "hashOfConfig": "48"}, {"size": 45019, "mtime": 1754045767929, "results": "49", "hashOfConfig": "48"}, {"size": 4668, "mtime": 1753956807048, "results": "50", "hashOfConfig": "48"}, {"size": 58875, "mtime": 1751019083533, "results": "51", "hashOfConfig": "48"}, {"size": 1834, "mtime": 1754045758120, "results": "52", "hashOfConfig": "48"}, {"size": 68527, "mtime": 1753958539273, "results": "53", "hashOfConfig": "48"}, {"size": 3098, "mtime": 1753958690219, "results": "54", "hashOfConfig": "48"}, {"size": 1628, "mtime": 1753958629731, "results": "55", "hashOfConfig": "48"}, {"size": 61557, "mtime": 1753958458403, "results": "56", "hashOfConfig": "48"}, {"size": 58157, "mtime": 1753777469519, "results": "57", "hashOfConfig": "48"}, {"size": 16915, "mtime": 1753787253491, "results": "58", "hashOfConfig": "48"}, {"size": 76283, "mtime": 1754050672053, "results": "59", "hashOfConfig": "48"}, {"size": 1261, "mtime": 1753691416035, "results": "60", "hashOfConfig": "48"}, {"size": 50894, "mtime": 1753707239793, "results": "61", "hashOfConfig": "48"}, {"size": 48389, "mtime": 1753956792872, "results": "62", "hashOfConfig": "48"}, {"size": 49420, "mtime": 1754038771236, "results": "63", "hashOfConfig": "48"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "crh80t", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gsrgjw", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kwb5c7", {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\React\\MCP REACT\\mcp-react\\src\\index.js", [], [], "D:\\React\\MCP REACT\\mcp-react\\src\\App.js", [], [], "D:\\React\\MCP REACT\\mcp-react\\src\\Components\\Navbar\\Sidebar\\Sidebar.js", ["154", "155", "156"], [], "D:\\React\\MCP REACT\\mcp-react\\src\\Components\\Navbar\\MainTopNav\\MainTopNav.js", [], [], "D:\\React\\MCP REACT\\mcp-react\\src\\Components\\ProjectDetails\\ProjectDetails.js", ["157"], [], "D:\\React\\MCP REACT\\mcp-react\\src\\Components\\Navbar\\ProjactTabsNav\\ProjectTabsNav.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectDetails\\ProjectDetails.js", ["158", "159"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\MainTopNav\\MainTopNav.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\ProjactTabsNav\\ProjectTabsNav.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\Sidebar\\Sidebar.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\History\\ChatHistory.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectChat\\ProjectChat.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\App.js", [], ["160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172"], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\History\\ChatHistory.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectChat\\ProjectChat.js", [], ["173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185"], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectDetails\\ProjectDetails.js", ["186"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\Sidebar\\Sidebar.js", [], ["187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "200"], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\MainTopNav\\MainTopNav.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\Navbar\\ProjactTabsNav\\ProjectTabsNav.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\AddFiles\\AddFiles.js", [], ["201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212"], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectDetails\\TestSteps.js", [], ["213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225"], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\ProjectDetails\\Links.js", ["226", "227"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\FeatureMerge\\FeatureMerge.js", ["228", "229", "230", "231", "232", "233", "234", "235"], ["236", "237", "238", "239", "240", "241", "242", "243", "244", "245", "246", "247", "248", "249"], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\AddFiles\\MarkdownEditor.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\AddFiles\\EditMarkdownFile.js", [], ["250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261"], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\JsonReports\\JsonReports.js", [], ["262", "263", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274"], "C:\\Users\\<USER>\\Documents\\GitHub\\MCP-Project-FullStack-local\\MCP-Project-FullStack\\mcp-react\\src\\Components\\FeatureMerge\\QaSpec\\QaSpec.js", [], ["275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287"], {"ruleId": "288", "severity": 1, "message": "289", "line": 68, "column": 13, "nodeType": "290", "endLine": 68, "endColumn": 82}, {"ruleId": "288", "severity": 1, "message": "289", "line": 74, "column": 13, "nodeType": "290", "endLine": 78, "endColumn": 14}, {"ruleId": "291", "severity": 1, "message": "292", "line": 104, "column": 11, "nodeType": "290", "endLine": 104, "endColumn": 93}, {"ruleId": "293", "severity": 1, "message": "294", "line": 69, "column": 9, "nodeType": "295", "messageId": "296", "endLine": 69, "endColumn": 25}, {"ruleId": "293", "severity": 1, "message": "297", "line": 22, "column": 12, "nodeType": "295", "messageId": "296", "endLine": 22, "endColumn": 24}, {"ruleId": "293", "severity": 1, "message": "298", "line": 432, "column": 11, "nodeType": "295", "messageId": "296", "endLine": 432, "endColumn": 24}, {"ruleId": "299", "severity": 1, "message": "300", "line": 69, "column": 100, "nodeType": "295", "messageId": "301", "endLine": 69, "endColumn": 104, "suppressions": "302"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 69, "column": 141, "nodeType": "295", "messageId": "301", "endLine": 69, "endColumn": 145, "suppressions": "303"}, {"ruleId": "293", "severity": 1, "message": "304", "line": 69, "column": 40633, "nodeType": "295", "messageId": "296", "endLine": 69, "endColumn": 40638, "suppressions": "305"}, {"ruleId": "293", "severity": 1, "message": "306", "line": 69, "column": 40767, "nodeType": "295", "messageId": "296", "endLine": 69, "endColumn": 40772, "suppressions": "307"}, {"ruleId": "293", "severity": 1, "message": "308", "line": 69, "column": 40901, "nodeType": "295", "messageId": "296", "endLine": 69, "endColumn": 40906, "suppressions": "309"}, {"ruleId": "293", "severity": 1, "message": "310", "line": 69, "column": 41011, "nodeType": "295", "messageId": "296", "endLine": 69, "endColumn": 41016, "suppressions": "311"}, {"ruleId": "312", "message": "313", "line": 69, "column": 41111, "endLine": 69, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "314"}, {"ruleId": "315", "message": "316", "line": 69, "column": 41111, "endLine": 69, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "317"}, {"ruleId": "318", "message": "319", "line": 69, "column": 41111, "endLine": 69, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "320"}, {"ruleId": "321", "message": "322", "line": 69, "column": 41111, "endLine": 69, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "323"}, {"ruleId": "324", "message": "325", "line": 69, "column": 41111, "endLine": 69, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "326"}, {"ruleId": "327", "message": "328", "line": 69, "column": 41111, "endLine": 69, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "329"}, {"ruleId": "330", "message": "331", "line": 69, "column": 41111, "endLine": 69, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "332"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 561, "column": 100, "nodeType": "295", "messageId": "301", "endLine": 561, "endColumn": 104, "suppressions": "333"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 561, "column": 141, "nodeType": "295", "messageId": "301", "endLine": 561, "endColumn": 145, "suppressions": "334"}, {"ruleId": "293", "severity": 1, "message": "304", "line": 561, "column": 40633, "nodeType": "295", "messageId": "296", "endLine": 561, "endColumn": 40638, "suppressions": "335"}, {"ruleId": "293", "severity": 1, "message": "306", "line": 561, "column": 40767, "nodeType": "295", "messageId": "296", "endLine": 561, "endColumn": 40772, "suppressions": "336"}, {"ruleId": "293", "severity": 1, "message": "308", "line": 561, "column": 40901, "nodeType": "295", "messageId": "296", "endLine": 561, "endColumn": 40906, "suppressions": "337"}, {"ruleId": "293", "severity": 1, "message": "310", "line": 561, "column": 41011, "nodeType": "295", "messageId": "296", "endLine": 561, "endColumn": 41016, "suppressions": "338"}, {"ruleId": "312", "message": "313", "line": 561, "column": 41111, "endLine": 561, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "339"}, {"ruleId": "315", "message": "316", "line": 561, "column": 41111, "endLine": 561, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "340"}, {"ruleId": "318", "message": "319", "line": 561, "column": 41111, "endLine": 561, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "341"}, {"ruleId": "321", "message": "322", "line": 561, "column": 41111, "endLine": 561, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "342"}, {"ruleId": "324", "message": "325", "line": 561, "column": 41111, "endLine": 561, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "343"}, {"ruleId": "327", "message": "328", "line": 561, "column": 41111, "endLine": 561, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "344"}, {"ruleId": "330", "message": "331", "line": 561, "column": 41111, "endLine": 561, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "345"}, {"ruleId": "293", "severity": 1, "message": "346", "line": 9, "column": 9, "nodeType": "295", "messageId": "296", "endLine": 9, "endColumn": 23}, {"ruleId": "293", "severity": 1, "message": "347", "line": 81, "column": 9, "nodeType": "295", "messageId": "296", "endLine": 81, "endColumn": 26, "suppressions": "348"}, {"ruleId": "293", "severity": 1, "message": "349", "line": 156, "column": 9, "nodeType": "295", "messageId": "296", "endLine": 156, "endColumn": 30, "suppressions": "350"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 746, "column": 100, "nodeType": "295", "messageId": "301", "endLine": 746, "endColumn": 104, "suppressions": "351"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 746, "column": 141, "nodeType": "295", "messageId": "301", "endLine": 746, "endColumn": 145, "suppressions": "352"}, {"ruleId": "293", "severity": 1, "message": "304", "line": 746, "column": 40633, "nodeType": "295", "messageId": "296", "endLine": 746, "endColumn": 40638, "suppressions": "353"}, {"ruleId": "293", "severity": 1, "message": "308", "line": 746, "column": 40901, "nodeType": "295", "messageId": "296", "endLine": 746, "endColumn": 40906, "suppressions": "354"}, {"ruleId": "293", "severity": 1, "message": "310", "line": 746, "column": 41011, "nodeType": "295", "messageId": "296", "endLine": 746, "endColumn": 41016, "suppressions": "355"}, {"ruleId": "312", "message": "313", "line": 746, "column": 41111, "endLine": 746, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "356"}, {"ruleId": "315", "message": "316", "line": 746, "column": 41111, "endLine": 746, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "357"}, {"ruleId": "318", "message": "319", "line": 746, "column": 41111, "endLine": 746, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "358"}, {"ruleId": "321", "message": "322", "line": 746, "column": 41111, "endLine": 746, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "359"}, {"ruleId": "324", "message": "325", "line": 746, "column": 41111, "endLine": 746, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "360"}, {"ruleId": "327", "message": "328", "line": 746, "column": 41111, "endLine": 746, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "361"}, {"ruleId": "330", "message": "331", "line": 746, "column": 41111, "endLine": 746, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "362"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 469, "column": 100, "nodeType": "295", "messageId": "301", "endLine": 469, "endColumn": 104, "suppressions": "363"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 469, "column": 141, "nodeType": "295", "messageId": "301", "endLine": 469, "endColumn": 145, "suppressions": "364"}, {"ruleId": "293", "severity": 1, "message": "304", "line": 469, "column": 40633, "nodeType": "295", "messageId": "296", "endLine": 469, "endColumn": 40638, "suppressions": "365"}, {"ruleId": "293", "severity": 1, "message": "308", "line": 469, "column": 40901, "nodeType": "295", "messageId": "296", "endLine": 469, "endColumn": 40906, "suppressions": "366"}, {"ruleId": "293", "severity": 1, "message": "310", "line": 469, "column": 41011, "nodeType": "295", "messageId": "296", "endLine": 469, "endColumn": 41016, "suppressions": "367"}, {"ruleId": "312", "message": "313", "line": 469, "column": 41111, "endLine": 469, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "368"}, {"ruleId": "315", "message": "316", "line": 469, "column": 41111, "endLine": 469, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "369"}, {"ruleId": "318", "message": "319", "line": 469, "column": 41111, "endLine": 469, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "370"}, {"ruleId": "321", "message": "322", "line": 469, "column": 41111, "endLine": 469, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "371"}, {"ruleId": "324", "message": "325", "line": 469, "column": 41111, "endLine": 469, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "372"}, {"ruleId": "327", "message": "328", "line": 469, "column": 41111, "endLine": 469, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "373"}, {"ruleId": "330", "message": "331", "line": 469, "column": 41111, "endLine": 469, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "374"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 419, "column": 100, "nodeType": "295", "messageId": "301", "endLine": 419, "endColumn": 104, "suppressions": "375"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 419, "column": 141, "nodeType": "295", "messageId": "301", "endLine": 419, "endColumn": 145, "suppressions": "376"}, {"ruleId": "293", "severity": 1, "message": "304", "line": 419, "column": 40633, "nodeType": "295", "messageId": "296", "endLine": 419, "endColumn": 40638, "suppressions": "377"}, {"ruleId": "293", "severity": 1, "message": "306", "line": 419, "column": 40767, "nodeType": "295", "messageId": "296", "endLine": 419, "endColumn": 40772, "suppressions": "378"}, {"ruleId": "293", "severity": 1, "message": "308", "line": 419, "column": 40901, "nodeType": "295", "messageId": "296", "endLine": 419, "endColumn": 40906, "suppressions": "379"}, {"ruleId": "293", "severity": 1, "message": "310", "line": 419, "column": 41011, "nodeType": "295", "messageId": "296", "endLine": 419, "endColumn": 41016, "suppressions": "380"}, {"ruleId": "312", "message": "313", "line": 419, "column": 41111, "endLine": 419, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "381"}, {"ruleId": "315", "message": "316", "line": 419, "column": 41111, "endLine": 419, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "382"}, {"ruleId": "318", "message": "319", "line": 419, "column": 41111, "endLine": 419, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "383"}, {"ruleId": "321", "message": "322", "line": 419, "column": 41111, "endLine": 419, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "384"}, {"ruleId": "324", "message": "325", "line": 419, "column": 41111, "endLine": 419, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "385"}, {"ruleId": "327", "message": "328", "line": 419, "column": 41111, "endLine": 419, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "386"}, {"ruleId": "330", "message": "331", "line": 419, "column": 41111, "endLine": 419, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "387"}, {"ruleId": "388", "severity": 1, "message": "389", "line": 154, "column": 6, "nodeType": "390", "endLine": 154, "endColumn": 15, "suggestions": "391"}, {"ruleId": "293", "severity": 1, "message": "392", "line": 164, "column": 11, "nodeType": "295", "messageId": "296", "endLine": 164, "endColumn": 21}, {"ruleId": "293", "severity": 1, "message": "393", "line": 91, "column": 10, "nodeType": "295", "messageId": "296", "endLine": 91, "endColumn": 23}, {"ruleId": "293", "severity": 1, "message": "394", "line": 91, "column": 25, "nodeType": "295", "messageId": "296", "endLine": 91, "endColumn": 41}, {"ruleId": "293", "severity": 1, "message": "395", "line": 94, "column": 10, "nodeType": "295", "messageId": "296", "endLine": 94, "endColumn": 19}, {"ruleId": "293", "severity": 1, "message": "396", "line": 97, "column": 10, "nodeType": "295", "messageId": "296", "endLine": 97, "endColumn": 20}, {"ruleId": "293", "severity": 1, "message": "397", "line": 100, "column": 10, "nodeType": "295", "messageId": "296", "endLine": 100, "endColumn": 17}, {"ruleId": "293", "severity": 1, "message": "398", "line": 100, "column": 19, "nodeType": "295", "messageId": "296", "endLine": 100, "endColumn": 29}, {"ruleId": "388", "severity": 1, "message": "399", "line": 131, "column": 6, "nodeType": "390", "endLine": 131, "endColumn": 23, "suggestions": "400"}, {"ruleId": "388", "severity": 1, "message": "401", "line": 137, "column": 6, "nodeType": "390", "endLine": 137, "endColumn": 23, "suggestions": "402"}, {"ruleId": "293", "severity": 1, "message": "403", "line": 458, "column": 13, "nodeType": "295", "messageId": "296", "endLine": 458, "endColumn": 17, "suppressions": "404"}, {"ruleId": "388", "severity": 1, "message": "405", "line": 599, "column": 6, "nodeType": "390", "endLine": 599, "endColumn": 17, "suggestions": "406", "suppressions": "407"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 1037, "column": 100, "nodeType": "295", "messageId": "301", "endLine": 1037, "endColumn": 104, "suppressions": "408"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 1037, "column": 141, "nodeType": "295", "messageId": "301", "endLine": 1037, "endColumn": 145, "suppressions": "409"}, {"ruleId": "293", "severity": 1, "message": "304", "line": 1037, "column": 40633, "nodeType": "295", "messageId": "296", "endLine": 1037, "endColumn": 40638, "suppressions": "410"}, {"ruleId": "293", "severity": 1, "message": "308", "line": 1037, "column": 40901, "nodeType": "295", "messageId": "296", "endLine": 1037, "endColumn": 40906, "suppressions": "411"}, {"ruleId": "293", "severity": 1, "message": "310", "line": 1037, "column": 41011, "nodeType": "295", "messageId": "296", "endLine": 1037, "endColumn": 41016, "suppressions": "412"}, {"ruleId": "312", "message": "313", "line": 1037, "column": 41111, "endLine": 1037, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "413"}, {"ruleId": "315", "message": "316", "line": 1037, "column": 41111, "endLine": 1037, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "414"}, {"ruleId": "318", "message": "319", "line": 1037, "column": 41111, "endLine": 1037, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "415"}, {"ruleId": "321", "message": "322", "line": 1037, "column": 41111, "endLine": 1037, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "416"}, {"ruleId": "324", "message": "325", "line": 1037, "column": 41111, "endLine": 1037, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "417"}, {"ruleId": "327", "message": "328", "line": 1037, "column": 41111, "endLine": 1037, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "418"}, {"ruleId": "330", "message": "331", "line": 1037, "column": 41111, "endLine": 1037, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "419"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 242, "column": 100, "nodeType": "295", "messageId": "301", "endLine": 242, "endColumn": 104, "suppressions": "420"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 242, "column": 141, "nodeType": "295", "messageId": "301", "endLine": 242, "endColumn": 145, "suppressions": "421"}, {"ruleId": "293", "severity": 1, "message": "304", "line": 242, "column": 40633, "nodeType": "295", "messageId": "296", "endLine": 242, "endColumn": 40638, "suppressions": "422"}, {"ruleId": "293", "severity": 1, "message": "308", "line": 242, "column": 40901, "nodeType": "295", "messageId": "296", "endLine": 242, "endColumn": 40906, "suppressions": "423"}, {"ruleId": "293", "severity": 1, "message": "310", "line": 242, "column": 41011, "nodeType": "295", "messageId": "296", "endLine": 242, "endColumn": 41016, "suppressions": "424"}, {"ruleId": "312", "message": "313", "line": 242, "column": 41111, "endLine": 242, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "425"}, {"ruleId": "315", "message": "316", "line": 242, "column": 41111, "endLine": 242, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "426"}, {"ruleId": "318", "message": "319", "line": 242, "column": 41111, "endLine": 242, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "427"}, {"ruleId": "321", "message": "322", "line": 242, "column": 41111, "endLine": 242, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "428"}, {"ruleId": "324", "message": "325", "line": 242, "column": 41111, "endLine": 242, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "429"}, {"ruleId": "327", "message": "328", "line": 242, "column": 41111, "endLine": 242, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "430"}, {"ruleId": "330", "message": "331", "line": 242, "column": 41111, "endLine": 242, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "431"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 170, "column": 100, "nodeType": "295", "messageId": "301", "endLine": 170, "endColumn": 104, "suppressions": "432"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 170, "column": 141, "nodeType": "295", "messageId": "301", "endLine": 170, "endColumn": 145, "suppressions": "433"}, {"ruleId": "293", "severity": 1, "message": "434", "line": 170, "column": 40501, "nodeType": "295", "messageId": "296", "endLine": 170, "endColumn": 40506, "suppressions": "435"}, {"ruleId": "293", "severity": 1, "message": "304", "line": 170, "column": 40633, "nodeType": "295", "messageId": "296", "endLine": 170, "endColumn": 40638, "suppressions": "436"}, {"ruleId": "293", "severity": 1, "message": "308", "line": 170, "column": 40901, "nodeType": "295", "messageId": "296", "endLine": 170, "endColumn": 40906, "suppressions": "437"}, {"ruleId": "293", "severity": 1, "message": "310", "line": 170, "column": 41011, "nodeType": "295", "messageId": "296", "endLine": 170, "endColumn": 41016, "suppressions": "438"}, {"ruleId": "312", "message": "313", "line": 170, "column": 41111, "endLine": 170, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "439"}, {"ruleId": "315", "message": "316", "line": 170, "column": 41111, "endLine": 170, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "440"}, {"ruleId": "318", "message": "319", "line": 170, "column": 41111, "endLine": 170, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "441"}, {"ruleId": "321", "message": "322", "line": 170, "column": 41111, "endLine": 170, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "442"}, {"ruleId": "324", "message": "325", "line": 170, "column": 41111, "endLine": 170, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "443"}, {"ruleId": "327", "message": "328", "line": 170, "column": 41111, "endLine": 170, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "444"}, {"ruleId": "330", "message": "331", "line": 170, "column": 41111, "endLine": 170, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "445"}, {"ruleId": "388", "severity": 1, "message": "446", "line": 61, "column": 6, "nodeType": "390", "endLine": 61, "endColumn": 23, "suggestions": "447", "suppressions": "448"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 207, "column": 100, "nodeType": "295", "messageId": "301", "endLine": 207, "endColumn": 104, "suppressions": "449"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 207, "column": 141, "nodeType": "295", "messageId": "301", "endLine": 207, "endColumn": 145, "suppressions": "450"}, {"ruleId": "293", "severity": 1, "message": "304", "line": 207, "column": 40633, "nodeType": "295", "messageId": "296", "endLine": 207, "endColumn": 40638, "suppressions": "451"}, {"ruleId": "293", "severity": 1, "message": "308", "line": 207, "column": 40901, "nodeType": "295", "messageId": "296", "endLine": 207, "endColumn": 40906, "suppressions": "452"}, {"ruleId": "293", "severity": 1, "message": "310", "line": 207, "column": 41011, "nodeType": "295", "messageId": "296", "endLine": 207, "endColumn": 41016, "suppressions": "453"}, {"ruleId": "312", "message": "313", "line": 207, "column": 41111, "endLine": 207, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "454"}, {"ruleId": "315", "message": "316", "line": 207, "column": 41111, "endLine": 207, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "455"}, {"ruleId": "318", "message": "319", "line": 207, "column": 41111, "endLine": 207, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "456"}, {"ruleId": "321", "message": "322", "line": 207, "column": 41111, "endLine": 207, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "457"}, {"ruleId": "324", "message": "325", "line": 207, "column": 41111, "endLine": 207, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "458"}, {"ruleId": "327", "message": "328", "line": 207, "column": 41111, "endLine": 207, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "459"}, {"ruleId": "330", "message": "331", "line": 207, "column": 41111, "endLine": 207, "endColumn": 41378, "severity": 2, "nodeType": null, "suppressions": "460"}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "jsx-a11y/no-redundant-roles", "The element button has an implicit role of button. Defining this explicitly is redundant and should be avoided.", "no-unused-vars", "'handleDeleteStep' is assigned a value but never used.", "Identifier", "unusedVar", "'editLinkName' is assigned a value but never used.", "'openEditModal' is assigned a value but never used.", "no-eval", "eval can be harmful.", "unexpected", ["461", "462"], ["463", "464"], "'oo_tr' is defined but never used.", ["465", "466"], "'oo_tx' is defined but never used.", ["467", "468"], "'oo_ts' is defined but never used.", ["469", "470"], "'oo_te' is defined but never used.", ["471", "472"], "unicorn/no-abusive-eslint-disable", "Definition for rule 'unicorn/no-abusive-eslint-disable' was not found.", ["473", "474"], "eslint-comments/disable-enable-pair", "Definition for rule 'eslint-comments/disable-enable-pair' was not found.", ["475", "476"], "eslint-comments/no-unlimited-disable", "Definition for rule 'eslint-comments/no-unlimited-disable' was not found.", ["477", "478"], "eslint-comments/no-aggregating-enable", "Definition for rule 'eslint-comments/no-aggregating-enable' was not found.", ["479", "480"], "eslint-comments/no-duplicate-disable", "Definition for rule 'eslint-comments/no-duplicate-disable' was not found.", ["481", "482"], "eslint-comments/no-unused-disable", "Definition for rule 'eslint-comments/no-unused-disable' was not found.", ["483", "484"], "eslint-comments/no-unused-enable", "Definition for rule 'eslint-comments/no-unused-enable' was not found.", ["485", "486"], ["487", "488"], ["489", "490"], ["491", "492"], ["493", "494"], ["495", "496"], ["497", "498"], ["499", "500"], ["501", "502"], ["503", "504"], ["505", "506"], ["507", "508"], ["509", "510"], ["511", "512"], "'selectedLinkId' is assigned a value but never used.", "'handleInputChange' is assigned a value but never used.", ["513", "514", "515", "516", "517", "518", "519", "520", "521"], "'handleEditInputChange' is assigned a value but never used.", ["522", "523", "524", "525", "526", "527", "528", "529", "530"], ["531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545"], ["546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560"], ["561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575"], ["576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590"], ["591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605"], ["606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620"], ["621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635"], ["636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650"], ["651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665"], ["666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680"], ["681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695"], ["696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710"], ["711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730"], ["731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750"], ["751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770"], ["771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790"], ["791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810"], ["811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830"], ["831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850"], ["851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870"], ["871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890"], ["891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910"], ["911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930"], ["931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950"], ["951", "952", "953", "954"], ["955", "956", "957", "958"], ["959", "960", "961", "962"], ["963", "964", "965", "966"], ["967", "968", "969", "970"], ["971", "972", "973", "974"], ["975", "976", "977", "978"], ["979", "980", "981", "982"], ["983", "984", "985", "986"], ["987", "988", "989", "990"], ["991", "992", "993", "994"], ["995", "996", "997", "998"], ["999", "1000", "1001", "1002"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'grabUrl', 'setFlashMsg', and 'timerId'. Either include them or remove the dependency array. If 'setFlashMsg' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["1003"], "'closeModal' is assigned a value but never used.", "'showGrabModal' is assigned a value but never used.", "'setShowGrabModal' is assigned a value but never used.", "'grabError' is assigned a value but never used.", "'linksError' is assigned a value but never used.", "'mainUrl' is assigned a value but never used.", "'setMainUrl' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchGroupsFromBackend'. Either include it or remove the dependency array.", ["1004"], "React Hook useEffect has a missing dependency: 'fetchProjectLinks'. Either include it or remove the dependency array.", ["1005"], "'data' is assigned a value but never used.", ["1006", "1007"], "React Hook useEffect has missing dependencies: 'isGrabbing' and 'stopTimer'. Either include them or remove the dependency array.", ["1008"], ["1009", "1010", "1011", "1012", "1013", "1014", "1015"], ["1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027"], ["1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039"], ["1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051"], ["1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063"], ["1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075"], ["1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087"], ["1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099"], ["1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111"], ["1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123"], ["1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135"], ["1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147"], ["1148", "1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159"], ["1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176"], ["1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193"], ["1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210"], ["1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227"], ["1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244"], ["1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261"], ["1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278"], ["1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295"], ["1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312"], ["1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329"], ["1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346"], ["1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363"], ["1364", "1365", "1366"], ["1367", "1368", "1369"], "'oo_oo' is defined but never used.", ["1370", "1371", "1372"], ["1373", "1374", "1375"], ["1376", "1377", "1378"], ["1379", "1380", "1381"], ["1382", "1383", "1384"], ["1385", "1386", "1387"], ["1388", "1389", "1390"], ["1391", "1392", "1393"], ["1394", "1395", "1396"], ["1397", "1398", "1399"], ["1400", "1401", "1402"], "React Hook useEffect has a missing dependency: 'handleGroupClick'. Either include it or remove the dependency array.", ["1403"], ["1404", "1405"], ["1406", "1407", "1408", "1409", "1410", "1411", "1412"], ["1413", "1414", "1415", "1416", "1417", "1418", "1419"], ["1420", "1421", "1422", "1423", "1424", "1425", "1426"], ["1427", "1428", "1429", "1430", "1431", "1432", "1433"], ["1434", "1435", "1436", "1437", "1438", "1439", "1440"], ["1441", "1442", "1443", "1444", "1445", "1446", "1447"], ["1448", "1449", "1450", "1451", "1452", "1453", "1454"], ["1455", "1456", "1457", "1458", "1459", "1460", "1461"], ["1462", "1463", "1464", "1465", "1466", "1467", "1468"], ["1469", "1470", "1471", "1472", "1473", "1474", "1475"], ["1476", "1477", "1478", "1479", "1480", "1481", "1482"], ["1483", "1484", "1485", "1486", "1487", "1488", "1489"], {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"desc": "1492", "fix": "1493"}, {"desc": "1494", "fix": "1495"}, {"desc": "1496", "fix": "1497"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"desc": "1498", "fix": "1499"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"desc": "1500", "fix": "1501"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, {"kind": "1490", "justification": "1491"}, "directive", "", "Update the dependencies array to be: [elapsed, grabUrl, setFlashMsg, timerId]", {"range": "1502", "text": "1503"}, "Update the dependencies array to be: [activeProjectId, fetchGroupsFromBackend]", {"range": "1504", "text": "1505"}, "Update the dependencies array to be: [activeProjectId, fetchProjectLinks]", {"range": "1506", "text": "1507"}, "Update the dependencies array to be: [isGrabbing, showModal, stopTimer]", {"range": "1508", "text": "1509"}, "Update the dependencies array to be: [activeProjectId, handleGroupClick]", {"range": "1510", "text": "1511"}, [5141, 5150], "[elapsed, grabUrl, setFlashMsg, timerId]", [4249, 4266], "[activeProjectId, fetchGroupsFromBackend]", [4356, 4373], "[activeProjectId, fetchProjectLinks]", [18174, 18185], "[isGrabbing, showModal, stopTimer]", [2340, 2357], "[activeProjectId, handleGroupClick]"]