import uuid
from datetime import datetime
import psycopg2
import os 
from db_connection import get_db_connection

# --- Database connection helper ---

# --- Fetch project_id from name and Feature name ---
def get_project_and_feature_ids(project_name: str, feature_name: str):
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT p.project_id, f.feature_id
                    FROM Projects p
                    JOIN Features f ON p.project_id = f.project_id
                    WHERE p.project_name = %s AND f.feature_name = %s
                    LIMIT 1
                """, (project_name, feature_name))

                result = cur.fetchone()

                if result:
                    return result[0], result[1]  # project_id, feature_id
                else:
                    raise ValueError(f"No match for project '{project_name}' and feature '{feature_name}'")

    except Exception as e:
        raise RuntimeError(f"Database error: {e}")

# --- Create a new test run entry ---
def insert_test_run(conn, project_id, run_name=None):
    run_id = str(uuid.uuid4())
    if not run_name:
        run_name = f"Run {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    with conn.cursor() as cur:
        cur.execute("""
            INSERT INTO TestRuns (run_id, project_id, run_name, run_started_at)
            VALUES (%s, %s, %s, %s)
        """, (run_id, project_id, run_name, datetime.now()))
    return run_id

def start_test_run(project_name, feature_name):
    project_id, feature_id = get_project_and_feature_ids(project_name, feature_name)

    run_id = str(uuid.uuid4())
    run_started_at = datetime.utcnow()

    conn = get_db_connection()
    cur = conn.cursor()

    cur.execute("""
        INSERT INTO TestRuns (run_id, project_id, run_name, run_started_at)
        VALUES (%s, %s, %s, %s)
        RETURNING run_id
    """, (run_id, project_id, f"Run {run_started_at.strftime('%Y-%m-%d %H:%M:%S')}", run_started_at))
    
    returned_run_id = cur.fetchone()[0]  
    conn.commit()

    return returned_run_id


def update_test_run_end_time(run_id):
    conn = get_db_connection()
    cur = conn.cursor()

    cur.execute("""
        UPDATE TestRuns
        SET run_ended_at = %s
        WHERE run_id = %s
    """, (datetime.now(), run_id))

    conn.commit()
    